<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>YS QATool使用帮助</title>
<link href="source/api.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="source/jquery.min.js"></script>
<script language="javascript" src="source/jquery.dimensions.js"></script>
</head>
<body>
<div class="tit">
	<div id="titcont">
    	SQATool使用帮助<span class="sma"> Author:<EMAIL></span>
    </div>
</div>
<div id="cont">
<div class='fun'>
	<div class='lineface'>一、概述 </div>
	<span>#.<em>支持工具说明</em></span>
	<span class='ri'></span>
	<div class='says'>
		    			脚本文件使用ini格式进行控制，下文会对各个参数进行描述。<br>
    				  当前版本，支持的工具列表如下：<br>
                      AS SSD Benchmark V2.0<br>
                      ATTO Disk Benchmark<br>
                      ATTO4.0<br>
                      CrystalDiskMark6_0_2<br>
                      h2testw_1.4<br>
                      HD Tune Pro<br>
                      IOmeter<br>
    </div>
</div>
<div class='fun'>
	<div class='lineface'>二、工具参数说明</div>
    <a name="AS_SSD"></a>
    <span class='le'><em>AS_SSD</em> <b>版本:AS SSD Benchmark V2.0</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 0-1G， 1-3G， 2-5G，3-10G，缺省值：1G
TEST_CAP = 0

; 是否测试Seq项，0 不测试，1 测试，缺省值：1
TEST_SEQ = 1

; 是否测试4K项，0 不测试，1 测试，缺省值：1
TEST_4K = 1

; 是否测试4K-64Thrd项， 0 不测试，1 测试，缺省值：1
TEST_4K_64Thrd = 1

; 是否测试Acc.Time， 0 不测试，1 测试，缺省值：1
TEST_ACC_TIME = 1 
</pre>
    </div>


    <a name="ATTO Disk Benchmark"></a>
    <span class='le'>#.<em>ATTO Disk Benchmark</em> <b>版本:ATTO Disk Benchmark3.05</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 最小的测试大小，要求使用符合工具列表指定字符串，否则工具无法修改该值,缺省值：512 B
TRANSFER_SIZE_MIN = 512 B

; 最大的测试大小，要求使用符合工具列表指定字符串，否则工具无法修改该值，缺省值：64 MB
TRANSFER_SIZE_MAX = 64 MB

; 测试总长度，要求使用符合工具列表指定字符串，否则工具无法修改该值，缺省值：256 MB
TOTAL_LENGTH = 256 MB

; 是否选中[Force Write Access]， 1 是， 0 否，缺省值：1
FORCE_WRITE_ACCESS = 1

; 是否选中[Direct IO]，1 是， 0 否，缺省值：1
DIRECT_IO = 1

; 测试模式 0 I/O Comparison, 1 Overlapped I/O, 2 Neither，缺省值：1
MODE = 1 

; 数据pattern，仅当MODE = 0 时有效。
; 0:00000000, 1:FFFFFFFF, 2:00FF00FF, 3:FF00FF00, 4:AA55AA55, 
; 5:55AA55AA, 6:33CC33CC, 7:CC33CC33, 8:66996699, 9:99669966, 
; 10:Increment, 11:Decrement, 12:Random
TEST_PATTERN=0

; Run Continuously,仅当MODE=0时有效
RUN_CONTINUOUSLY = 0

; 仅当RUN_CONTINUOUSLY=1时有效，缺省值：30
RUN_TIME = 30

; 队列深度，仅当MODE=1时有效
QUEUE_DEEP = 4
</pre>
    </div> 


    <a name="BurnInTest"></a>
    <span class='le'><em>BurnInTest</em> <b>版本:BurnInTest(TM)V8.1Pro(1017)</b></span>
    <span class='ri'>类型:<em> 安装工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
;测试圈数, 0 means run forever
TEST_CYCLE = 0

;测试分钟, 0 means run forever, set 100 when it's above 100.
TEST_MINIUTES = 1

;Select the tests to perform and the load of each test (1 = Minimum load, 100 = Maximum load)
TEST_LOAD = 100

;测试模式
;0: Default (Cyclic)
;1:Sequential data pattern (0,1,2...255)
;2:Random data with random seeking
;3:High Low freq data overwrite (10101 then 00001)
;4:Butterfly seeking
;5:Binary data pattern 1 (10101010)
;6:Binary data pattern 2 (01010101)
;7:Zeros data pattern (00000000)
;8:Ones data pattern (11111111)
;9:Random data pattern
;10:User defined test pattern
;11:Quick physical drive test, 非系统物理盘专用
;12:Physical drive read test, 物理盘专用
TEST_MODE = 0

; 文件大小
TEST_FILE_SIZE = 1.00

; 块大小,需要使用工具指定内容，否则该项失效
TEST_BLOCK_SIZE = 4096

TEST_SEEK_COUNT = 100

; NA, No Threshold warning, 单位：MB/Sec
SLOW_DRIVE_THRESHOLD = NA

; Duty cycle override, leave blank to accept default
DUTY_CYCLE_OVERRIDE = 

; Run self test and log SMART errors, 1 YES, 0 NO
RUN_SELF_TEST = 0

; Log bad sector increase, 1 YSE, 0 NO
BAD_SECTOR_INCREASE = 0

; Bad sector threshold
BAD_SECTOR_THRESHOLD = 20
</pre>
    </div>

    <a name="CrystalDiskMark"></a>
    <span class='le'><em>CrystalDiskMark</em> <b>版本:CrystalDiskMark6_0_2</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 0 全部测试模式 1-4 单项测试模式，缺省值：0
TEST_MODE = 0

; 测试圈数，1-9，缺省值：1
TEST_CIRCLE = 1

; 测试容量 0 50M, 1 100M, 2 500M, 3 1G, 4 2G, 5 4G, 6 8G, 7 16G, 8 32G， 缺省值：3
TEST_CAP = 3
</pre>
    </div>


    <a name="h2test"></a>
    <span class='le'><em>h2test</em> <b>版本:h2testw_1.4</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 是否指定容量测试，1 是，0 否（否：满容测试）,缺省：0
FIXED_VOLUME = 1

; 是否按照百分比测试，仅当FIX_VOLUME = 1,且TEST_MODE=1时有效，缺省：100
TEST_PERCENT = 100

; 测试模式，0 verify，1 write&verify，缺省：1
TEST_MODE = 1

; 是否循环校验, 0 否 1 循环校验，缺省：0
ENDLESS_VERIFY = 0

; 测试时间，单位：分钟，仅当ENDLESS_VERIFY=1时有效，0 表示不停止，缺省值：0
TEST_MINUTE = 0

; 测试结束后，删除已创建文件个数的百分比，100，全部删除，0不删除，缺省值：0
DELETE_FILE_PERCENT = 100
</pre>
    </div>
    
    <a name="HD Tune Pro"></a>
    <span class='le'><em>HD Tune Pro</em> <b>版本:HDTunePro_Win8_10</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 测试模式，0 读取，1 写入，缺省：0
TEST_MODE = 0

; 是否选择快捷行程，0 不选择，1 选择，缺省值：0
FAST_TRIP = 0

; 测试容量，仅当FAST_TRIP = 1时有效，缺省值：1
FAST_TRIP_GB = 1

; 是否测试传输速率，1 是， 0 否，缺省值：1
TRANS_RATE = 1

; 是否测试存取时间, 1 是，0 否，缺省值：1
ACC_TIME = 1

; 是否测试突发传输速率， 1 是， 0 否，缺省值：1
SUDDEN_TRANS_RATE = 1
</pre>
    </div>

    <a name="IOmeter"></a>
    <span class='le'><em>IOmeter</em> <b>版本:IOmeter1.0</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 0 IOmeter中全部脚本都测试 1-512K顺序写测试，2-512K顺序读测试，3-4K随机写测试，4-4K随机读测试，缺省值：0
TEST_MODE = 0

; 测试时间，单位分钟，默认5
RUN_TIME = 5

; 延迟时间，单位秒，默认5
RAMPUP_TIME = 5

; 最大测试容量大小，单位sector，默认0-代表全盘
MAXIMUM_DISK_SIZE = 0
</pre>
    </div>
    
	<div class='lineface'>三、其他脚本说明</div>

	  <a name="删除分区"></a>
    <span class='le'><em>删除分区</em> <b>版本:无</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
仅需要指定类型，无须配置参数
</pre>
    </div>

	  <a name="格式化分区"></a>
    <span class='le'><em>格式化分区</em> <b>版本:无</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; 0表示，全部格式化，其他为格式化分区大小，单位：MB，缺省值：0
SIZE = 0
; 可支持类型：ntfs，fat32，exfat(须指定系统支持的格式化类型，否则可能失败)
FORMAT = ntfs
</pre>
    </div>

	  <a name="空闲等待"></a>
    <span class='le'><em>空闲等待</em> <b>版本:无</b></span>
    <span class='ri'>类型:<em> 自带工具</em></span>
    <div class='says'>参数说明：无须其它参数</div>
    <div class='says'>示例参数说明如下：
<pre class="intersays">
; Idle的时间，单位：分钟，缺省：0
TIME_MIN = 1

; Idle的时间，单位：秒，缺省：0
TIME_SEC = 0
</pre>
    </div>
    
	<div class='lineface'>四、常规操作</div>
		  <a name="执行用例"></a>
    <span class='le'><em>执行用例</em> <b>版本:无</b></span>
	<img src="run_case.gif" width="900">
	
			  <a name="使用计划用例"></a>
    <span class='le'><em>使用计划用例</em> <b>版本:无</b></span>
	<img src="run_plan.gif" width="900">
	
<div class="info">
    <b>*</b> 本接口文档最后更新时间：2019.10.09<br>
</div>
</div>
<div id="foot">
深圳市得一微电子有限责任公司（YEESTOR Microelectronics Co., Ltd)<br>
</div>


<!--浮动接口导航栏-->
<div id="floatMenu">
  <ul class="menu"></ul>
</div>
<script language="javascript">
var name = "#floatMenu";
var menuYloc = null;
$(document).ready(function(){
    $(".le > em").each(function(index, element){
        $(".menu").append(" <li><a href='#"+ $(this).text() +"'>"+ $(this).text()+"</a></li>");
    });
    menuYloc = parseInt($(name).css("top").substring(0,$(name).css("top").indexOf("px")))
    $(window).scroll(function () { 
        offset = menuYloc+$(document).scrollTop()+"px";
        $(name).animate({top:offset},{duration:500,queue:false});
    });
});
</script>
</body>
</html>

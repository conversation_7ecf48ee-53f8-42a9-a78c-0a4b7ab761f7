﻿<!DOCTYPE html>
<html><head>
   <title>Rebooter by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />

   <!-- This line includes the general project style sheet (not required) -->
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- You can change the fonts, text colors, and styles of your search results with the CSS below -->
   <style type="text/css">
        body { background:#FFF; }
        .navbar { font-size: 120%; }

        .submit { font-size: 9pt; }
        .highlight { background: #FFFF40; }
        .searchheading { font-size: 9pt; font-weight: bold; }
        .summary { font-size: 8pt; font-style: italic; }
        .results { font-size: 9pt; }
        .description { font-size: 9pt; }
        .context { font-size: 9pt; }
        .result_title { font-size: 9pt; }

	       .suggestion { font-size: 100%; }
	       .category { color: #999999; }
	       .sorting { text-align: right; }
	       .zoom_searchform { font-size: 100%; }
	       .zoom_results_per_page { font-size: 80%; margin-left: 10px; }
	       .zoom_match { font-size: 80%; margin-left: 10px;}
	       .zoom_categories { font-size: 80%; }
	       .zoom_categories ul { display: inline; margin: 0px; padding: 0px;}
	       .zoom_categories li { display: inline; margin-left: 15px; list-style-type: none; }
	       input.zoom_button {  }
	       input.zoom_searchbox {  }
	       .result_image { float: left; display: block; }
	       .result_image img { margin: 10px; width: 80px; border: 0px; }
	       .result_block { margin-top: 15px; margin-bottom: 15px; clear: left; }
	       .result_altblock { margin-top: 15px; margin-bottom: 15px; clear: left; }
	       .result_pages { font-size: 100%; }
	       .result_pagescount { font-size: 100%; }
	       .searchtime { font-size: 80%; }
	       .recommended
	       {
		        background: #DFFFBF;
	       		border-top: 1px dotted #808080;
	       		border-bottom: 1px dotted #808080;
	       		margin-top: 15px;
	       		margin-bottom: 15px;
	       }
	       .recommended_heading { float: right; font-weight: bold; }
	       .recommend_block { margin-top: 15px; margin-bottom: 15px; clear: left; }
	       .recommend_title { font-size: 100%; }
	       .recommend_description { font-size: 100%; color: #008000; }
	       .recommend_infoline { font-size: 80%; font-style: normal; color: #808080;}

 	</style>
</head>
<body>
<p class="navbar"><a href="hmcontent.htm">Contents</a>
 | <a href="hmkwindex.htm">Index</a>
 | <b>Search</b>
</p>
<hr/>
<p class="submit">Enter one or more keywords to search ('*' and '?' wildcards are supported):</p>

  <!-- This is where the search form and results will appear -->
  <div id="loadingmsg" align="center"><img src="cicon_loadindex_ani.gif" style="border:none" alt="Loading..." /></div>
<script type="text/javascript" src="settings.js" charset="UTF-8"></script>
<script type="text/javascript" src="zoom_search.js"></script>
<script type="text/javascript">ZoomSearch();</script>


<noscript>
<p>You must have JavaScript enabled to use this version of the search engine.</p>
</noscript>
</body>
</html>

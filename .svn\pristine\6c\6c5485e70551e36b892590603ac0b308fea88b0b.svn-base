import os,sys,logging,traceback,time,shutil,tempfile

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
if len(sys.argv) > 1:
    reportPath = sys.argv[1]

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'PCIE_Report_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)

try:
    from win32com.client import Dispatch
    import requests
    import openpyxl
    from openpyxl.styles import Alignment
    import Performance,PublicFuc,PerformanceDingRong,InnerTest,RebootSleepLaterPerformance
    if len(sys.argv) > 1:
        #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt') 
        jsonData = {}
        jsonData['token'] = 'c55b58b3e6ceddcb7f2348214e4f9e6e004e8f0f24275b76d56d943ab31e6b5b'
        jsonData['secret'] = 'SEC0d5b713c89344135026affa518fd1656379cbeb2dc0a7f653dcbcb749974aadf'
        jsonData['atLst'] = '18312006726'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg' 
        bRmsRun = True
    templateFile = os.path.join(curpath, 'pcie_report_template.xlsx') 
    nanoTempFile = os.path.join(curpath, 'nano_report_template.xlsx')
    competitionPfmTempFile = os.path.join(curpath, 'competition_pfm_report_template.xlsx')
    resultFileName = 'PCIE汇总报告.xlsx'
    resultFile = os.path.join(reportPath, resultFileName)
    errDiskFile = os.path.join(reportPath, 'ErrDisk.txt')
    addpath = os.path.join(reportPath,'ManReport')

    logging.info('程序开始运行！')
    PublicFuc.GetAllFile(reportPath)
    if os.path.exists(templateFile):
        wb = openpyxl.load_workbook(filename = templateFile)
        alignment = Alignment(horizontal='center',vertical='center') 
        PerformanceDingRong.Run(reportPath, wb, alignment)
        Performance.Run(reportPath, wb, alignment)
        InnerTest.Run(reportPath, wb, alignment)
        RebootSleepLaterPerformance.Run(reportPath, wb, alignment)
        wb.save(resultFile)
        if os.path.exists(addpath):
            dst = resultFile
            xl = Dispatch("Excel.Application")
            wb2 = xl.Workbooks.Open(Filename=dst)
            for src_plan in os.listdir(addpath):
                src_plan = os.path.join(addpath,src_plan)
                src_time = os.listdir(src_plan)
                src_time = sorted(src_time,reverse=True)
                src_time = os.path.join(src_plan,src_time[0])
                for src_xls in os.listdir(src_time):
                    src_xls = os.path.join(src_time,src_xls)
                    wb1 = xl.Workbooks.Open(Filename=src_xls)
                    xl.Visible = False  # You can remove this line if you don't want the Excel application to be visible
                    for shes in wb1.sheets: 
                        ws1 = wb1.Worksheets(shes.name)
                        ws1.Copy(wb2.Worksheets[len(wb2.Worksheets)-1])
                    wb1.Close()
            wb2.Close(SaveChanges=True) 
            xl.Quit() 
       
    PublicFuc.WriteErrDiskFile(errDiskFile)
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = 'eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/report/download/?nid=PCIE&key=%s'%enTestNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&file='%('PCIE',enTestNo)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFile):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://gateway.yeestor.com:8789/wo/report/status"
        headers = {'Content-Type': 'application/json','ORDER-NO':enTestNo}
        requests.request("POST", woUrl, headers=headers, data=json.dumps(woDic))
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        jsonData['type'] = 'text'
        jsonData['text'] = 'eReport报告合并异常！@18312006726\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)


﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files\CListCtrl">
      <UniqueIdentifier>{2672fac1-46b6-4c3f-884a-dc610bcdf2ac}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\StatusListBox">
      <UniqueIdentifier>{b1a6829f-2725-476c-9c75-fa32fa03f325}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\CaseAtomicPort">
      <UniqueIdentifier>{91569c7f-51f8-4b28-a6ae-f60d0991dba9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\CfgMgr">
      <UniqueIdentifier>{c4929440-7dba-449c-996f-aea1fb8e645e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\TestUtility">
      <UniqueIdentifier>{85acfd61-2e03-4216-9aba-a3a4a3052430}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Report">
      <UniqueIdentifier>{ad821b72-3f14-4d11-bf0b-e03847ddfb4c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\SleepByTool">
      <UniqueIdentifier>{0afc2c30-913e-45c8-9a71-6ae312494886}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\RebootByTool">
      <UniqueIdentifier>{2f7d371b-6c81-4293-90b5-9383c5914b29}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\FillMaterialNoTool">
      <UniqueIdentifier>{02fbfb44-449a-49d2-8d3c-93e3df083b30}</UniqueIdentifier>
    </Filter>
    <Filter Include="SkynetServiceCtrol">
      <UniqueIdentifier>{88ced2c0-3148-4d18-b47d-2814169399f7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\VTE">
      <UniqueIdentifier>{54b5aafe-f68b-465e-ad28-ce99aadf63ef}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Bit91">
      <UniqueIdentifier>{5abb1ebe-1473-4871-9d03-0b21a462fec9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\SpecialTestSets">
      <UniqueIdentifier>{c14a1558-64a9-4824-bcb2-bfdbaabcd31d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\SpecialTestSets\MultiPartitionTest1">
      <UniqueIdentifier>{0555e863-6d36-4346-ae58-da1c5dd697fe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\SpecialTestSets\BasicToolTest">
      <UniqueIdentifier>{fa170f65-1abb-4aa2-b59d-f26cc06b0d2c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\SpecialTestSets\BasicToolTest\h2test">
      <UniqueIdentifier>{ae75667d-1545-4451-acd7-8d7939719ded}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\SataSpeedDetect">
      <UniqueIdentifier>{4e7d9b3f-d023-403a-8576-a88faf3fd987}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\TemperatureBox">
      <UniqueIdentifier>{8b5da9d1-6615-4da1-aa21-0f91d16185d5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\WSDPowerBox">
      <UniqueIdentifier>{5cb870f3-5a5b-4db1-8c1a-cccdfe0b7946}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\WSDPowerBox\SerialPowerCtrl">
      <UniqueIdentifier>{25fddd25-9bfa-4d36-8812-6745a57d81fd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\pc_sleeper">
      <UniqueIdentifier>{fd49a8f9-88bc-4611-8fe2-e3265255cd15}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
    <None Include="res\AterEx.rc2">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\AterEx.ico">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AterEx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AterExDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CListCtrl\HeaderCtrlCl.h">
      <Filter>Source Files\CListCtrl</Filter>
    </ClInclude>
    <ClInclude Include="CListCtrl\ListCtrlExClEdit.h">
      <Filter>Source Files\CListCtrl</Filter>
    </ClInclude>
    <ClInclude Include="MainDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TestPlanDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TabSheet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PublicDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StatusListBox\StatusListBox.h">
      <Filter>Source Files\StatusListBox</Filter>
    </ClInclude>
    <ClInclude Include="CaseAtomicPort.h">
      <Filter>Source Files\CaseAtomicPort</Filter>
    </ClInclude>
    <ClInclude Include="CaseAtomicPortDef.h">
      <Filter>Source Files\CaseAtomicPort</Filter>
    </ClInclude>
    <ClInclude Include="CFGMgr.h">
      <Filter>Source Files\CfgMgr</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\VideoAutoRecord.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\PowerController.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="ProErrDisk.h">
      <Filter>Source Files\CaseAtomicPort</Filter>
    </ClInclude>
    <ClInclude Include="USBConfigDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Utility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ServerReport.h">
      <Filter>Source Files\Report</Filter>
    </ClInclude>
    <ClInclude Include="UpdateDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tool_base.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SleepByTool.h">
      <Filter>Source Files\SleepByTool</Filter>
    </ClInclude>
    <ClInclude Include="RebootByTool.h">
      <Filter>Source Files\RebootByTool</Filter>
    </ClInclude>
    <ClInclude Include="BitMapButton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FillMaterialNoTool.h">
      <Filter>Source Files\FillMaterialNoTool</Filter>
    </ClInclude>
    <ClInclude Include="SkynetServiceCtrol.h">
      <Filter>SkynetServiceCtrol</Filter>
    </ClInclude>
    <ClInclude Include="CommonInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AutoStatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="VTETest.h">
      <Filter>Source Files\VTE</Filter>
    </ClInclude>
    <ClInclude Include="EMMCConfigDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BurnInTest9_1.h">
      <Filter>Source Files\Bit91</Filter>
    </ClInclude>
    <ClInclude Include="MultiPartitionTest1.h">
      <Filter>Source Files\SpecialTestSets\MultiPartitionTest1</Filter>
    </ClInclude>
    <ClInclude Include="h2test.h">
      <Filter>Source Files\SpecialTestSets\BasicToolTest\h2test</Filter>
    </ClInclude>
    <ClInclude Include="SATAGenManager.h">
      <Filter>Source Files\SataSpeedDetect</Filter>
    </ClInclude>
    <ClInclude Include="SATASpeedDownDetection.h">
      <Filter>Source Files\SataSpeedDetect</Filter>
    </ClInclude>
    <ClInclude Include="SpeedDownDetectionDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DeviceInfoQuery.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ColdStartTest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Format.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\RAID0Format.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="UDPTemperatureBoxCtrl\UDP.h">
      <Filter>Source Files\TemperatureBox</Filter>
    </ClInclude>
    <ClInclude Include="UDPTemperatureBoxCtrl\UDPTemperatureBoxCtrl.h">
      <Filter>Source Files\TemperatureBox</Filter>
    </ClInclude>
    <ClInclude Include="CRC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AutoLock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TemperatureMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\MultiBit9Test.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\MultiIOMonkeyTest.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\SleepWakeUp.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\RAID1Format.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="TestUtility\RAID5Format.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="RaidFormat.h">
      <Filter>Source Files\TestUtility</Filter>
    </ClInclude>
    <ClInclude Include="WSDPowerBox\PowerMgr.h">
      <Filter>Source Files\WSDPowerBox</Filter>
    </ClInclude>
    <ClInclude Include="WSDPowerBox\SerialPowerCtrl\SerialPort.h">
      <Filter>Source Files\WSDPowerBox\SerialPowerCtrl</Filter>
    </ClInclude>
    <ClInclude Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrl.h">
      <Filter>Source Files\WSDPowerBox\SerialPowerCtrl</Filter>
    </ClInclude>
    <ClInclude Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrlWSD.h">
      <Filter>Source Files\WSDPowerBox\SerialPowerCtrl</Filter>
    </ClInclude>
    <ClInclude Include="WSDPowerBox\CRC.h">
      <Filter>Source Files\WSDPowerBox</Filter>
    </ClInclude>
    <ClInclude Include="TestDiskChecker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DialogTransarentNotify.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pc_sleeper.h">
      <Filter>Source Files\pc_sleeper</Filter>
    </ClInclude>
    <ClInclude Include="cpu-z.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AterEx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AterExDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CListCtrl\HeaderCtrlCl.cpp">
      <Filter>Source Files\CListCtrl</Filter>
    </ClCompile>
    <ClCompile Include="CListCtrl\ListCtrlExClEdit.cpp">
      <Filter>Source Files\CListCtrl</Filter>
    </ClCompile>
    <ClCompile Include="MainDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestPlanDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TabSheet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PublicDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StatusListBox\StatusListBox.cpp">
      <Filter>Source Files\StatusListBox</Filter>
    </ClCompile>
    <ClCompile Include="CaseAtomicPort.cpp">
      <Filter>Source Files\CaseAtomicPort</Filter>
    </ClCompile>
    <ClCompile Include="CFGMgr.cpp">
      <Filter>Source Files\CfgMgr</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\VideoAutoRecord.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\PowerController.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="ProErrDisk.cpp">
      <Filter>Source Files\CaseAtomicPort</Filter>
    </ClCompile>
    <ClCompile Include="USBConfigDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ServerReport.cpp">
      <Filter>Source Files\Report</Filter>
    </ClCompile>
    <ClCompile Include="UpdateDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tool_base.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SleepByTool.cpp">
      <Filter>Source Files\SleepByTool</Filter>
    </ClCompile>
    <ClCompile Include="RebootByTool.cpp">
      <Filter>Source Files\RebootByTool</Filter>
    </ClCompile>
    <ClCompile Include="BitMapButton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FillMaterialNoTool.cpp">
      <Filter>Source Files\FillMaterialNoTool</Filter>
    </ClCompile>
    <ClCompile Include="SkynetServiceCtrol.cpp">
      <Filter>SkynetServiceCtrol</Filter>
    </ClCompile>
    <ClCompile Include="CommonInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AutoStatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="VTETest.cpp">
      <Filter>Source Files\VTE</Filter>
    </ClCompile>
    <ClCompile Include="EMMCConfigDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BurnInTest9_1.cpp">
      <Filter>Source Files\Bit91</Filter>
    </ClCompile>
    <ClCompile Include="MultiPartitionTest1.cpp">
      <Filter>Source Files\SpecialTestSets\MultiPartitionTest1</Filter>
    </ClCompile>
    <ClCompile Include="h2test.cpp">
      <Filter>Source Files\SpecialTestSets\BasicToolTest\h2test</Filter>
    </ClCompile>
    <ClCompile Include="SATAGenManager.cpp">
      <Filter>Source Files\SataSpeedDetect</Filter>
    </ClCompile>
    <ClCompile Include="SATASpeedDownDetection.cpp">
      <Filter>Source Files\SataSpeedDetect</Filter>
    </ClCompile>
    <ClCompile Include="SpeedDownDetectionDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DeviceInfoQuery.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ColdStartTest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Format.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\RAID0Format.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="UDPTemperatureBoxCtrl\UDP.cpp">
      <Filter>Source Files\TemperatureBox</Filter>
    </ClCompile>
    <ClCompile Include="UDPTemperatureBoxCtrl\UDPTemperatureBoxCtrl.cpp">
      <Filter>Source Files\TemperatureBox</Filter>
    </ClCompile>
    <ClCompile Include="CRC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AutoLock.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TemperatureMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\MultiBit9Test.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\MultiIOMonkeyTest.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\SleepWakeUp.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\RAID1Format.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="TestUtility\RAID5Format.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="RaidFormat.cpp">
      <Filter>Source Files\TestUtility</Filter>
    </ClCompile>
    <ClCompile Include="WSDPowerBox\PowerMgr.cpp">
      <Filter>Source Files\WSDPowerBox</Filter>
    </ClCompile>
    <ClCompile Include="WSDPowerBox\SerialPowerCtrl\SerialPort.cpp">
      <Filter>Source Files\WSDPowerBox\SerialPowerCtrl</Filter>
    </ClCompile>
    <ClCompile Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrl.cpp">
      <Filter>Source Files\WSDPowerBox\SerialPowerCtrl</Filter>
    </ClCompile>
    <ClCompile Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrlWSD.cpp">
      <Filter>Source Files\WSDPowerBox\SerialPowerCtrl</Filter>
    </ClCompile>
    <ClCompile Include="WSDPowerBox\CRC.cpp">
      <Filter>Source Files\WSDPowerBox</Filter>
    </ClCompile>
    <ClCompile Include="TestDiskChecker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DialogTransarentNotify.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pc_sleeper.cpp">
      <Filter>Source Files\pc_sleeper</Filter>
    </ClCompile>
    <ClCompile Include="cpu-z.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="AterEx.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>
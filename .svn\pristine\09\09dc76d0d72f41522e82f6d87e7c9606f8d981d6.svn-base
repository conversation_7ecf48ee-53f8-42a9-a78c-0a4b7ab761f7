﻿<!DOCTYPE html>
<html>
<head>
   <title>Sleep State Types</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Hibernate,Power State,Sleep" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "hid_sleepstates.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="page-break-after: avoid; margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold; color: #ffffff;">Types of Sleep States</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="hid_overview.htm"><img src="nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="hid_overview.htm"><img src="nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="hid_ui.htm"><img src="nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Power management is an integral feature of most new computers. With laptops and wireless computing on the rise it is especially important to have strict control and reliability when it comes to your systems power states.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Most modern computer systems support at least the following basic power states.</span></p>
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 7px 0px 7px 0px;"><table style="border:none; border-spacing:0px;">
<tr style="text-align:left;vertical-align:top;">
<td style="vertical-align:top; width:129px; padding:0px;"><p style="margin: 7px 0px 7px 0px;"><span style="font-weight: bold; color: #010100;">Power State</span></p>
</td>
<td style="vertical-align:top; width:487px; padding:0px;"><p style="margin: 7px 0px 7px 12px;"><span style="font-weight: bold; color: #010100;">Description</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td style="vertical-align:top; width:129px; padding:0px;"><p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Working</span></p>
</td>
<td style="vertical-align:top; width:487px; padding:0px;"><p style="margin: 7px 0px 7px 12px;"><span style="color: #010100;">The system is fully on. Some devices may independently conserve power if there usage falls below a certain threshold.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td style="vertical-align:top; width:129px; padding:0px;"><p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Sleeping</span></p>
</td>
<td style="vertical-align:top; width:487px; padding:0px;"><p style="margin: 7px 0px 7px 12px;"><span style="color: #010100;">The system seems as though it’s shut down. Power consumption is reduced to one of the three sleep levels (see below). The lower the level, the more power that is conserved, but the longer it takes for the system to wake. Mouse movement or key presses will usually wake the system.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td style="vertical-align:top; width:129px; padding:0px;"><p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Soft Off or Hibernate</span></p>
</td>
<td style="vertical-align:top; width:487px; padding:0px;"><p style="margin: 7px 0px 7px 12px;"><span style="color: #010100;">The system appears to be off. Power consumption is very low. The operating system context is saved out to disk and no longer exists in RAM. Wake up usually results in hardware boot, but not software boot.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td style="vertical-align:top; width:129px; padding:0px;"><p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Mechanical Off</span></p>
</td>
<td style="vertical-align:top; width:487px; padding:0px;"><p style="margin: 7px 0px 7px 12px;"><span style="color: #010100;">No power consumption. A full reboot will be required to start up the system.</span></p>
</td>
</tr>
</table>
</div>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 7px 0px 7px 0px;">Hybrid sleep is where the hibernation file is used in conjunction with sleep states S1-S3 so the system state can be restored if power is lost while sleeping. </p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">ACPI (Advanced Configuration and Power Interface) is an industry wide standard that defines a power management and configuration mechanism for hardware and operating systems. ACPI defines six discrete power states. Lower states consume more power, but have a smaller latency on wake up.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">S0 – Working</span><br />
<span style="color: #010100;">S1 – Sleeping</span><br />
<span style="color: #010100;">S2 – Sleeping</span><br />
<span style="color: #010100;">S3 – Sleeping</span><br />
<span style="color: #010100;">S4 – Hibernate</span><br />
<span style="color: #010100;">S5 – Off</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Sleeper allows the user to put the system into sleep state S1, S2, S3 or S4. To turn off or reboot the computer, see the Rebooter application also available from PassMark.</span><br />
<span style="color: #010100;">Note that not all computer systems will support all power states. It is not unusual for just S1 and S4 to be supported or S1, S3 and S4. Attempting to put a system into an unsupported state results in the next lowest supported state being chosen.</span></p>

</td></tr></table>

</body>
</html>

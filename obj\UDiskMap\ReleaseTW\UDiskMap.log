﻿Build started 2025/7/23 17:33:05.
     1>Project "D:\NewWorkSpace\9-AterPlan11\UDiskMap\UDiskMap.vcxproj" on node 5 (build target(s)).
     1>InitializeBuildStatus:
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.unsuccessfulbuild".
       ClCompile:
         All outputs are up-to-date.
         All outputs are up-to-date.
         All outputs are up-to-date.
       ResourceCompile:
         All outputs are up-to-date.
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\dll\UDiskMap.dll" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib\\" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" UsbPort.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\UDiskMap.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"..\lib\UDiskMap.lib" /MACHINE:X86 /DLL "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\dllmain.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.obj"
            Creating library ..\lib\UDiskMap.lib and object ..\lib\UDiskMap.exp
         Generating code
         Finished generating code
         UDiskMap.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\dll\UDiskMap.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\dll\UDiskMap.dll;#2" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.dll.intermediate.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\UDiskMap\ReleaseTW\UDiskMap.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\UDiskMap\UDiskMap.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:01.17

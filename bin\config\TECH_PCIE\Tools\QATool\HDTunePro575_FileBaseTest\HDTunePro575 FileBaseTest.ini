[TOOL]
; 工具类型：1 AS SSD, 2 CDM, 3 h2test, 4 hd tune, 5 ATTO DISK, 6 BIT
TYPE = 4
PATH = TOOLS\HD Tune Pro v5.75 CN\HDTunePro_Win8_10.exe

[PARAM]
; 测试模式，0 读取，1 写入，缺省：0
TEST_MODE = 0
; 是否选择快捷行程，0 不选择，1 选择，缺省值：0
FAST_TRIP = 0
; 测试容量，仅当FAST_TRIP = 1时有效，缺省值：1
FAST_TRIP_GB = 1
; 是否测试传输速率，1 是， 0 否，缺省值：1
TRANS_RATE = 1
; 是否测试存取时间, 1 是，0 否，缺省值：1
ACC_TIME = 1
; 是否测试突发传输速率， 1 是， 0 否，缺省值：1
SUDDEN_TRANS_RATE = 1

; 是否测试突发传输速率， 0 基准测试，1、文件基准测试
TEST_ITEM = 1
; 是否测试传输速度，0 否，1 是
FILEBASE_TEST_SPEED = 1
; 速度测试的文件大小，单位M，不可过大，否则因为内存问题无法测试，如果为0则自适应按磁盘大小去决策文件长度。
FILEBASE_SPEED_FILELEN = 0
; 速度测试的数据模式，0 全零，1 随机， 2 混合
FILEBASE_SPEED_DATAMODE = 1
; 4K的random测试队列深度，1-64之间的数字，默认32
FILEBASE_SPEED_QUEUE_DEEP = 32

; 是否进行块大小测试，0 否，1 是
FILEBASE_TEST_BLOCK = 1
; 块大小测试，0 全零，1 随机， 2 混合
FILEBASE_BLOCK_FILELEN = 512 MB
; 块大小测试的延迟时间
FILEBASE_BLOCK_DELAY = 0
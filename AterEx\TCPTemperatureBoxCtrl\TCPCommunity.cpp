#include "stdafx.h"
#include "TCPCommunity.h"
#include <WS2tcpip.h>

TCPCommunity::TCPCommunity()
{
    m_socket = INVALID_SOCKET;
    m_bConnected = false;
    m_dwLastError = 0;
    m_nPort = 0;
    InitWinsock();
}

TCPCommunity::~TCPCommunity()
{
    CloseConnection();
    CleanupWinsock();
}

BOOL TCPCommunity::InitWinsock()
{
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        m_dwLastError = WSAGetLastError();
        return FALSE;
    }
    return TRUE;
}

void TCPCommunity::CleanupWinsock()
{
    WSACleanup();
}

BOOL TCPCommunity::InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs)
{
    if (m_bConnected && m_serverIP == _pServerIP && m_nPort == _nPort) {
        return TRUE; // 已经连接到相同的服务器
    }
    
    CloseConnection();
    
    m_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_socket == INVALID_SOCKET) {
        m_dwLastError = WSAGetLastError();
        return FALSE;
    }
    
    // 设置连接超时
    DWORD timeout = _nTimeoutMs;
    setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
    setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
    
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(_nPort);
    inet_pton(AF_INET, _pServerIP, &serverAddr.sin_addr);
    
    if (connect(m_socket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        m_dwLastError = WSAGetLastError();
        closesocket(m_socket);
        m_socket = INVALID_SOCKET;
        return FALSE;
    }
    
    m_bConnected = true;
    m_serverIP = _pServerIP;
    m_nPort = _nPort;
    m_dwLastError = 0;
    
    return TRUE;
}

void TCPCommunity::CloseConnection()
{
    if (m_socket != INVALID_SOCKET) {
        closesocket(m_socket);
        m_socket = INVALID_SOCKET;
    }
    m_bConnected = false;
}

bool TCPCommunity::IsConnected()
{
    return m_bConnected && m_socket != INVALID_SOCKET;
}

DWORD TCPCommunity::SendData(const char* _pData, int _nDataLen)
{
    if (!IsConnected()) {
        m_dwLastError = ERROR_NOT_CONNECTED;
        return m_dwLastError;
    }
    
    // 创建带帧头的数据包
    char* pPacket = new char[6 + _nDataLen];
    
    // 填充6字节帧头（大端序）
    UINT32 dataLen = _nDataLen;
    pPacket[0] = 0;
    pPacket[1] = 0;
    pPacket[2] = (dataLen >> 24) & 0xFF;
    pPacket[3] = (dataLen >> 16) & 0xFF;
    pPacket[4] = (dataLen >> 8) & 0xFF;
    pPacket[5] = dataLen & 0xFF;
    
    // 复制数据
    memcpy(pPacket + 6, _pData, _nDataLen);
    
    DWORD result = SendRawData(pPacket, 6 + _nDataLen);
    delete[] pPacket;
    
    return result;
}

int TCPCommunity::ReceiveData(char* _pBuffer, int _nBufferSize, UINT _nTimeoutMs)
{
    if (!IsConnected()) {
        m_dwLastError = ERROR_NOT_CONNECTED;
        return -1;
    }
    
    // 先接收6字节帧头
    char header[6];
    int headerReceived = ReceiveRawData(header, 6, _nTimeoutMs);
    if (headerReceived != 6) {
        return -1;
    }
    
    // 解析数据长度（大端序）
    UINT32 dataLen = ((UINT32)header[2] << 24) | 
                     ((UINT32)header[3] << 16) | 
                     ((UINT32)header[4] << 8) | 
                     (UINT32)header[5];
    
    if (dataLen > (UINT32)_nBufferSize) {
        m_dwLastError = ERROR_INSUFFICIENT_BUFFER;
        return -1;
    }
    
    // 接收实际数据
    return ReceiveRawData(_pBuffer, dataLen, _nTimeoutMs);
}

DWORD TCPCommunity::SendRawData(const char* _pData, int _nDataLen)
{
    int totalSent = 0;
    while (totalSent < _nDataLen) {
        int sent = send(m_socket, _pData + totalSent, _nDataLen - totalSent, 0);
        if (sent == SOCKET_ERROR) {
            m_dwLastError = WSAGetLastError();
            m_bConnected = false;
            return m_dwLastError;
        }
        totalSent += sent;
    }
    return 0;
}

int TCPCommunity::ReceiveRawData(char* _pBuffer, int _nBufferSize, UINT _nTimeoutMs)
{
    int totalReceived = 0;
    while (totalReceived < _nBufferSize) {
        int received = recv(m_socket, _pBuffer + totalReceived, _nBufferSize - totalReceived, 0);
        if (received == SOCKET_ERROR) {
            m_dwLastError = WSAGetLastError();
            m_bConnected = false;
            return -1;
        }
        if (received == 0) {
            // 连接被关闭
            m_bConnected = false;
            return totalReceived;
        }
        totalReceived += received;
    }
    return totalReceived;
}

DWORD TCPCommunity::GetLastErrorCode()
{
    return m_dwLastError;
}
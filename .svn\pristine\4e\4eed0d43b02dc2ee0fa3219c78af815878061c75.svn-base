﻿#pragma once

#include <TypeDef.h>

namespace SG9081CfgDef
{
	U32 const ECC_FAIL_BIT_MASK = 0x00000800;
	U32 const RDT_INFO_ADDR     = 0x453A80;

	char const IDENTIFY_NAME[] = "9081CMD";

	enum ErrorCode
	{
		CMD_NO_ERROR = 0,
		CMD_ENTER_PRIVATE_MODE_FAIL = 1,
		CMD_FORCE_CLOSE_CMD = 2,
		C<PERSON>_DEVICE_IO_FAIL = 3,
		CMD_DEVICE_IO_CHECKSUM_FAIL = 4,
		CMD_SET_PRIVATE_READ_CMD_PARAM_FAIL = 5,
		CMD_CRC_FAIL = 6,
		CMD_ECC_FAIL = 7,
		C<PERSON>_PARAM_FAIL = 8,
		CMD_PRIVATE_FLAG_READ_CHECK_FAIL = 9,
		CMD_TIME_OUT = 10
	};

	typedef enum _E_SET_BOOT_SCAN_ENVIR_OP_TYPE 
	{
		SET_RDT_REG_CFG = 0,
		SET_RDT_FLASH_OP = 1,
		SET_RDT_FLASH_PARAM = 2,
		SET_WORDLINE_INFO = 3,
		SET_MODE_SWITCH = 4,
		SET_PROG_CQ_CMD = 5,
		SET_ERASE_CQ_CMD = 6,
		SET_READ_CQ_CMD = 7,
		SET_READ_STATUS_CQ_CMD = 8,
		SET_TWO_PLANE_DATAOUT = 9,
		SET_FEATURE_SET = 10,
		SET_FLASH_MODEL = 11,
		SET_TLC_WORDLINE_MAP = 12,
		SET_RDT_OP = 13
	} E_SET_BOOT_SCAN_ENVIR_OP_TYPE;

	typedef enum _E_ENVIR_TYPE
	{
		BOOT_SCAN_ENVIR_TYPE = 0,
		ALG_RDT_ENVIR_TYPE = 1
	}E_ENVIR_TYPE;

	enum E_CODE_TYPE
	{
		E_BOOT_CODE = 0,
		E_SCAN_CODE = 1,
		E_ALG_TABLE = 2,
		E_ALG_LOGIC = 3,
		E_ALG_SUPER_INDEX = 4,
		E_HIGH_TABLE = 5,
		E_CONCURRENT_TYPE = 6
	};

	enum E_FLASH_MODE
	{
		E_SDR_MODE = 0,
		E_DDR_MODE = 1
	};

	enum E_ERASE_TYPE
	{
		SLC_MODE = 0,
		MLC_MODE = 1,
		TLC_MODE = 2
	};
	static const U32 RD_STS_70_CMD = (0);
	static const U32 RD_STS_71_CMD = (1);
	static const U32 RD_STS_78_CMD = (2);
	static const U32 RD_STS_FN_CMD = (3);
	static const U32 PER_BLK_RDWR_MODE = (0);
	static const U32 ALL_BLK_RDWR_MODE = (1);

	static const U8 MAX_TLC_PRO_CQ_ADDR_PLANE_NUM = 5;
	static const U8 MAX_TLC_PRO_CQ_ADDR_NUM = 3;

	static const U32 MODE_DIS = (0);
	static const U32 MODE_EN  = (1);

	static const U32 RAND_PATTERN_MODE  = (0);
	static const U32 FIX_PATTERN_MODE  =  (1);

	static const U32 SAVE_BLK_ECC   = (0);
	static const U32 SAVE_PAGE_ECC  = (1);

	static const U32 MAX_CH_NUM = (4);
	static const U32 MAX_CE_NUM = (4);
	static const U32 MAX_LUN_NUM = (16);
	static const U32 MAX_PLN_NUM = (4);

	const static U32 COL_ADDR_ON_PLN = (0);
	const static U32 COL_ADDR_ON_LUN = (1);
	const static U32 MAX_BLK_NUM = (4);

	const static U32 R_CQ_ATTR_BIT = (24);
	const static U32 R_CQ_ATTR_CMD = (((U32)0x08)<<R_CQ_ATTR_BIT);
	const static U32 FL_CMD_MUL_PLANE_CMD1 = 0x81;
	const static U32 FL_CMD_MUL_PLANE_CMD = 0x11;
	const static U32 FL_CMD_READ4 = 0x32;
	
	static const U8 CONST_MAX_G0_FW_VER_SIZE = 40;
	static const U8 CONST_MAX_G3_FW_VER_SIZE = 40;
	static const U8 CONST_MAX_CH_NUM = 4;
	static const U8 CONST_MAX_CE_NUM_PER_CH = 4;
	static const U8 CONST_MAX_FLASH_ID_SIZE = 8;
	static const U16 CONST_MAX_USERDATA_SIZE = 256;

	static const U32 CMD_TITILE_SIZE	 = 8;//sata控制命令头大小
	static const U32 CMD_DEFINE_DATA_SIZE	 = 468;//sata自定义数据大小

	static const U32 IC_VERSION_SIZE = 8;//主控信息字符串长度

#pragma pack(1)

	/////////////////////////////////////////////////////////////////////////////////////////////


	typedef struct  
	{
		U8 bOpType;//具体的操作类型
		U8 bOpSct; //具体的操作的扇区个数
		U8 bReserve[2];

	}T_PARAM;
	/////////////////////////////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////////////////////////////
	// 私有命令参数
	//// CMD_OPCODE_READ_BASIC_INFO
	typedef struct STR_PRI_FW_VER
	{
		//G0固件版本号，无效时为0XFF
		U8 m_g0FwVersion[CONST_MAX_G0_FW_VER_SIZE];
		//G3固件版本号，无效时为0XFF
		U8 m_g3FwVersion[CONST_MAX_G3_FW_VER_SIZE];

	} PRI_FW_VER, *PPRI_FW_VER;

	//// CMD_OPCODE_READ_BASIC_INFO
	typedef struct STR_PRI_FLASH_ID
	{
		//4通道,4CE,每CE8 BYTE ID
		U8 m_flashID[CONST_MAX_CH_NUM][CONST_MAX_CE_NUM_PER_CH][CONST_MAX_FLASH_ID_SIZE];
	} PRI_FLASH_ID, *PPRI_FLASH_ID;

	// FlyCode私有命令参数头
	typedef struct _CODE_PRICMD_PARAM_HEAD
	{
		U32 dOpRowAddr;
		U16 wOpBlk;
		U16 wOpColAddr;

		U8  bOpCh;
		U8  bOpCe;
		U8  bOpLun;
		U8  bOpPln;

		U32 dOpByteCnt;
		U16 wOpSctCnt;
		U8  bErrDma;
		U8  bErrCnt;

		U8  bDir;
		U8  Reserve[5];
		U16 wDevFrequency;

	}CODE_PRICMD_PARAM_HEAD,*PCODE_PRICMD_PARAM_HEAD;

	// 通过私有命令逻辑读写命令结构
	typedef struct _PRI_LG_RD_WR
	{
		CODE_PRICMD_PARAM_HEAD codePricmdParamHead;
		U32 dLba;
		U16 wSctCnt;
		U8 bDir;
		U8 bReserve;
	}PRI_LG_RD_WR, *PPRI_LG_RD_WR;

	// 逻辑读写命令结构
	typedef struct STR_SSD_PRI_LOGIC_RW_PARAM
	{
		// 是否用私有命令log读写
		Bool bPrivateLogRW;

		// 是否用SCSI标准命令（0x2a）写
		Bool bSCSILogRead;

		// 逻辑扇区偏移
		U32 m_addr;

		// 逻辑数据长度，以sector为单位
		U16 m_secNum;
	} SSD_PRI_LOGIC_RW_PARAM, *PSSD_PRI_LOGIC_RW_PARAM;

	typedef struct STR_PRI_BASIC_INFO
	{
		PRI_FW_VER m_strFwVer;
		PRI_FLASH_ID m_strFlashID;
		U32 dResReg0Val;
		U8 bICVersion[IC_VERSION_SIZE];
	} PRI_BASIC_INFO, *PPRI_BASIC_INFO;

	//// CMD_OPCODE_OP_RAM
	typedef struct STR_PRI_RAM_OP_INFO
	{
		//RAM地址
		U32 m_ramAddr;
		//操作扇区的个数
		U16 m_sctCnt;
		U8  m_bReserve[2];

	} PRI_RAM_OP_INFO, *PPRI_RAM_OP_INFO;

	//// CMD_OPCODE_SWITCH_INTERFACE
	typedef struct STR_PRI_CORE_SWITCH
	{
		//控制G0是否使能，0:dis，1:en
		U8  m_g0RunningEn;
		U8  m_bReserve1[3];
		//设置G0PC指针地址
		U32 m_g0PCAddr; 
		//设置G0的向量地址
		U32 m_g0VectorAddr;
		//控制G3是否使能，0:dis，1:en
		U8  m_g3RunningEn;
		U8  m_bReserve2[3];
		//设置G3PC指针地址
		U32 m_g3PCAddr;   
		U8  m_bReserve3[4];
		U32 m_resReg0Val;
		U8 m_bReserve4[480];
		U32 m_checkSum;

	} PRI_CORE_SWITCH, *PPRI_CORE_SWITCH;

	//// CMD_OPCODE_OP_BOOT_IDX
	typedef struct STR_PRI_BOOT_IDX_PARAM
	{
		U8 m_reserve[32];

	} PRI_BOOT_IDX_PARAM, *PPRI_BOOT_IDX_PARAM;

	//// CMD_OPCODE_OP_PAGE
	//// CMD_OPCODE_OP_BLK
	typedef struct STR_PRI_EWR_OP
	{
		//操作具体lun内的Page地址
		U32 m_opPageAddr;
		//操作具体lun内的块号
		U16 m_opBlk; 
		//操作BLK的Page偏移
		U16 m_opPageOffs;
		//操作的通道
		U8  m_opCh;  
		//操作的CE
		U8  m_opCe;  
		//操作的Lun
		U8  m_opLun;  
		//操作的Pln
		U8  m_opPln;  
		//操作的byte数据量
		U32 m_opByteCnt; 
		//操作的Sector数据量
		U16 m_opSctCnt; 
		//操作的种子
		U16 m_randSeed;
		// 当TLC读所发地址为WL地址时，该段表示读WL内偏移的页
		U8	bWLIndex;
		// SLC操作\TLC操作,0:SLC、1:TLC
		U8	bOpMode;
		// 指定userdata的长度，写读boot的时候，该长度为0，写读算法表时非0
		U16 wUserDataLen;
		U8  bUserData[CONST_MAX_USERDATA_SIZE];

		//***************************算法和RDT相关表格的读、写、擦时用************************
		// 表格簇大小
		U8 tabClstSec;
		// 数据簇大小
		U8 dataClstSec;
		// 擦除的类型
		E_ERASE_TYPE eraseType;
		// 数据类型，表格数据或逻辑数据
		U8 m_dataType;
		//是否发生retry
		U8 m_bRetry;

	} PRI_EWR_OP, *PPRI_EWR_OP;

	typedef struct STR_PRI_CFG_PARAM
	{
		//具体的操作类型
		U8 m_opType;
		//具体的操作的扇区个数
		U8 m_opSct; 
		// 环境类型，表示当前配置的是哪一套环境
		U8 m_envirType;
		U8 bReserve[1];
	} PRI_CFG_PARAM, *PPRI_CFG_PARAM;

	typedef  struct _FDL_PARAM_INFO
	{
		U8  bSctCnt;//除head外的扇区个数
		U8  bReverse[3];
	}FDL_PARAM_INFO;

	typedef  struct
	{
		U8  m_bDataType;   // 数据类型
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U8  m_bClstOffs;   // 页中簇偏移
		U32  m_dRowAddr;    // Lun中的行地址
	}T_PHY_ADDR;

	typedef  struct
	{
		U8  bClstCnt;
		U8  bSctCnt;
		U16  UserDataOffs;//userdata相对起始data的所在ram的偏移
		U8  bRetry;
		U8  bReserved[3];
		T_PHY_ADDR m_tClstAddr[32];
	}T_RD_ClST_OP_INFO;

	typedef  struct
	{
		U8  bPage;
		U8  bSctCnt;
		U8  bReverse[6];
		T_PHY_ADDR m_tPageAddr;
		U8  bUserData[256];
	}T_WR_ClST_OP_INFO;

	typedef  struct
	{
		U8 bScntCnt;
		U8 bReserve[3];
		T_PHY_ADDR m_SuperIndexAddr;
	}T_SUPER_INDEX_OP_INFO;

	typedef  struct
	{
		U8  bEraseType;
		U8  bBlkCnt;//擦除的块个数
		U8  bSctCnt;
		U8  bReverse;
		T_PHY_ADDR m_tBlkAddr[32];
	}T_ERS_BLK_OP_INFO;

	// ********擦除返回状态************//
	typedef enum
	{
		ERS_ERR_NONE = 0,
		ERS_VDT ,   //VDT first
		ERS_FAIL,
	}ERASE_ERR_TYPE;

	typedef struct _T_ERASE_EXCEPTION
	{
		U8 bDescript;   //0 erase success,1 erase fail
		U8 ArrRev[1];
		U16 wLen;       //length of exception information
	}ERASE_EXCEPTION;

	typedef struct _T_ERR_INFO
	{
		U16 wIndex;         //index of parameter which has occur error
		ERASE_ERR_TYPE ErrType;        //error type,
	}ERASE_ERR_INFO;
	// ******************************//

	//// 逻辑操作信息
	//typedef struct STR_PRI_LOGIC_CMD_PARAM
	//{
	//	U8 m_opCode;
	//	U16 m_secNum;
	//	U32 m_logicSecOffset;
	//} PRI_LOGIC_CMD_PARAM, *PPRI_LOGIC_CMD_PARAM;

	typedef struct _SPI_RDWR_DATA
	{
		U32 opPageAddr;
		U16 opByteCnt;
		U16 opSctCnt;
	}SPI_RDWR_DATA;

	typedef struct _PRI_LG_RW
	{
		U32 lba;
		U32 secSize;
		U8  direction;
		U8  rev[3];
	}PRI_LG_RW;

	// 读写取RDT_CFG参数
	typedef struct _READ_WRITE_RDT_CFG
	{
		U8 rdtCfgOpCh;
		U8 rdtCfgOpCe;
		U16 rdtCfgOpSecCnt;
		U32 rdtCfgOpPageAddr;
	}READ_WRITE_RDT_CFG, *PREAD_WRITE_RDT_CFG;

	// 读写RDT_INFO参数
	typedef struct _READ_WRITE_RDT_INFO
	{
		U8 rdtInfoOpCh;
		U8 rdtInfoOpCe;
		U16 rdtInfoOpSecCnt;
	}READ_WRITE_RDT_INFO, *PREAD_WRITE_RDT_INFO;

	// 设置是否进行RDT理想扫描参数
	typedef struct _SET_IS_RDT_RUN_PARAM
	{
		U8 bRdtRun;
	}SET_IS_RDT_RUN_PARAM, *PSET_IS_RDT_RUN_PARAM;

	typedef struct _FLASH_PHY_BLK_ADDR
	{   
		U8  m_bChan;        // Channel index
		U8  m_bLunOfChan;    // LUN index of Channel
		U16  m_wBlkAddr;    // block index of LUN
	}FLASH_PHY_BLK_ADDR, *PFLASH_PHY_BLK_ADDR;

	// ADJ扫描参数
	typedef	struct _ADJ_SCAN_PARAM
	{
		FLASH_PHY_BLK_ADDR m_tBlkAddr[16]; //4CH * 4CE
		U16  bBlkCnt;
		U8	planeNum;
		U8  bReverse;
	} ADJ_SCAN_PARAM, *PADJ_SCAN_PARAM;

	// 多地址操作时，发地址命令参数
	typedef  struct _OP_ADDR_INFO
	{
		U16  m_wTotalOpAddrCnt; ////表示普通编程的页数，3D Flash的Unit数(该模式下最多支持255个)，读簇操作的簇个数
		U8  m_bOpMode;          ////0:普通读写操作，地址结构为T_FLASH_PHY_ADDR， 1：3D编程操作，地址结构为T_FLASH_PROG_ORDER_UNIT
		U8  m_MultiPlnEn;       //表示在order编程时，是否需要开启multipln
	}OP_ADDR_INFO, *POP_ADDR_INFO;

	// 多地址操作时，读写Userdata命令参数
	typedef  struct _USERDATA_INFO
	{

		U16  m_wTotalUserdataCnt; //表示总共的userdatabyte个数，不能超过16kbyte个
		U8  m_bUserdataSize;     //表示每个userdata的大小，即byte个数
		U8  m_bReserve;
	}USERDATA_INFO, *PUSERDATA_INFO;

	// 多地址操作时，读写数据区数据命令参数
	typedef  struct _FLASH_OP_INFO
	{
		U16  m_wFlashOpMode; //:0表示表示正常的Flash顺序标尺，1表示TLC Flash的Order编程
		U16  m_wReserve2;
	}FLASH_OP_INFO, *PFLASH_OP_INFO;

	typedef struct _FLASH_CACHE_OP
	{
		U8  bChan;
		U8  bCeip;
		U8  bPlane;
		U8  bRndEn; //随机数    0 : dis  1:En
		U16 wSet;
		U8  bReverse[2];
	}T_FLASH_CACHE_OP;

	//8T23 retry scan发送地址
	typedef struct _RETRY_PlANE_ADDR
	{
		U8 bChan;
		U8 bChip;
		U8 bReserve[2];
		U32 dBlkAddr;
	}RETRY_PlANE_ADDR;

	typedef struct _RDT_ONLINE_SCAN_PARAM
	{
		U8 bOpCh;
		U8 bOpCe;
		U8 bEraseEnable;
		U8 bProgEnable;
		U8 bReadEnable;
		U8 bReserve[3];
		U16 wOpSct;
		U16 wBlkNum;
		U16 wBlkNumCnt;
		U16 wSLCLoopCnt;
		U16 wTLCLoopCnt;
		U16 wFlashSerialNum;
		U8 bMultiPlane;
		U8 bRetryReadEnable;
		U8 bReserve1[6];
	}RDT_ONLINE_SCAN_PARAM;

	typedef struct
	{
		//  U32  rDMA2_LEN_CFG;    //0: 代表不开USER DATA, default:512// UI 上选择 有512, 
		union
		{
			U32 dAll;
			struct
			{
				U32 BASIC_DATA_LEN:12 ;/*DMA长度， UI上可选择 512, 1024, 2048， 并且支持可以自由输入， 默认是512*/
				U32 Reserved:4 ;/*保留*/
				U32 UDATA_LEN:6 ;/*默认是0，可选择 0， 1， 并且可以自由输入*/
				U32 Reserved1:2 ;/*保留*/
				U32 UDATA_ATTCH_SIZE:6 ;/*默认是0，可选择 0， 1， 2， 并且可以自由输入*/
				U32 Reserved2:2 ;/*保留*/
			}bits;
		}rDMA2_LEN_CFG;/*<Offset:0x04> */

		//  U32 rNF_CFG;
		U8  FlashMode;  //0: SLC 2: TLC; default:0; //UI 上只有 SLC 和 TLC两种选择， 
		//  U8  EccMode;  //0: LDPC; 1:BCH; 2:ecc disable;
		U8  BchMode;  //0：LDPC 其他的都是BCH        //UI上选择 8， 16， 24， 30， 36， 40， default： 0
		U8  LdpcMode;  //default:7;          //UI上选择 5， 6， 7 ，8
		U8  Randomize;  //1:randomize， 0:bypass, default：0 //UI上选择 randomize， bypass;
		U8  bReverse[3];
		U8  SectorCnt;  //传输的总sector 数，cmdhead除外；

		U8  OpMode;  //0: write, 1: read, 2: erase;    //UI上选择 wrie, read, erase三种， 另外可以继续手动输入数值，
		U8  OpCnt;  //操作地址个数，默认为1；只给出第一个地址， 后面的地址就是页连续， 或者擦的时候块连续
		U16  DataSize;  //每个地址读写数据的长度， 那么总的数据长度就是OpCnt*DataSize; default:16k

		U8  ch;
		U8  ce;
		U8  lun;  //default:0
		U8  plane;
		U16  blk;
		U16  page;
		U16 wReverse;
		U16  col;  //defaul:0
		U32 row;//default 0xffffffff, 优先row, 就是如果blk, page没有填或者和row不对应的优先操作row； 

		//  U32 DataPatern;  //写的时候数据模式  
	}T_UI_PARAM;

	typedef struct
	{
		U16 bClstCnt;		//pageCnt
		U8 bSctCnt;			//表示发送的T_FLASH_PHY_ADDR数量,例如40*8< 2sectors
		U8 bRetry;			//是否使能Retry
		U8 bReserve[4];		//保留位
	}T_WR_CIST_OP_INFO,*pT_WR_CIST_OP_INFO;

	typedef struct _PRIVATE_CMD_EX
	{
		U8 opCmd;
		U8 subOpCmd;
		U8 reserved[2];

		union 
		{
			struct 
			{
				SPI_RDWR_DATA spiRdWrParam;
				U8 exParam[500];
			}spiRandWR;

			struct 
			{
				PRI_LG_RW priLgRw;
				U8 rev[496];
			}priLogWR;

			//Flash cache效验
			T_FLASH_CACHE_OP FlashCacheInfo;

			// 读写取RDT_CFG参数
			READ_WRITE_RDT_CFG readWriteRdtCfg;

			// 读写RDT_INFO参数
			READ_WRITE_RDT_INFO readWriteRdtInfo;

			// 设置是否进行RDT理想扫描参数
			SET_IS_RDT_RUN_PARAM setIsRdtRunParam;

			// ADJ扫描参数
			ADJ_SCAN_PARAM adjScanParam;

			/*****************多地址操作*************/
			// 多地址操作时，发地址阶段的命令参数
			OP_ADDR_INFO opAddrInfo;

			// 多地址操作时，读写Userdata命令参数
			USERDATA_INFO usedataInfo;

			// 多地址操作时，读写数据区数据命令参数
			FLASH_OP_INFO flashOpInfo;

			//8T23 retry scan发送地址
			RETRY_PlANE_ADDR retryPlaneAddr;

			//在线RDT扫描
			RDT_ONLINE_SCAN_PARAM rdtOnlineScanParam;
			/***************************************/

			T_UI_PARAM wrUIParam;
			
			T_WR_CIST_OP_INFO readUserDataParam;

		};
	}PRIVATE_CMD_EX;

	typedef  struct
	{   
		U8  m_bDataType;   // 数据类型
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U8  m_bClstOffs;   // 页中簇偏移
		U32  m_dRowAddr;    // Lun中的行地址
	} T_FLASH_PHY_ADDR; 

	typedef	struct _BOOT_ENVIR_CFG
	{
		U8 bEccType;  //记录该操作采用的ecc模式，1表示BCH模式，0表示LDPC模式（LDPC下只采用模式7）
		U8 bEccMode;  //记录该操作时，采用BCH时的ECC模式
		U16 wDmaLen;   //记录该操作，所选用的dma长度
		U16 wEcc_Threshold;
		U16 wReserve;
	}T_BOOT_ENVIR_CFG;

	typedef struct
	{
		U32  m_dRowAddr;    // Lun中的行地址
		U16 wColAddr;       //HAL参数指针，由上层初始化
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U32 secCnt;
		
		U8 bEccType;  //记录该操作采用的ecc模式，1表示BCH模式，0表示LDPC模式（LDPC下只采用模式7）
		U8 bEccMode;  //记录该操作时，采用BCH时的ECC模式
		U16 wDmaLen;   //记录该操作，所选用的dma长度
		U16 wEccThreshold;
		U16 wInvalidDataLen;  //用于boot写表中记录无效数据的长度，表示最后一段无效数据的长度
		U32 dDataLen;         //需要操作的数据量byte数
	}T_BOOT_PARA;

	typedef struct
	{
		U32  m_dRowAddr;    // Lun中的行地址
		U16  wColAddr;       //HAL参数指针，由上层初始化
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U32 wSecCnt;
		
		U8 bEccType;  //记录该操作采用的ecc模式，1表示BCH模式，0表示LDPC模式（LDPC下只采用模式7）
		U8 bEccMode;  //记录该操作时，采用BCH时的ECC模式
		U16 wDmaLen;   //记录该操作，所选用的dma长度
		U8  bRndEn;
		U8  bBadColEn;
		U16 wInvalidDataLen;  //用于boot写表中记录无效数据的长度，表示最后一段无效数据的长度
		U32 dDataLen;         //需要操作的数据量byte数
	}T_HF_PARA;

	const U8 MAX_COL_NUM_OF_CONCURRENT_RW_FLASH_ADDR = 4;
	typedef struct _CONCURRENT_RW_FLASH_ADDR
	{
		union
		{
			U32 addr;
			struct  
			{
				U32 chNo : 3;
				U32 ceNo : 2;
				U32 lunNo : 1;
				U32 planeNo : 2;
				U32 rowAddr : 24;
			}tAddr;
		}utAddr;

		U8 secOfst[MAX_COL_NUM_OF_CONCURRENT_RW_FLASH_ADDR];	// 页内起始扇区地址
		U8 secSize[MAX_COL_NUM_OF_CONCURRENT_RW_FLASH_ADDR];	// 扇区大小
	}CONCURRENT_RW_FLASH_ADDR, *PCONCURRENT_RW_FLASH_ADDR;
	const U8 MAX_ADDR_NUM_OF_ONE_PRIVATE_CMD = 36;

	typedef struct _CONCURRENT_RW_PARAM
	{
		U8 bOpAddrCnt;
		U8 bConcurrentLunNum;	// 一次多少个lun并行
		U8 bPlaneMode;			// 0:single page, 1:multiplane
		U8 bTlcFlag;			// 0:slc, 1:tlc
		CONCURRENT_RW_FLASH_ADDR arrConcurrentRwFlashAddr[MAX_ADDR_NUM_OF_ONE_PRIVATE_CMD];
	}CONCURRENT_RW_PARAM, *PCONCURRENT_RW_PARAM;

	typedef struct _RW_PAGE_PARAM
	{
		U8 codeType;

		union
		{
			T_BOOT_PARA bootPara;
			PRI_EWR_OP priEwrOp;
			T_HF_PARA hfParam;																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																													
			CONCURRENT_RW_PARAM concurrentRwParam;
		}DetailParam;
	}RW_PAGE_PARAM, *PRW_PAGE_PARAM;
	/////////////////////////////////////////////////////////////////////////////////////////////

	//*////////////////////////////写表scan的环境参数/////////////////////////////////////////////
	const U32 SIZEOFLDPCP1 = (240*4);
	const U32 SIZEOFLDPCP2 = (400*4);
	const U32 SIZEOFLDPCA = (150*4);
	const U32 SIZEOFLDPCB = (512*4);

// 	typedef  struct
// 	{
// 		U8  dMtx_A[SIZEOFLDPCA];
// 		U8  dMtx_B[SIZEOFLDPCB];
// 		U8  bMtx_P1[SIZEOFLDPCP1];
// 		U8  dMtx_P2[SIZEOFLDPCP2];
// 	} T_LDPC_MATRIX;

	typedef	struct _ENVIR_CFG
	{
		U8 bEccType;  //记录该操作采用的ecc模式，1表示BCH模式，0表示LDPC模式（LDPC下只采用模式7）
		U8 bEccMode;  //记录该操作时，采用BCH时的ECC模式
		U16 wDmaLen;   //记录该操作，所选用的dma长度
		U16 wEcc_Threshold;
		U16 wInvalidDataLen;  //用于boot写表中记录无效数据的长度，表示最后一段无效数据的长度
		U32 dDataLen;         //需要操作的数据量byte数
	}T_ENVIR_CFG;
	/////////////////////////////写表scan的环境参数////////////////////////////////////////////*//

	//////////////////////////////扫描scan的环境参数/////////////////////////////////////////////
	typedef enum 
	{
		RDT_CFG_TABLE = 101
	}TABLE_TYPE;

	static const S8 RDT_VERSION[] = "20170110";
	static const S8 VERIFY_KEY[] = "jkjk872s";

	#define UART_DEBUG_ON_TEST (1)

	#define ONLINE_MODE (0)
	#define RDT_MODE    (1)

	#define ATTRIBUTE_PACKED  __attribute__ ((packed))

	#define TOTAL_CH_CNT 4
	#define CH_INVALID 	 					(0xFF)
	#define CE_INVALID                      (0xFF)
	#define CH_VALID						(0x1)
	#define CE_VALID						(0x1)
	#define INVALID_BLK						(0xFFFF)
	#define INVALID_WL						(0xFFFFFFFF)
	#define INVALID_PAGE					(0xFFFFFFFF)
	#define INVALID_SEQ						(0xFF)
	#define INVALID_CMD						(0xFF)

	#define RDT_CFG_KEY0	(0x5555aaaa)
	#define RDT_CFG_KEY1	(0xaaaa5555)
	#define RDT_INFO_KEY0	(0x5a5a5a5a)
	#define RDT_INFO_KEY1	(0xa5a5a5a5)
	#define  RDT_INFO_CHECKSUM (0x5a5a050a)

	//为了支持TLC Flash的Copyback扫描，因此提供源块记录字段：

	//#define ENABLE 	(0x1)
	#define DISABLE (0x0)

	#define DIR_READ (1)
	#define DIR_WRITE (0)

	enum FLASH_TYPE
	{
	SLC_TYPE = 0,
	MLC_TYPE = 1,
	TLC_TYPE = 3
	};

#if 0
	//读状态的定义：
	typedef struct _READ_STS
	{
		U8   bOPMode;       //0:70,1:71,2:78 addr  3:fn
		U8   bReserve[3];
	}T_READ_STS;

	//改变随机读的列地址定义：
	typedef struct _READ_COL
	{
		U8   bOPMode;       //0:05 Col E0, 1:00 Row Col 05 Col E0
		U8   bReserve[3];
	}T_READ_COL;

	//改变随机写的列地址定义：
	typedef struct _WRITE_COL
	{
		U8   bOPMode;       //0:85 Col,1:85 Row Col
		U8   bReserve[3];
	}T_WRITE_COL;

	//Multiplane和需加前缀命令的控制CQ定义：
	typedef struct _CQ_CFG
	{
		U32 dPreErsCQCmd[4];
		U32 dPreProgCQCmd[4];
		U32 dPreReadCQCmd[4];
		U32 dMultiPlnCQWrCmd1; //多Plane编程时,非最后Plane的编程命令，如0x11。
		U32 dMultiPlnCQWrCmd2; //多Plane编程时，启动命令（如0x80,0x81、或0x85（开启cache编程））
		U32 dMultiPlnCQRdCmd;  //多Plane读操作时，非最后一个Page的读命令码，如0X32替换0X30  
	}T_CQ_CFG;

	/*flash类型*/
	enum E_FLASH_TYPE
	{
		E_FLASH_TYPE_SLC = 0,
		E_FLASH_TYPE_MLC = 0,
		E_FLASH_TYPE_ED3 = 3,
		E_FLASH_TYPE_OBP = 4,
		E_FLASH_TYPE_MLC_3D = 5,
		E_FLASH_TYPE_TLC_3D = 6
	};

	/*OBP类型flash的子类型*/
	enum OBP_FLASH_SUBTYPE
	{
		OBP_19NM_ACG_FLASH  =90,  //19NM ACG
		OBP_19NM_ADG_FLASH  =95,   //19NM ADG
		OBP_SP_FLASH        =105
	};

	/*ED3类型flash的子类型*/
	enum ED3_FLASH_SUBTYPE
	{
		ED3_8M2M_TLC	= 200
	};

	/*ED3的program序号*/
	enum ED3_PROGRAM_SEQ
	{
		FIRST_PROG		= 0,
		SECOND_PROG		= 1,
		THIRD_PROG		= 2,
		ED3_PROG_MAX	= 3
	};

	typedef struct _ED3_PARAM
	{
		U16	wTLC_PageNub[ED3_PROG_MAX];	// program order对应的page number(LSB+CSB+MSB)
		U8  wProgSeq;			// 编程序号(eg: 1st PGM/2nd PGM/3rd PGM)
		U8	wPageCnt;			// pargram order实际对应的page number
	}T_TLC_PARAM;

	typedef struct  _COPY_BACK_CTRL
	{
		//	U16 wSourceBlkNum[MAX_CH_NUM][MAX_LUN_NUM][MAX_PLN_NUM][MAX_BLK_NUM];
		U16 wSourceBlkNum[MAX_CH_NUM][MAX_CE_NUM][4][MAX_PLN_NUM][MAX_BLK_NUM];
		U8  bUsefulChNum;
		U8  bUsefulChipNum;
		U8  bUsefulLunNum;
		U8  bUsefulPlnNum;
		U8  bUsefulBlkNum;
		U8  bReserve[3];
	}T_COPY_BACK;

	//具体的定义则如下：
	typedef struct
	{
		T_READ_STS       tRdStsCfg;      //读状态控制参数
		T_READ_COL       tRdColCfg;      //随机读控制参数
		T_WRITE_COL      tWrColCfg;      //随机写控制参数
		T_CQ_CFG         tCQCfg;         //CQ配置控制
		T_COPY_BACK      tCBCtrl;        //copyback操作的开关
		U8               bMultiPlnOpen;  //多plane操作的开关，0:disable
		U8               bInterleaveOpen;//interleave模式的开关
		U8               bMultiChOpen;   //多通道操作模式的开关；
		U8               bReadColOpen;   //带随机读列地址操作的开关;
		U8               bWriteColOpen;  //带随机写列地址操作的开关；
		U8               bReserve[2];
	}T_FLASH_OP;
#endif

	typedef struct
	{
		U8 m_bWordlineType;       //0:  单Wordline结构。   1: 奇偶Wordline结构
		U8 m_bReserved0[3];
		U16 m_wSlcPageOfBlk;
		U16 m_wWordlineMax;       //每个块中包含的最大Wordline个数
		U16 m_wSmallPageBaseNum;  //PageToWLMapTab的结尾段，小的页号基本值
		U16 m_wBigPageBaseNum;    //PageToWLMapTab的结尾段，大的页号基本值
		//Flash的Group A/B/C 表。
		U16  m_wPageGroupMapTabLowWL[8][4]; //由Wordline号，查找映射的Page Number
		U16  m_wPageGroupMapTabHighWL[8][4];
		U8  m_bSmallPageMappedWlNum[8*4]; //由Page Number查找对应的Wordline号，以及Wordline内的page 偏移号
		U8  m_bSmallPageMappedOffs[8*4];    //物理页偏移对应的WL内PageOffs
		U16  m_wBigPageMappedWlNum[8*4];
		U8  m_bBigPageMappedOffs[8*4];
		U16  m_wSmallOrderBase;    //OrderToWLMapTab的结尾段，小的Order号基本值
		U16  m_wBigOrderBase;      //OrderToWLMapTab的结尾段，大的Order号基本值
		U16  m_wBeginSegOrderWlThres;  //起始特殊Order对应最末Wordline值  (ED3 Flash为第一个Wordline，即0， 而Samsung TLC为最后一个包含3个Page的Wordline)
		U16  m_wEndSegOrderWlThres;    //末尾特殊Order对应最小Wordline值  (ED3 Flash为最后一个Wordline， Samsung TLC为最后一个包含3个Page的Wordline)
		//TLC Flash的 Program Order表。
		U16  m_wPrgOrderTabLowWL[8][4];        //由Wordline号，查找映射的Program Order信息
		U16  m_wPrgOrderTabHighWL[8][4];
		U8  m_bSmallOrderMappedWlNum[8*4];   //由Program Order查找对应的Wordline号，以及Wordline内的编程次序
		U8  m_bSmallOrderMappedCycle[8*4];    //Program Order对应的WL内第几次编程(1st/2nd/3rd Program)
		U16  m_wBigOrderMappedWlNum[8*4];
		U8  m_bBigOrderMappedCycle[8*4];
	}T_UDP_WL_PARAM;

	typedef struct
	{
		/**************通用寄存器************************/
		U32 dNfCfg;
		U32 dFioSpopCtrl;
		U32 dDdrTimingCfg;
		U32 dAleCleTimCfg;
		U32 dMcuCfgCle;
		U32 dMcuCfgAle;
		U32 dChkStRbCnt;
		U32 dChkCqCtrl;
		U32 dBadColumnCtrl;
		U32 dBadcolInfoBaddr;
		U32 dECCFuncSel;
		U8 dReserve[17];
		U8 bTRX2_CLK_SOURCE_SEL;
		U8 bTRX2_CLK_PERIOD;
		U8 bFlashOdtEn;
		/**************时钟采样设置相关寄存器******************/
		U32 dDqsDelayCtrl;
		U32 dSselCtrl;
		U32 dTselCtrl;
		U32 dPadMiscCtrl;
		/******************写表相关寄存器*********************/
		U32 dEnCtrl;				//err enter int
		U32 dEccCtrl;				//dma2x en
		U32 dBchMode;				// BCH Mode,dmax_ecc_cfg
		U32 dDMA2LenCfg;			// Len Cfg,user data len,user data attach len
		U32 dDMA2RndCtrl;			// Randomizer Configuration
		U32 dDMA2RndSeed;
		U32 bUserDataEn;			//是否带USER-DATA要根据写表的情形
		U32 dFilterColumnEn;		//是否带跳列(1表示需要跳列，0表示不需要)
		
		U32 PAD_dPhyXcfgD0;    //HAL赋值给寄存器: rPHY_XCFGD0
		U32 PAD_dPhyXcfgD1;    //HAL赋值给寄存器: rPHY_XCFGD1
		U32 PAD_dPhyXcfgD2;    //HAL赋值给寄存器: rPHY_XCFGD2
		U32 PAD_dPhyXcfgD3;    //HAL赋值给寄存器: rPHY_XCFGD3
		U32 PAD_dPhyXcfgA0;    //HAL赋值给寄存器: rPHY_XCFGA0
		U32 PAD_dPhyXcfgA1;    //HAL赋值给寄存器: rPHY_XCFGA1
		U32 PAD_dPhyXcfgA2;    //HAL赋值给寄存器: rPHY_XCFGA2
		U32 PAD_dPhyXcfgA3;    //HAL赋值给寄存器: rPHY_XCFGA3
	}T_RDT_REG_CFG;

	/* 			PRNG时会用到此数据结构
	 * 假设数据簇有10个DMA，即bDmaOfClst = 10.
	 * 前面9次DMA的大小 = wDmaSize + wParitySize;
	 * 最后一次DMA的大小 = wDmaSize + wParitySize + wUserDataSize*/
	typedef struct
	{
		U16 wDmaSize;			// DMA size大小
		U8  wParitySize;		// parity size大小
		U8  wUserDataSize;		// 用户数据size
		U8  bSectOfClst;		// 一个簇中的扇区数
		U8  bDmaOfClst;			// 一个簇DMA的次数
		U8  bClstOfPage;		// 一个page中的簇数
	}T_ALGO_CFG;

	typedef struct
	{
		U8  bCEMap[MAX_CH_NUM][MAX_CE_NUM];		//每个通道实际挂载的Chip
	}T_FLASH_MODEL;

	typedef struct
	{
		U8               bMultiPlnOpen;  //多plane操作的开关，0:disable
		U8               bInterleaveOpen;//interleave模式的开关
		U8               bMultiChOpen;   //多通道操作模式的开关；
		U8               bReadColOpen;   //带随机读列地址操作的开关;
		U8               bWriteColOpen;  //带随机写列地址操作的开关；
		U8               bReserve[2];
	}T_RDT_FLASH_OP;

	typedef struct
	{
		U8 m_bChanNum;                    //设备包含通道的总个数
		U8 m_bChipOfChan;                 //一个通道包含的物理片选数
		U8 m_bLunOfChip;                  //每Chip包含的Lun数
		U8 m_bLunOfChan;                  //一个通道包含的lun数
		U8 m_bLunOfDev;                   //设备包含总的lun数
		U8 m_bPlaneOfLun;                 //每个lun包含plane个数
		U16 m_wPageOfBlk;                  //每个block有多少个可用物理page
		U16 m_wRowOfBlk;                   //Block对应的行地址大小，主要用于擦除
		U16 m_wRowGapOfBlkInPlane;         //一个plane内，相邻两个blk的行地址间隔
		U32 m_dRowGapOfPlane;              //每个palne间，的行地址间隔（与m_wPageOfBlk相同?）
		U32 m_dRowGapOf2LUN;               //两个lun之间行地址的间隔
		U16 m_wBlkOfLUN;                   //每个lun包含的block个数
		U8 m_bSectOfPage;                 //1个page有多少个扇区
		U8 m_bSectOfDMA;                  //1个DMA有多少个扇区
		U8 m_bCellType;                 //1： SLC, 2:MLC, 3:TLC
		U8 m_bBlockGapInPlane;			// 一个plane内相邻block的间隔block数
		U16 m_BlockOfPlane;				// 一个plane包含的block个数
		U16 m_wWordlineMax;				//每个块中包含的最大Wordline个数
		U16 m_wPageSize;				//page大小，以字节为单位
		U16 m_wParitySize;				//page对应的parity大小，以字节为单位
		U16 m_wAlgUserdata;				//上层算法使用的userdata长度
		U16 m_wSlcWordlineMax;			//SLC模式下block中包含的WL数(也即page数)
		U8  m_bFillDataLen;				//每个page DMA完成后，还需要填充的数据字节数
		U8  bReserve[1];
	}T_RDT_FLASH_PARAM;

	enum TLC_PAGE
	{
		LSB_PAGE=0,
		CSB_PAGE=1,
		MSB_PAGE=2,
		MAX_PAGE=3
	};

	enum FLASH_PLANE
	{
		PLANE0=0,
		PLANE1=1,
		PLANE2=2,
		PLANE3=3,
		MAX_PLANE=4
	};

	enum PROG_SEQ
	{
		PROG_1ST=0,
		PROG_2ND=1,
		PROG_3RD=2
	};

	const U8 PRG_CYCLE_1ST = 0;
	const U8 PRG_CYCLE_2ND  = 1;
	const U8 PRG_CYCLE_3RD = 2;
	const U8 TOTAL_PRG_CYCLE = 3;
	const U8 MAX_WL_OF_PAGE = 3;
	const U8 MAX_SEGMENT = 8; //目前看到B16A需要8段的数据模

	typedef struct
	{
		U16   wWLCnt;   //这个段包含的wordline个数
		U16   wMinWLNum;//这个段最小wordline号
		U16   wMaxWLNum;//这个段最大wordline号
		U16   wPageBaseNum[MAX_WL_OF_PAGE];//page基值
		U8   bPageUnit[MAX_WL_OF_PAGE];   //page系数

		U8   bPrgCycleNum;//这个段中，每个wordline所需编程的次数
		U16   wPrgOrderBaseNum[TOTAL_PRG_CYCLE];//order基值
		U8   bPrgOrderUnit[TOTAL_PRG_CYCLE];   //order系数
		U8   bPrgPageCnt[TOTAL_PRG_CYCLE];//这个段中，wordline每次编程所包含的page个数
		U8   bPrgPageIdx[TOTAL_PRG_CYCLE][MAX_WL_OF_PAGE];//这个段中，wordline每次编程的page号索引
		U8   bReserve[3];
	}T_WL_MATH;//44Byte


	typedef struct _WORDLINE_INFO
	{
		U8   bSegCnt;//段的个数，暂时最大支持8个段 
		U8   bPrgCycleMaxCnt;
		U16   wPrgOrderTotalCnt;
		U16   wWordlineTotalCnt;
		U8	bExtraWLNum;//描述编程完最后一个wordline，需要额外编程几个wordline
		U8   bReserve[1];
		T_WL_MATH tWlMath[MAX_SEGMENT]; 
	}WORDLINE_INFO, *PWORDLINE_INFO;//356Byte

const U8 MAX_PREFIX_CQ_CMD = 4;
const U8 MAX_SUFFIX_CQ_CMD = 4;
	typedef struct
	{
		U8 PreAddrCQCmd [MAX_PREFIX_CQ_CMD]; 	// 驱动的前缀命令
		U8 AftDataCQCmd [MAX_SUFFIX_CQ_CMD];	// 驱动的后缀命令
		U8 bCmdAddrArray[MAX_PREFIX_CQ_CMD+MAX_SUFFIX_CQ_CMD];//表示上述两表每项为命令还是地址，0表示命令，1表示地址。0-3项表示前缀项，4-7项后缀项
	}T_CQ_CMD_UNIT;



	/*注意：类ED3的Flash prog时使用的是WL地址而不是page地址*/
	/*描述WL中编程的驱动模型*/
	typedef struct
	{
		U8  	bWLorOffsAddrMode;			// 1表示WL地址, 0表示page偏移地址
		U8	tNoFinalProgCmd;		//非final plane的program结束命令,一般为0x11
		U8	tFinalPlaneProgCmd;		// prog命令的结束命令,一般为0x10
		U8	tFirstProgCmd[MAX_PLANE];			// program的首个命令,一般为0x80
		U8   bExtraCmdorAddrMode;     //是否发送额外的命令，像samsung Flash TLC编程最后需要加8Bh···10h命令
		U8   bExtraWLorOffsAddrMode;  //同上述
		U8   bExtra1stProCQCmd[MAX_PLANE];		 //特别Flash（如Samsung）会发送额外的命令，像8Bh
		U8   bExtra2ndProCQCmd[MAX_PLANE];       //一般是10h
		U8	bProCacheCmd;	//Cache Pro

		U8	bReverse[38];

		/*************************下面字段用于TLC program************************************/
		// WL中第一次编程的驱动命令
		T_CQ_CMD_UNIT tProg1stUnit[MAX_PLANE][MAX_PAGE];
		// WL中第二次编程的驱动命令
		T_CQ_CMD_UNIT tProg2ndUnit[MAX_PLANE][MAX_PAGE];
		// WL中第三次编程的驱动命令
		T_CQ_CMD_UNIT tProg3rdUnit[MAX_PLANE][MAX_PAGE];
	}T_PROG_CQ_CMD;

	/*描述擦除块的驱动模型*/
	typedef struct
	{
		U8		tFirstEraseCmd;			// 擦除的首个命令,一般为0x60
		U8		tNoFinalEraseCmd;		// 非final plane的擦除结束命令,一般为0xD1
		U8		tFinalPlaneEraseCmd;	// 擦除命令的结束命令,一般为0xD0
		U8		bReverse[5];
	}T_ERASE_CQ_CMD;

	typedef struct
	{
		U8		tWLFlag;				// 1表示WL地址,0表示page地址
		U8		tFirstReadCmd;			// read的首个命令,一般为0x00
		U8		tNoFinalReadCmd;		// 多plane时的中间read命令,一般为0x32
		U8		tFinalReadCmd;			// read命令的结束命令,一般为0x30
		U8		tPagePreCmd[MAX_PAGE];	// 操作TLC Flash的page的前缀命令,一般为 0x01/0x02/0x03
		U8		bReverse[1];
	}T_READ_CQ_CMD;

	typedef struct
	{
		U8		tReadCmd;				// 一般为0x00
		U8		tReadCmdComfirm;		// 一般为0x05
		U8		tRandomCmd;				// 一般为0xe0
	}T_TWO_PLANE_DATAOUT;

	typedef struct
	{
		U8		tStatus;			// 读取整个target的状态命令，一般为0x70
		U8		tEnhancedStatus;	// 读选定LUN的状态命令，一般为0x78
	}T_READ_STATUS_CQ_CMD;

	typedef struct
	{
		U8 bFlashMode; //SDR:0x00<->DDR:0x02
		U8 bSetFeatureCmd; // set feature命令, 一般为0xEF
		U8 bFeatureAddr; // set feature地址, 01h表示Timing mode,02h表示Toggle 2.0 specific,10h表示Driver strength,30h表示External VPP
		U8 bPxData[4];				// 4字节的脉冲数据
		/***********下述俩字段只在开启flash retry时才有效************/
		U8 bRetryFeatureAddr;       //retry需要设置feature时的地址，b0kb为0x89
		U8 bRetryModeCnt;           //最多支持多少种retry模式
	}T_FEATURE_SET;

	typedef struct
	{
		U8		tSLCModeFlag;			// 是否有SLC模式切换命令（1表示有，0表示没有）
		U8		tSLCDisableFlag;		// 是否有SLC disable命令（1表示有，0表示没有）
		U8		tToSLCModeCmd;			// 切换到SLC模式的命令,一般为0xDA
		U8		tSLCDisableCmd;			// 切换到TLC模式的命令，一般为0xDF
		U8      bSlcMode;				// 非0表示SLC
		U8		bL06B_TO_B0KBEn;		// L06B转B0KB
		U8   	bReserve[2];
	}T_MODE_SWITCH;	// 8 Bytes
	///////////////////////////////////////////////////////////////////////////////////////////


	// command code define here by FW
	typedef struct STR_PRI_CMD_BLOCK
	{
		//头标记
		U32 dTitle[CMD_TITILE_SIZE];
		//操作码
		U8 bOpCode;
		//子操作码
		U8 bSubOpCode;
		//SCT
		U16 wSCT;
		//保留
		U32 dReserve;
		//自定义数据
		U8 bDataDefine[CMD_DEFINE_DATA_SIZE];
		//校验和
		U32 dCheckSum;

	} PRI_CMD_BLOCK, *PPRI_CMD_BLOCK;

#pragma pack()
}
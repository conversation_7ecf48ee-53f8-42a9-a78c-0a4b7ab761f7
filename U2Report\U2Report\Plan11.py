
import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

dicPfm = {} #存放性能相关数据
g_listMPColumnName = ['Flash编号','LogCap','MP_Result','MP_Time','MPStatus','FlashID','测试PC编号','PCVersion','FWVersion','FlashName','MCUType']

g_invalid_pos = -1
mp_column_name_map = {} #量产工具低格高格测试结果的列映射关系
PFM_DATA_START_LINE = 18

FW_VERSION = '' #固件版本
MP_VERSION = '' #量产工具版本
FLASH_ID = ''  #flash的ID
FLASH_NAME = ''  #flash的型号
FLASH_MCU = '' #主控版本号
TEST_TIME = '' #测试时间

def Run(curpath, workBook, alignment):
    ws = workBook['性能测试2']
    ws.alignment = alignment
    ProPfm(curpath, ws)

def ProPfm(curpath, worksheet):
    #高格
    InitMPCsvColumnNameMapAutomatic(curpath)
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    #hdbench
    hdbKey = ['Read','Write','RRead','RWrite']
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\HDBench_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    #cdm
    cdmKey = ['SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\CDM_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    #h2testw
    h2Key = ['write speed','read speed','qa_err_msg']
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\满盘H2_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    
    #hdbench_2
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\HDBench_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_2', hdbKey, 'HDBench.bmp',0) 
    #cdm_2
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\CDM_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_2', cdmKey, 'CDM.bmp',0)
    #h2_2
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\满盘H2_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_2', h2Key, 'H2.bmp',0)
    #h2_3
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C25\\\\满盘H2_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_3', h2Key, 'H2.bmp',0)

    #写数据
    startLine = PFM_DATA_START_LINE  
    WritePfmDic(worksheet,startLine,dicPfm)
    worksheet['%s%d'%('B', 9)] = FLASH_MCU
    worksheet['%s%d'%('F', 9)] = MP_VERSION
    worksheet['%s%d'%('F', 11)] = PublicFuc.GetDate()
    PublicFuc.WriteReportTime(worksheet,'N',1)
    PublicFuc.WriteReportOperator(worksheet,'P',1)

def ReadFormatCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[mp_column_name_map['Flash编号']]
                cap = row[mp_column_name_map['LogCap']]
                mode = row[mp_column_name_map['MPStatus']]
                pcNo = row[mp_column_name_map['测试PC编号']]
                tempRow = [cap,pcNo]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                    fileMdTime = os.path.getmtime(file)
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
                else:
                    #查看是否是最新数据做覆盖处理。
                    oldTime = dataDic[key][PublicFuc.GetTimeKeyName(caseKey)]
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global FLASH_ID
                global FLASH_NAME
                global FLASH_MCU

                if FW_VERSION == '':
                    if 'FWVersion' in mp_column_name_map and mp_column_name_map['FWVersion'] >=0:
                        FW_VERSION = row[mp_column_name_map['FWVersion']]
                if MP_VERSION == '':
                    if 'PCVersion' in mp_column_name_map and mp_column_name_map['PCVersion'] >=0:
                        MP_VERSION = row[mp_column_name_map['PCVersion']]
                if FLASH_ID == '':
                    if 'FlashID' in mp_column_name_map and mp_column_name_map['FlashID'] >=0:
                        FLASH_ID = row[mp_column_name_map['FlashID']]
                if FLASH_NAME == '':
                    if 'FlashName' in mp_column_name_map and mp_column_name_map['FlashName'] >=0:
                        FLASH_NAME = row[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    if 'MCUType' in mp_column_name_map and mp_column_name_map['MCUType'] >=0:
                        FLASH_MCU = row[mp_column_name_map['MCUType']]
                #end

#绘制表格。
def InitPfmReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(dicPfm)):
        for col in range(37):
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].font = cellfont
        worksheet['%s%d'%('A', PFM_DATA_START_LINE+rowIdx)] = rowIdx+1

def WritePfmDic(worksheet, startLine, dataDic,imgWidth = 360, imgHeight = 300):
    InitPfmReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    imageStartLine = startLine + len(dataDic)+2
    imageLine = imageStartLine
    imageCol = 1
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for idx,key in enumerate(keySortLst):
        imageCol = 1
        imageLine = imageStartLine + idx*18

        worksheet['%s%d'%('B', curLine)] = key #填写flash编号

        caseKey = 'HighFormat'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['D','C']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDB_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['E','F','G','H']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'CDM_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['I','J','K','L','M','N','O','P']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'H2_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['Q','R','S']
            for index,col in enumerate(colLst):
                if col == 'S':
                    if line[index] == '':
                        worksheet['%s%d'%(col, curLine)] = 'TRUE'
                    else:
                        worksheet['%s%d'%(col, curLine)] = 'FALSE'                   
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDB_2'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['T','U','V','W']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'CDM_2'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['X','Y','Z','AA','AB','AC','AD','AE']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'H2_2'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['AF','AG','AH']
            for index,col in enumerate(colLst):
                if col == 'AH':
                    if line[index] == '':
                        worksheet['%s%d'%(col, curLine)] = 'TRUE'
                    else:
                        worksheet['%s%d'%(col, curLine)] = 'FALSE'                   
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'H2_3'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['AI','AJ','AK']
            for index,col in enumerate(colLst):
                if col == 'AK':
                    if line[index] == '':
                        worksheet['%s%d'%(col, curLine)] = 'TRUE'
                    else:
                        worksheet['%s%d'%(col, curLine)] = 'FALSE'                   
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index]

        #写图片
        worksheet['%s%d'%(get_column_letter(imageCol), imageLine)] = key #填写flash编号
        imageLine += 1
        if 'HDB_1' in dataDic[key]:
            line = dataDic[key]['HDB_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 4

        if 'CDM_1' in dataDic[key]:
            line = dataDic[key]['CDM_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'H2_1' in dataDic[key]:
            line = dataDic[key]['H2_1']
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])   
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5   

        if 'HDB_2' in dataDic[key]:
            line = dataDic[key]['HDB_2']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'CDM_2' in dataDic[key]:
            line = dataDic[key]['CDM_2']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'H2_2' in dataDic[key]:
            line = dataDic[key]['H2_2']
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])   
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5   

        if 'H2_3' in dataDic[key]:
            line = dataDic[key]['H2_3']
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])   
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5 

        curLine += 1


def ParserMPColumnName(line):
    PublicFuc.RemoveSpace(line)
    for i in g_listMPColumnName:
        pos = g_invalid_pos
        try:
            pos = line.index(i)
        except:
            pos = g_invalid_pos
        if pos == g_invalid_pos:
            return False #只要有一个名称没有找到则直接返回false
        mp_column_name_map[i] = pos
    return True

#读取文件去文件中寻找位置信息
def FindColumnPosInfo(curpath,pattern):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题

            #解析量产工具各列位置
            if ParserMPColumnName(birth_header):
                return True

    return False


def InitMPCsvColumnNameMapAutomatic(curpath):
    #自动化获取量产工具的列信息
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:  #解析到列信息就直接返回，没解析到就继续用默认内容
        return

    if len(mp_column_name_map) == 0:
        InitMPCsvColumnNameMap(mp_column_name_map)

def InitMPCsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 23
    columnNameMap['LogCap'] = 4  #开卡容量
    columnNameMap['MP_Result'] = 5 #通过Pass 否则错误码
    columnNameMap['MP_Time'] = 7 #量产时间
    columnNameMap['MPStatus'] = 13 #模式P-NS,P-HS等
    columnNameMap['FlashID'] = 16
    columnNameMap['测试PC编号'] = 20
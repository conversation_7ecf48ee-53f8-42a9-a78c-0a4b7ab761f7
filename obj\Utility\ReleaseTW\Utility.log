﻿Build started 2025/7/23 17:33:05.
     1>Project "D:\NewWorkSpace\9-AterPlan11\Utility\Utility.vcxproj" on node 4 (build target(s)).
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-Ater<PERSON>lan11\obj\Utility\ReleaseTW\Utility.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         All outputs are up-to-date.
         All outputs are up-to-date.
       ResourceCompile:
         All outputs are up-to-date.
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\Utility.dll" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib\\" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" /DEF:".\Utility.def" /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\Utility.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\Utility\Utility.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\Lib\Utility.lib" /MACHINE:X86 /DLL "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\Utility.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\commonFunction.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\IUtility.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\PrivateCmdCommunication.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\Utility.obj"
            Creating library D:\NewWorkSpace\9-AterPlan11\Lib\Utility.lib and object D:\NewWorkSpace\9-AterPlan11\Lib\Utility.exp
         Generating code
     1>d:\newworkspace\9-aterplan11\utility\commonfunction.cpp(55): warning C4715: 'DoCopyFile' : not all control paths return a value
         Finished generating code
         Utility.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\Utility.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\Utility.dll;#2" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\Utility.dll.intermediate.manifest" "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\Utility.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\Utility\ReleaseTW\Utility.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\Utility\Utility.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:01.30

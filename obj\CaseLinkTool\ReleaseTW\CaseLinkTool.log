﻿Build started 2025/7/23 17:32:45.
     1>Project "D:\NewWorkSpace\9-AterPlan11\CaseLinkTool\CaseLinkTool.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\pyinclude\include" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\pyinclude\include" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt CaseLinkTool.cpp CaseLinkToolDlg.cpp DataBaseUtility.cpp Login.cpp TestPlanParser.cpp
         CaseLinkTool.cpp
         CaseLinkToolDlg.cpp
     1>CaseLinkToolDlg.cpp(170): warning C4018: '<' : signed/unsigned mismatch
     1>CaseLinkToolDlg.cpp(173): warning C4018: '<' : signed/unsigned mismatch
         DataBaseUtility.cpp
         Login.cpp
         TestPlanParser.cpp
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(72): warning C4005: 'INT8_MIN' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(144) : see previous definition of 'INT8_MIN'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(73): warning C4005: 'INT16_MIN' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(146) : see previous definition of 'INT16_MIN'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(74): warning C4005: 'INT32_MIN' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(148) : see previous definition of 'INT32_MIN'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(76): warning C4005: 'INT8_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(167) : see previous definition of 'INT8_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(77): warning C4005: 'INT16_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(171) : see previous definition of 'INT16_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(78): warning C4005: 'INT32_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(176) : see previous definition of 'INT32_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(79): warning C4005: 'UINT8_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(168) : see previous definition of 'UINT8_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(80): warning C4005: 'UINT16_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(173) : see previous definition of 'UINT16_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(81): warning C4005: 'UINT32_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(178) : see previous definition of 'UINT32_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(149): warning C4005: 'INT64_MIN' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(152) : see previous definition of 'INT64_MIN'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(150): warning C4005: 'INT64_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(184) : see previous definition of 'INT64_MAX'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\stdint.h(151): warning C4005: 'UINT64_MAX' : macro redefinition
                 C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\intsafe.h(189) : see previous definition of 'UINT64_MAX'
     1>d:\newworkspace\9-aterplan11\include\pyinclude\include\pyhash.h(92): warning C4510: '<unnamed-tag>' : default constructor could not be generated
                 d:\newworkspace\9-aterplan11\include\pyinclude\include\pyhash.h(87) : see declaration of '<unnamed-tag>'
     1>d:\newworkspace\9-aterplan11\include\pyinclude\include\pyhash.h(92): warning C4610: struct '<unnamed-tag>' can never be instantiated - user defined constructor required
     1>TestPlanParser.cpp(40): warning C4189: 'rowCnt' : local variable is initialized but not referenced
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D NDEBUG /D _AFXDLL /l"0x0409" /I"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\\" /nologo /fo"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.res" CaseLinkTool.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\CaseLinkTool.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" LibPublicwin32.lib python38.lib LibPhyDevice.lib LibDecompression.lib json.lib /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.exe.intermediate.manifest" /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\CaseLinkTool.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\bin\CaseLinkTool.lib" /MACHINE:X86 "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkToolDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\DataBaseUtility.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\Login.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\TestPlanParser.obj"
         Generating code
         Finished generating code
     1>LibSQLOperate.lib(ISQLOperate.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibSQLOperate.lib(ISQLOperate.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\vc100.pdb'; linking object as if no debug info
     1>LibSQLOperate.lib(SQLOperate.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibSQLOperate.lib(SQLOperate.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\vc100.pdb'; linking object as if no debug info
     1>LibDecompression.lib(NewHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
         CaseLinkTool.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\CaseLinkTool.exe
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\CaseLinkTool.exe;#1" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.exe.intermediate.manifest" "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\CaseLinkTool\ReleaseTW\CaseLinkTool.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\CaseLinkTool\CaseLinkTool.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:08.30

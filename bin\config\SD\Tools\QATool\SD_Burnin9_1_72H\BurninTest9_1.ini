[TOOL]
; 工具类型：1 AS SSD, 2 CDM, 3 h2test, 4 hd tune, 5 ATTO DISK, 6 BIT
TYPE = 15

[PARAM]
;测试圈数, 0 means run forever
TEST_CYCLE = 0
;测试分钟, 0 means run forever, set 100 when it's above 100.
TEST_MINIUTES = 4320
;Select the tests to perform and the load of each test (1 = Minimum load, 100 = Maximum load)
TEST_LOAD = 100
;测试模式
;0: Default (Cyclic)
;1:Sequential data pattern (0,1,2...255)
;2:Random data with random seeking
;3:High Low freq data overwrite (10101 then 00001)
;4:Butterfly seeking
;5:Binary data pattern 1 (10101010)
;6:Binary data pattern 2 (01010101)
;7:Zeros data pattern (00000000)
;8:Ones data pattern (11111111)
;9:Random data pattern
;10:User defined test pattern
;11:Quick physical drive test, 非系统物理盘专用
;12:Physical drive read test, 物理盘专用
TEST_MODE = 0
; 文件大小
TEST_FILE_SIZE = 1.00
; 块大小,需要使用工具指定内容，否则该项失效
TEST_BLOCK_SIZE = 32
TEST_SEEK_COUNT = 100
; NA, No Threshold warning, 单位：MB/Sec
SLOW_DRIVE_THRESHOLD = NA
; Duty cycle override, leave blank to accept default
;DUTY_CYCLE_OVERRIDE = 
; Run self test and log SMART errors, 1 YES, 0 NO
RUN_SELF_TEST = 0
; Log bad sector increase, 1 YSE, 0 NO
BAD_SECTOR_INCREASE = 0
; Bad sector threshold
BAD_SECTOR_THRESHOLD = 20

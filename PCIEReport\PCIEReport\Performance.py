import PublicFuc
from openpyxl.drawing.image import Image
import csv
import matplotlib.pyplot as plt
import tempfile,os
from openpyxl.utils import get_column_letter

strPCIETempDir = os.path.join(tempfile.gettempdir(), 'pcie_report_temp')

def Run(curpath, workBook, alignment):
    ws = workBook['初始_性能测试 ']
    ws.alignment = alignment
    ProCdm(curpath, ws)
    ProCdmPlan3(curpath, ws)
    ProFormatTimeCompare(curpath, ws)
    ProAssd(curpath, ws)
    ProAtto(curpath, ws)
    ProTxBENCH(curpath, ws)
    ProAnvilsStorageUtilities(curpath, ws)
    proAIDA64_Linear_Read(curpath, ws)
    proAIDA64_Linear_Write(curpath, ws)
    proAIDA64_Average_Read(curpath, ws)
    proAIDA64_Average_Write(curpath, ws)
    proHdtune(curpath, ws)
    proHdtuneFileBase(curpath, ws)
    proPCMark8(curpath, ws)
    proPCMarkVantage(curpath, ws)
    proIometer(curpath, ws)
    
    PublicFuc.WriteReportTime(ws,'P',2)
    PublicFuc.WriteReportOperator(ws,'D',2)

def ProCdm(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SEQ1MQ8T1_Read','SEQ1MQ8T1_Write','SEQ128KQ32T1_Read','SEQ128KQ32T1_Write','RND4KQ32T16_Read','RND4KQ32T16_Write','RND4KQ1T1_Read','RND4KQ1T1_Write']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'P','G','H','I','J','K','L','M','N']
    cdmDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C28\\\\CDM测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 27
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProCdmPlan3(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SEQ1MQ1T1_Read','SEQ1MQ1T1_Write']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'P','G','H']
    cdmDic = {}
    pattern = '.+\\\\Plan3\\\\T-SS_PCIE_M2-C04\\\\CDM测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 221
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProFormatTimeCompare(curpath, worksheet):
    #写空闲盘格式化
    emptyFormatKey = ['pc_no', 'Fmt_Time']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    emptyFormatCol = ['C','B', 'D']
    emptyFormatDic = {}
    pattern = '.+\\\\Plan3\\\\T-SS_PCIE_M2-C04\\\\空盘格式化\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, emptyFormatDic, emptyFormatKey,'')
    startLine = 245
    PublicFuc.WriteData(worksheet, startLine, emptyFormatDic, emptyFormatCol, emptyFormatKey)

    #写满盘格式化
    fullFormatKey = ['pc_no', 'Fmt_Time']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    fullFormatCol = ['C','B', 'E']
    fullFormatDic = {}
    pattern = '.+\\\\Plan3\\\\T-SS_PCIE_M2-C05\\\\满盘格式化\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, fullFormatDic, fullFormatKey,'')
    startLine = 245
    PublicFuc.WriteData(worksheet, startLine, fullFormatDic, fullFormatCol, fullFormatKey)

def ProAssd(curpath, worksheet):   
    assdKey = ['pc_no', 'Cap', 'qa_err_msg','Seq Read','Seq Write','4K Read','4K Write','4K 64Thrd Read','4K 64Thrd Write','Read Acc Time','Write Acc Time','Read Score','Write Score','Total Score']
    assdCol = ['C','B','E','S','G','H','I','J','K','L','M','N','O','P','Q']
    assdDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C29\\\\AS SSD Benchmark测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 52
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)
    
def ProAtto(curpath, worksheet):
    attoKey = ['pc_no', 'Cap', 'qa_err_msg','64 MB_Write','64 MB_Read']
    attoCol = ['C','B','E','M','G','I']
    attoDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C30\\\\ATTO Disk Benchmark测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, attoDic, attoKey, 'ATTO4_0_MBps.bmp')
    PublicFuc.GetMaxOrMinValueLst(attoKey, attoDic)
    startLine = 77
    imgWidth = 250
    imgHeight = 330
    PublicFuc.WriteDataAndImage(worksheet, startLine, attoDic, attoCol, attoKey, imgWidth, imgHeight)

def WriteDataAndImageOfHdtune(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            curLine += 1
            # hdtune列表最后两项是图片路径(读和写)
            if '' != line[-2]:
                img = Image(line[-2])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                imageCol += 8
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+1
        imageLine += 1

def WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    imageCol = 1
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                 #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 8

def proHdtune(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']
    hdtuneCol = ['C','B','E','T','G','H','I','J','K','L','M','N','O','P','Q','R']
    readDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C37\\\\基准测试 for Read\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)
    writeDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C38\\\\基准测试 for Write\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, writeDic, hdtuneKey, 'HDTune.bmp', 1)
    #读写结果合并到一张表格
    rwDic = {}
    for key in readDic:
        if key not in writeDic:
            continue
        readRecord = readDic[key][0]
        writeRecord = writeDic[key][0]
        readRecord[2] += writeRecord[2]
        rwRecord = []
        for i in range(len(readRecord)):
            rwRecord.append(readRecord[i])
            #容量只需要记录一次
            if i>2:
                rwRecord.append(writeRecord[i])
        rwDic[key] = []
        rwDic[key].append(rwRecord)
    startLine = 180
    imgWidth = 450
    imgHeight = 350
    WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)
    # hdtune满盘写，物理基准写
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','Q']
    writeDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C40\\\\满盘基准测试 for Write\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, writeDic, hdtuneKey, 'HDTune.bmp', 1)
    startLine = 187
    WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, writeDic, hdtuneCol, imgWidth, imgHeight)

def proHdtuneFileBase(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','sequential read speed','sequential write speed','4kb read speed','4kb write speed','4kb queue read speed','4kb queue write speed','data mode','file length']
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','P','Q','R']
    dataDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C39\\\\满盘文件基准测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, hdtuneKey, 'HDTune.bmp', 1)

    for keyNo in dataDic:
        keySet = dataDic[keyNo]
        for line in keySet:
            if line[2] == '':
                line[2] = 'PASS'

    imgWidth = 666
    imgHeight = 666
    #WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)
    # hdtune满盘写
    startLine = 193
    WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, hdtuneCol, imgWidth, imgHeight)

def GetImagePath(strCsvFile, key):
    strPath = ''
    dataLst = []
    with open(strCsvFile, 'r', errors='ignore') as f:
        rowLst = list(csv.reader(f))
        for line in rowLst:
            if len(line) >= 14 and 'WORKER' == line[1]:
                dataLst.append(float(line[13]))
    if [] != dataLst:
        #自适应纵坐标刻度值
        maxVerticalNum = 20 #只有20个左右的刻度时纵坐标刻度才易看清楚。
        maxValue = max(dataLst)
        verticalGap = maxValue/maxVerticalNum
        candidateGap = [10,20,30,50,100,150,200,300]
        detaList = [0]*len(candidateGap)
        for idx in range(len(candidateGap)):
            detaList[idx] = abs(candidateGap[idx]-verticalGap)
        minDeta = min(detaList)
        minIdx = detaList.index(minDeta)
        targetGap = candidateGap[minIdx]

        plt.figure(figsize=(13,5))
        plt.title('%s  Phy_Seq_1M_2H(MB/s)'%key)
        xLst = [x for x in range(len(dataLst))]
        plt.plot(xLst, dataLst)
        ax = plt.gca()
        ax.xaxis.set_major_locator(plt.MultipleLocator(120))
        plt.xlim(xLst[0]-50, None) #减掉50是为了右移留空白。
        #plt.margins(x=100)
        plt.xticks(rotation=90)
        ax.yaxis.set_major_locator(plt.MultipleLocator(targetGap))
        if not os.path.exists(strPCIETempDir):
            os.mkdir(strPCIETempDir)
        strPath = os.path.join(strPCIETempDir, '%s_iometer.png'%key)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()  
    return strPath

def proIometer(curpath, worksheet):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E','R', 'G','I','K','L','M','P']
    imtKeyLst = ['pc_no', 'Cap', 'qa_err_msg', 'Seq_1M_2H_Iops', 'Seq_1M_2H_MiBps']
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C43\\\\IOmeter_Phy_Seq_1M_2H\\\\\d{14}\\\\report.ini$'
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'IOmeter.bmp', 1)
    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey, True)
    newKey = imtKeyLst+smartKeyNew
    startLine = 214
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)
    startLine += 2
    imgWidth = 1000
    imgHeight = 320
    for key in newDic:
        for line in newDic[key]:
            strPath = GetImagePath(line[-1], key)
            if '' != strPath:
                img = Image(strPath)
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, 'G%d'%startLine)
                startLine += 1


def ProTxBENCH(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap','transmission1','transmission2', 'qa_err_msg','read0','write0','read1','write1','read2','write2','read3','write3']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E','G','H', 'R','I','J','K','L','M','N','O','P']
    cdmDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C31\\\\TXBENCH测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'TxBench.bmp')
    #PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 102
    imgWidth = 510
    imgHeight = 390
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+20, cdmDic, cdmCol, cdmKey,imgWidth, imgHeight,8,2)
    #PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAnvilsStorageUtilities(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg']  #获取不到信息，因此只填关键信息，和截图
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'AO']
    cdmDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C32\\\\Anvils Storage Utilities测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'AnvilsStorage.bmp')
    #PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 127
    imgWidth = 700
    imgHeight = 500
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+20, cdmDic, cdmCol, cdmKey,imgWidth, imgHeight,10,2)
    #PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def proAIDA64_Linear_Read(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C33\\\\AIDA64 Linear Read测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 152
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol,dataKey,imgWidth, imgHeight,8,2)

def proAIDA64_Linear_Write(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C34\\\\AIDA64 Linear Write测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 159
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)

def proAIDA64_Average_Read(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C35\\\\AIDA64 Average Read测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 166
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)

def proAIDA64_Average_Write(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C36\\\\AIDA64 Average Write测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 173
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)


def proPCMark8(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','score','bandwidth']
    dataCol = ['C','B','E','K','G','I']
    dataDic = {}

    imgWidth = 450
    imgHeight = 350
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C41\\\\PCMark8测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'PCMark.bmp', 1)
    startLine = 200
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)


def proPCMarkVantage(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','score']
    dataCol = ['C','B','E','K','G']
    dataDic = {}

    imgWidth = 450
    imgHeight = 350
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C42\\\\PCMark Vantage测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'PCMarkVantage.bmp', 1)
    startLine = 207
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)
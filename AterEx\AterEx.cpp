
// AterEx.cpp : Defines the class behaviors for the application.
//

#include "stdafx.h"
#include "AterEx.h"
#include "AterExDlg.h"
#include "Dbghelp.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

#pragma comment(lib, "Dbghelp.lib")

// CAterExApp

BEGIN_MESSAGE_MAP(CAterExApp, CWinApp)
	ON_COMMAND(ID_HELP, &CWinApp::OnHelp)
END_MESSAGE_MAP()


// CAterExApp construction

CAterExApp::CAterExApp()
{
	// support Restart Manager
	m_dwRestartManagerSupportFlags = AFX_RESTART_MANAGER_SUPPORT_RESTART;

	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
}


// The one and only CAterExApp object

CAterExApp theApp;

LONG WINAPI ExceptionFilter(EXCEPTION_POINTERS* _pExcp);
// CAterExApp initialization

BOOL CAterExApp::InitInstance()
{
	SetUnhandledExceptionFilter(ExceptionFilter);
	// InitCommonControlsEx() is required on Windows XP if an application
	// manifest specifies use of ComCtl32.dll version 6 or later to enable
	// visual styles.  Otherwise, any window creation will fail.
	INITCOMMONCONTROLSEX InitCtrls;
	InitCtrls.dwSize = sizeof(InitCtrls);
	// Set this to include all the common control classes you want to use
	// in your application.
	InitCtrls.dwICC = ICC_WIN95_CLASSES;
	InitCommonControlsEx(&InitCtrls);

	CWinApp::InitInstance();

	CString strExeName = "ATEREX.exe";
	HANDLE hMutex = ::CreateMutex(NULL,TRUE,strExeName);
	if (GetLastError() == ERROR_ALREADY_EXISTS)
	{
		return FALSE;
	}

	if (!CFG_MGR->ReadBool(REBOOT_PARA, IS_AUTO_START, False))
	{	
#ifndef ATER_TW
		if (!VerifyPermission())
		{
			AfxMessageBox("Authentication failed, please contact the system administrator!");
			return FALSE;
		}
#else
		if (!LicenseVerify())
		{
			AfxMessageBox("��Ȩʧ��,��������ʧ��");
			return FALSE;
		}
#endif
	}

	AfxEnableControlContainer();

	// Create the shell manager, in case the dialog contains
	// any shell tree view or shell list view controls.
	CShellManager *pShellManager = new CShellManager;

	// Standard initialization
	// If you are not using these features and wish to reduce the size
	// of your final executable, you should remove from the following
	// the specific initialization routines you do not need
	// Change the registry key under which our settings are stored
	// TODO: You should modify this string to be something appropriate
	// such as the name of your company or organization
	SetRegistryKey(_T("Local AppWizard-Generated Applications"));

	CAterExDlg dlg;
	m_pMainWnd = &dlg;
	INT_PTR nResponse = dlg.DoModal();
	if (nResponse == IDOK)
	{
		// TODO: Place code here to handle when the dialog is
		//  dismissed with OK
	}
	else if (nResponse == IDCANCEL)
	{
		// TODO: Place code here to handle when the dialog is
		//  dismissed with Cancel
	}

	// Delete the shell manager created above.
	if (pShellManager != NULL)
	{
		delete pShellManager;
	}

	// Since the dialog has been closed, return FALSE so that we exit the
	//  application, rather than start the application's message pump.
	return FALSE;
}

BOOL CAterExApp::VerifyPermission()
{
	BOOL ret = TRUE;
	//��ȡ��̬���ļ�·��
	CString strModulePath = PublicW32::GetModulePath(NULL).c_str();

#ifdef _DEBUG
	strModulePath += "\\Dll\\PermissionsDll_d.dll";
#else
	strModulePath += "\\Dll\\PermissionsDll.dll";
#endif

	//������
	CString strFunName = "VerifyPermissionLevel";

	//���ض�̬��
	HINSTANCE hInstance = LoadLibraryEx(strModulePath, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);

	//��ȡ��Ҫ�ĺ���
	typedef BOOL (*PFunc)(BYTE level, BYTE *, char*, UINT);
	PFunc pFunc = NULL;
	if (0 != hInstance)
	{
		do 
		{
			// Ҫ��ر��߳�
			pFunc = (PFunc)GetProcAddress(hInstance, strFunName);

			if (NULL != pFunc)
			{
				const UINT uRetBufSize = 32;
				BYTE bRetBuffer[uRetBufSize] = {0};
				CString strPassword = "@admin4aterex";
				if (pFunc(0 , bRetBuffer, (char*)strPassword.GetBuffer(), strPassword.GetLength()))
				{
					BYTE bRightBuffer[uRetBufSize] = {0xaa, 0xbb, 0xcc, 0xdd, 0x00, 0x4d, 0x41, 0x43, 0x00, 0x43, 0x50, 0x55};
					if(memcmp(bRetBuffer, bRightBuffer, sizeof(BYTE)*28))
					{
						ret = FALSE;
						break;
					}

					BYTE bCheckSum = 0;
					for (UINT uByteCnt = 0; uByteCnt<uRetBufSize-1; ++uByteCnt)
					{
						bCheckSum ^= bRetBuffer[uByteCnt];
					}
					if (bCheckSum != bRetBuffer[uRetBufSize-1])
					{
						ret = FALSE;
						break;
					}
				}
				else
				{
					ret = FALSE;
					break;
				}
			}
			else
			{
				ret= FALSE;
				break;
			}

		} while (0);

		FreeLibrary(hInstance);
	}
	else
	{
		ret = FALSE;
	}

	return ret;
}

bool CAterExApp::LicenseVerify()
{
	typedef BOOL (*pApplyLicense)();

	CString strModulePath = PublicW32::GetModulePath(NULL).c_str();
	strModulePath += "\\dll\\YSLicense.dll";

	HMODULE hDll = LoadLibraryEx(strModulePath, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);
	if(NULL == hDll)
	{
		return false;
	}

	pApplyLicense pFuncApplyLicense = (pApplyLicense)GetProcAddress(hDll, "ApplyLicense");
	if (pFuncApplyLicense == NULL)
	{
		return false;
	}

	return pFuncApplyLicense();
}


LONG WINAPI ExceptionFilter(EXCEPTION_POINTERS* _pExcp) 
{   
	std::string strDumppath = PublicW32::GetModulePath() + "\\AterexException.dmp";
	HANDLE hFile = CreateFile(  
		strDumppath.c_str(),   
		GENERIC_WRITE,   
		0,   
		NULL,   
		CREATE_ALWAYS,   
		FILE_ATTRIBUTE_NORMAL,   
		NULL);  
	if (INVALID_HANDLE_VALUE == hFile)  
	{
		return EXCEPTION_CONTINUE_EXECUTION;
	}

	MINIDUMP_EXCEPTION_INFORMATION einfo = {0};  
	einfo.ThreadId = ::GetCurrentThreadId();  
	einfo.ExceptionPointers = _pExcp;  
	einfo.ClientPointers = FALSE;  

	MiniDumpWriteDump(GetCurrentProcess(),   
		GetCurrentProcessId(),   
		hFile,   
		MiniDumpWithFullMemory,   
		&einfo,   
		NULL,   
		NULL);  
	CloseHandle(hFile);   

	return   EXCEPTION_EXECUTE_HANDLER;   
}

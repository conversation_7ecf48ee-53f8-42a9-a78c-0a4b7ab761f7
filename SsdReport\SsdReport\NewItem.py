import PublicFuc

def Run(curpath, workBook, alignment):
    ws = workBook['新增测试项']
    ws.alignment = alignment
    ProMarsCase(curpath, ws)
    PublicFuc.WriteReportTime(ws,'B',39)
    PublicFuc.WriteReportOperator(ws,'F',39)

def proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt = 2):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName, recordCnt)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = PublicFuc.GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def ProMarsCase(curpath, worksheet):
    keyLst = ['pc_no','cap','result','cycle','wrdata','runtime','SmartInfo','wavg','wmax','wmin','ravg','rmax','rmin','wovertime','A5-A6']
    colLst = ['C','B','E','U','H','I','J','K','L','M','N','O','P','Q','R','T']
    #高温老化逻辑盘
    caseName = 'AT_BIT24H'
    pattern = '.+\\\\Plan53\\\\T-SS-SS-D02\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    startLine = 4
    proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, 8)

    #高温读干扰逻辑盘85℃
    colLst = ['C','B','E','V','I','J','K','S','L','M','N','O','P','Q','R','U']
    pattern = '.+\\\\Plan54\\\\T-SS-SS-C35\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'ReadDisturb'
    startLine = 16
    proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, 8)

    #高温读干扰逻辑盘70℃24H
    pattern = '.+\\\\Plan39\\\\T-SS-SS-D01\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_ReadDisturb24'
    startLine = 28
    proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, 8)
import os,sys,json
import logging,traceback,time,shutil,tempfile

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
xl = None
if len(sys.argv) > 1:
    #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
    reportPath = sys.argv[1]


#日志配置
curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'SD_Report_' + curtime + '.log')
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)
try:
    import requests
    import openpyxl
    from openpyxl.styles import Alignment
    import VideoCheckTest,MP50Times,Performance,PowerOffTest,EmptyChunk,YSEnvironmentTest,DataCoverage,MPFormat,PerformanceCompetition,Function
    import Nano,PublicFuc,BurnIn,HTLTBurnin,Retry,CardReader,MPTool48H,MPToolGivenCount,MP_H2,MP_Function,Summary,PerformanceSimple,ErrDiskInfo
    import YSMpengVideoCheckTest
    from win32com.client import Dispatch
    import urllib.parse

    templateFile = os.path.join(curpath, 'sd_report_template.xlsx') 
    templateFileOfConsumer = os.path.join(curpath, 'sd_consumer_report_template.xlsx') 
    nanoTempFile = os.path.join(curpath, 'nano_report_template.xlsx')
    competitionPfmTempFile = os.path.join(curpath, 'competition_pfm_report_template.xlsx')
    errDiksTemplateFile = os.path.join(curpath, 'err_disk_template.xlsx') 

    errorFileName = 'ErrDisk.xlsx'
    resultFileName = 'SD消费级汇总报告.xlsx'
    #resultFile = os.path.join(reportPath, 'SD汇总报告.xlsx')
    resultFileOfConsumer = os.path.join(reportPath, resultFileName)
    nanoResultFile = os.path.join(reportPath, 'Nano汇总报告.xlsx')
    competitionPfmResultFile = os.path.join(reportPath, 'SD卡竞品性能测试报告.xlsx')
    errDiskFile = os.path.join(reportPath, 'ErrDisk.txt')
    competitionResultFile = os.path.join(curpath, 'AK2703-B17-64G竞品设备兼容性测试报告-20201123.xlsx')
    resultFileOfErrDisk = os.path.join(reportPath, errorFileName)
    addpath = os.path.join(reportPath,'ManReport')
    if len(sys.argv) > 1:
        #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
        PublicFuc.MPDATA_DIR = reportPath.replace('Report','MPData')
        PublicFuc.REPORT_DIR = reportPath
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt') 
        jsonData = {}
        jsonData['token'] = '8744f400b2634eb662def3a305e77e6b53c227f5578c005d112236e428a91f90'
        jsonData['secret'] = 'SEC73004c353c4a89cb2649a3ab12513ea59b6aa496bafd6293cfd4d486e40de211'
        jsonData['atLst'] = '18312006726'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg'
        bRmsRun = True


    logging.info('程序开始运行！')
    PublicFuc.GetAllFile(reportPath)
    if os.path.exists(templateFileOfConsumer):
        wb = openpyxl.load_workbook(filename = templateFileOfConsumer)
        alignment = Alignment(horizontal='center',vertical='center')
        wbCompetition = None
        if os.path.exists(competitionResultFile):
            wbCompetition = openpyxl.load_workbook(filename = competitionResultFile)
        VideoCheckTest.Run(reportPath, wb,wbCompetition,alignment)
        Performance.Run(reportPath, wb, alignment)
        PerformanceSimple.Run(reportPath, wb, alignment)
        PowerOffTest.Run(reportPath, wb, alignment)
        EmptyChunk.Run(reportPath, wb, alignment)
        YSEnvironmentTest.Run(reportPath, wb, alignment)
        DataCoverage.Run(reportPath, wb, alignment)
        BurnIn.Run(reportPath, wb, alignment)
        HTLTBurnin.Run(reportPath, wb, alignment)
        CardReader.Run(reportPath, wb, wbCompetition,alignment)
        dataCnt = MPTool48H.Run(reportPath, wb, alignment)
        MPToolGivenCount.Run(reportPath, wb, alignment,dataCnt)
        MP_Function.Run(reportPath, wb, alignment)
        YSMpengVideoCheckTest.Run(reportPath, wb, alignment)
        wb.save(resultFileOfConsumer)
        if os.path.exists(resultFileOfConsumer):
            PublicFuc.TryRepeatOpenCloseExcelFile(resultFileOfConsumer)
            wb = openpyxl.load_workbook(filename = resultFileOfConsumer,data_only=True)
            Summary.Run(reportPath, wb, alignment)
            wb.save(resultFileOfConsumer)
    if os.path.exists(nanoTempFile):
        wb = openpyxl.load_workbook(filename = nanoTempFile)
        alignment = Alignment(horizontal='center',vertical='center')
        Nano.Run(reportPath, wb, alignment)
        wb.save(nanoResultFile)
    if os.path.exists(competitionPfmTempFile):
        wb = openpyxl.load_workbook(filename = competitionPfmTempFile)
        alignment = Alignment(horizontal='center',vertical='center')
        PerformanceCompetition.Run(reportPath, wb, alignment)
        wb.save(competitionPfmResultFile)
    #if os.path.exists(templateFile):
    #    wb = openpyxl.load_workbook(filename = templateFile)
    #    alignment = Alignment(horizontal='center',vertical='center')   
    #    MP50Times.Run(reportPath, wb, alignment)       
    #    MPFormat.Run(reportPath, wb, alignment)
    #    Retry.Run(reportPath, wb, alignment)
    #    wb.save(resultFile) 
    if os.path.exists(errDiksTemplateFile):
        wb = openpyxl.load_workbook(filename = errDiksTemplateFile)
        alignment = Alignment(horizontal='center',vertical='center')   
        ErrDiskInfo.Run(reportPath, wb, alignment)       
        wb.save(resultFileOfErrDisk)
    PublicFuc.WriteErrDiskFile(errDiskFile)
    if os.path.exists(addpath):
        excelLst = ['xls','xlsx']
        dst = resultFileOfConsumer
        xl = Dispatch("Excel.Application")
        xl.Visible = 0
        wb2 = xl.Workbooks.Open(Filename=dst,UpdateLinks=0) 
        oldPos = len(wb2.Worksheets)-1
        for src_plan in os.listdir(addpath):
            src_plan = os.path.join(addpath,src_plan)
            src_time = os.listdir(src_plan)
            src_time = sorted(src_time,reverse=True)
            src_time = os.path.join(src_plan,src_time[0])
            for src_xls in os.listdir(src_time):
                pos = src_xls.rfind('.')
                if -1 != pos:
                    suffix = src_xls[pos+1:]
                    if suffix.lower() in excelLst:
                        src_xls = os.path.join(src_time,src_xls)
                        wb1 = xl.Workbooks.Open(Filename=src_xls,UpdateLinks=0)
                        for shes in wb1.sheets: 
                            ws1 = wb1.Worksheets(shes.name)
                            ws1.Copy(wb2.Sheets[len(wb2.Worksheets)-1])
                        wb1.Close(False)
        #Copy无法复制到最后一个sheet，因此要把最后一个sheet移到原有的位置
        wb2.Sheets[len(wb2.Worksheets)-1].Move(wb2.Sheets[oldPos])
        wb2.Worksheets[0].Activate()
        wb2.Close(SaveChanges=True) 
        xl.Quit() 
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = 'eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/report/download/?nid=SD&key=%s'%enTestNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&file='%('SD',enTestNo)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFileOfErrDisk):
            tempDic = {}
            tempDic['name'] = errorFileName
            tempDic['type'] = 'error'
            tempDic['url'] = strUrl+errorFileName
            woDic['reportInfoList'].append(tempDic)
        if os.path.exists(resultFileOfConsumer):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://gateway.yeestor.com:8789/wo/report/status"
        headers = {'Content-Type': 'application/json','ORDER-NO':enTestNo}
        requests.request("POST", woUrl, headers=headers, data=json.dumps(woDic))
    del xl
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        jsonData['type'] = 'text'
        jsonData['text'] = 'eReport报告合并异常！@18312006726\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
    if None != xl:
        try:
            wb2.Close(False)
            xl.Quit()
            del xl
        except:
            xl.Quit()
            del xl


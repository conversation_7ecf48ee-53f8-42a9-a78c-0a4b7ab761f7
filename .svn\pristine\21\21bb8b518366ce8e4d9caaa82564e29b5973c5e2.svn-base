#pragma once
class IISQLOperate;
#include <StatusUpload/IStatusUpload.h>
#include <afxmt.h>

class CPcStatus
{
public:
	CPcStatus(void);
	virtual ~CPcStatus(void);

	bool UpdatePcStatus(PC_STATUS* _pStatus);

	void ReleaseResource();
	CString GetErrInfo();
protected:
	bool TryConnect();
private:
	IISQLOperate *m_pSqlOperator;

	std::string m_strErrInfo;
	bool m_bConnected;
	CCriticalSection m_csLock;
};


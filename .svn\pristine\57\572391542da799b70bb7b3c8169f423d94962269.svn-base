import os,sys,logging,traceback,time,shutil,tempfile

import openpyxl,shutil
from openpyxl.styles import Alignment

curpath = os.path.split(sys.argv[0])[0]
curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(curpath, 'EMMC_Report_' + curtime + '.log')



def GetAllSubDir(curPath):
    listDir = []
    for dirpath,dirnames,filenames in os.walk(curPath):
        planDirName = dirpath.upper()
        planDirName = planDirName[-8:]
        if planDirName != 'PLAN1000':
            continue
        for dir in dirnames:
            absolutePath = dirpath+ '\\' + dir
            if absolutePath not in listDir:
                listDir.append(absolutePath)
    return listDir

def del_file(filepath):
    """
    删除某一目录下的所有文件或文件夹
    :param filepath: 路径
    :return:
    """
    del_list = os.listdir(filepath)
    for f in del_list:
        file_path = os.path.join(filepath, f)
        if os.path.isfile(file_path):
            os.remove(file_path)
        elif os.path.isdir(file_path):
            shutil.rmtree(file_path)


#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)


try:
    logging.info('程序开始运行！')

    listSubDir = []
    listSubDir = GetAllSubDir(curpath)

    for dirPath in listSubDir:
        del_file(dirPath)

    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())

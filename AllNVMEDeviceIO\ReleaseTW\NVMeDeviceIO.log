﻿Build started 2025/7/23 17:32:45.
     1>Project "D:\NewWorkSpace\9-AterPlan11\AllNVMEDeviceIO\NVMeDeviceIO.vcxproj" on node 4 (build target(s)).
     1>InitializeBuildStatus:
         Creating "ReleaseTW\NVMeDeviceIO.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\AllNVMEDeviceIO\public" /I"D:\NewWorkSpace\9-AterPlan11\AllNVMEDeviceIO\public\CMD\SGCMD\include" /Zi /nologo /W4 /WX- /Od /Oi /Oy- /GL /D WIN32 /D NDEBUG /D _LIB /D ATER_TW /D _MBCS /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"ReleaseTW\LibNVMeDeviceIO.pch" /Fo"ReleaseTW\\" /Fd"ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\AllNVMEDeviceIO\public" /I"D:\NewWorkSpace\9-AterPlan11\AllNVMEDeviceIO\public\CMD\SGCMD\include" /Zi /nologo /W4 /WX- /Od /Oi /Oy- /GL /D WIN32 /D NDEBUG /D _LIB /D ATER_TW /D _MBCS /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"ReleaseTW\LibNVMeDeviceIO.pch" /Fo"ReleaseTW\\" /Fd"ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt baseDeviceIO\DeviceIo.cpp DeviceImport\NVMEDevice.cpp NVMEDeviceIO\NVMeDeviceIo.cpp USBNVMEDeviceIO\USB2M2DeviceIo.cpp
         DeviceIo.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         NVMEDevice.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         NVMeDeviceIo.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\..\public\NVMEPublicDef.h(394): warning C4201: nonstandard extension used : nameless struct/union
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\..\public\NVMEPublicDef.h(485): warning C4201: nonstandard extension used : nameless struct/union
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\..\public\NVMEPublicDef.h(505): warning C4201: nonstandard extension used : nameless struct/union
         USB2M2DeviceIo.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:"D:\NewWorkSpace\9-AterPlan11\Lib\LibNVMeDeviceIO.lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" /NOLOGO /LTCG ReleaseTW\DeviceIo.obj
         ReleaseTW\NVMEDevice.obj
         ReleaseTW\NVMeDeviceIo.obj
         ReleaseTW\stdafx.obj
         ReleaseTW\USB2M2DeviceIo.obj
         NVMeDeviceIO.vcxproj -> D:\NewWorkSpace\9-AterPlan11\Lib\LibNVMeDeviceIO.lib
       FinalizeBuildStatus:
         Deleting file "ReleaseTW\NVMeDeviceIO.unsuccessfulbuild".
         Touching "ReleaseTW\NVMeDeviceIO.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\AllNVMEDeviceIO\NVMeDeviceIO.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:06.36

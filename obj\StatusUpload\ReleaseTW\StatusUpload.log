﻿Build started 2025/7/23 17:32:45.
     1>Project "D:\NewWorkSpace\9-AterPlan11\StatusUpload\StatusUpload.vcxproj" on node 3 (build target(s)).
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D _USRDLL /D STATUS_EXPORTS /D ATER_TW /D _WINDLL /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D _USRDLL /D STATUS_EXPORTS /D ATER_TW /D _WINDLL /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt AutoLock.cpp commonFunction.cpp IStatusUpload.cpp PcStatus.cpp StatusUpload.cpp TestResult.cpp TestStatus.cpp UpLoadSleepReboot.cpp
         AutoLock.cpp
         commonFunction.cpp
         IStatusUpload.cpp
     1>D:\NewWorkSpace\9-AterPlan11\Include\StatusUpload\IStatusUpload.h(175): warning C4190: 'GetPcErrInfo' has C-linkage specified, but returns UDT 'ATL::CStringT<BaseType,StringTraits>' which is incompatible with C
                 with
                 [
                     BaseType=char,
                     StringTraits=StrTraitMFC_DLL<char>
                 ]
         PcStatus.cpp
     1>D:\NewWorkSpace\9-AterPlan11\Include\StatusUpload/IStatusUpload.h(175): warning C4190: 'GetPcErrInfo' has C-linkage specified, but returns UDT 'ATL::CStringT<BaseType,StringTraits>' which is incompatible with C
                 with
                 [
                     BaseType=char,
                     StringTraits=StrTraitMFC_DLL<char>
                 ]
     1>PcStatus.cpp(85): warning C4244: 'argument' : conversion from 'U64' to 'U32', possible loss of data
         StatusUpload.cpp
         TestResult.cpp
     1>D:\NewWorkSpace\9-AterPlan11\Include\StatusUpload/IStatusUpload.h(175): warning C4190: 'GetPcErrInfo' has C-linkage specified, but returns UDT 'ATL::CStringT<BaseType,StringTraits>' which is incompatible with C
                 with
                 [
                     BaseType=char,
                     StringTraits=StrTraitMFC_DLL<char>
                 ]
         TestStatus.cpp
     1>D:\NewWorkSpace\9-AterPlan11\Include\StatusUpload/IStatusUpload.h(175): warning C4190: 'GetPcErrInfo' has C-linkage specified, but returns UDT 'ATL::CStringT<BaseType,StringTraits>' which is incompatible with C
                 with
                 [
                     BaseType=char,
                     StringTraits=StrTraitMFC_DLL<char>
                 ]
         UpLoadSleepReboot.cpp
     1>D:\NewWorkSpace\9-AterPlan11\Include\StatusUpload/IStatusUpload.h(175): warning C4190: 'GetPcErrInfo' has C-linkage specified, but returns UDT 'ATL::CStringT<BaseType,StringTraits>' which is incompatible with C
                 with
                 [
                     BaseType=char,
                     StringTraits=StrTraitMFC_DLL<char>
                 ]
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D NDEBUG /D _AFXDLL /l"0x0409" /I"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\\" /nologo /fo"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.res" StatusUpload.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\StatusUpload.dll" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib\\" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" /DEF:".\StatusUpload.def" /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\StatusUpload.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\Lib\StatusUpload.lib" /MACHINE:X86 /DLL "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\AutoLock.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\commonFunction.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\IStatusUpload.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\PcStatus.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\TestResult.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\TestStatus.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\UpLoadSleepReboot.obj"
            Creating library D:\NewWorkSpace\9-AterPlan11\Lib\StatusUpload.lib and object D:\NewWorkSpace\9-AterPlan11\Lib\StatusUpload.exp
         Generating code
         Finished generating code
     1>LibSQLOperate.lib(ISQLOperate.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibSQLOperate.lib(ISQLOperate.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\vc100.pdb'; linking object as if no debug info
     1>LibSQLOperate.lib(SQLOperate.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibSQLOperate.lib(SQLOperate.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\vc100.pdb'; linking object as if no debug info
         StatusUpload.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\StatusUpload.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\StatusUpload.dll;#2" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.dll.intermediate.manifest" "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\StatusUpload\ReleaseTW\StatusUpload.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\StatusUpload\StatusUpload.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:05.35

#pragma once
#include "TCPCommunity.h"
#include "..\AutoLock.h"
#include <public.h>

class TCPTempBoxBase
{
public:
    TCPTempBoxBase();
    virtual ~TCPTempBoxBase();

    // 初始化TCP连接
    virtual BOOL InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs = 5000);
    
    // 关闭连接
    virtual void CloseConnection();
    
    // 检查是否就绪
    virtual bool IsReady();
    
    // 设置目标温度，单位：0.1度
    virtual void SetTargetTemperature(int _nVal) = 0;
    
    // 获取目标温度，单位：0.1度
    virtual int GetTargetTemperature() = 0;
    
    // 获取温度，单位：0.1度
    virtual int GetTemperature() = 0;
    
    // 是否正在运行
    virtual bool IsRunning() = 0;
    
    // 是否报警
    virtual bool IsAlarm() = 0;
    
    // 运行
    virtual bool Run() = 0;
    
    // 停止
    virtual void Stop() = 0;
    
    // 设置升降温时间，单位秒
    virtual void SetTemperatureChangeTime(int _nVal) = 0;
    
    // 获取升降温时间，单位秒
    virtual int GetTemperatureChangeTime() = 0;
    
    // 设置通讯组号，0-20
    virtual void SetTS1(int _nVal) = 0;
    
    virtual int GetTS1() = 0;
    
    // 设置运行模式0-定值，1-程式
    virtual void SetMode(int _nVal = 0) = 0;
    
    virtual int GetMode() = 0;
    
    // 获取错误码
    virtual DWORD GetErrCode();

protected:
    TCPCommunity m_tcpComm;
    CCriticalSection m_critiLock;
    DWORD m_errCode;
    bool m_bIsReady;
    std::string m_serverIP;
    UINT m_nPort;
    
    // 检查连接参数是否改变
    bool IsConnectionParamChange(const char* _pServerIP, UINT _nPort);
};
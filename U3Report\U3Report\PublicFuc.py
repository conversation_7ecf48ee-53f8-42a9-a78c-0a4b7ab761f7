import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import ErrDiskInfo
from enum import Enum

class machaine_info(Enum):
    BaseBoardManufaturer=0
    Brand=1
    CPU_Name=2
    NorthBridge=3
    SouthBridge=4
    BiosVersion=5
    BiosReleaseDate=6
    WINDOWS_VERSION=7
    BaseBoardProduct=8
    TestBoardNo=9
    PcType=10
    PcNum=11

mach_info=['BaseBoardManufaturer','Brand','CPU_Name','NorthBridge','SouthBridge','BiosVersion','BiosReleaseDate','WINDOWS_VERSION','BaseBoardProduct','TestBoardNo','PC_TYPE','PC_NUM']

minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
warnFill = PatternFill('solid', fgColor='FF0000')
maxFill = PatternFill('solid', fgColor='64C8FF')
red_font = Font(color="FF0000")
commonSmartKey = ['F1','F2','A5','A6','05','0C','A3','A4','A7','AF','B2','B5','B6','C0','C3','C4','C5','C6','C7']

MPDATA_DIR = ""
REPORT_DIR = ""

errDiskLst = []
fileLst = []
config = configparser.RawConfigParser()
CommnInfoCfg = configparser.RawConfigParser()
def GetAllFile(curpath):
    for dirpath,dirnames,filenames in os.walk(curpath):
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)


def WriteErrDiskFile(strFile):
    if 0 != len(errDiskLst):
        with open(strFile,'w+') as file: 
            for errLst in errDiskLst:
                strErr = '样片:%s    PC:%s    Err:%s    Time:%s\n'%(errLst[0],errLst[1],errLst[2],errLst[3])
                file.write(strErr)


#定义边框样式
def my_border(t_border, b_border, l_border, r_border):
    border = Border(top=Side(border_style=t_border, color=colors.BLACK),
                    bottom=Side(border_style=b_border, color=colors.BLACK),
                    left=Side(border_style=l_border, color=colors.BLACK),
                    right=Side(border_style=r_border, color=colors.BLACK))
    return border


def ReadQaIniData(curpath, pattern, dataDic, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            if caseKey not in dataDic[sec]:
                fileMdTime = os.path.getmtime(file)
                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                    dataDic[sec][tmpItemKey] = pcNo#记录当前case的电脑编号
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst
            else:
                #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
                oldTime = dataDic[sec][GetTimeKeyName(caseKey)]
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖

                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime

                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                    dataDic[sec][tmpItemKey] = pcNo#记录当前case的电脑编号
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst

            #这里获取路径和windows系统。
            if 'windows_version' not in dataDic[sec] or dataDic[sec]['windows_version'] == '':
                dataDic[sec]['windows_version'] = GetCommonInfoByFilePath(file,'WINDOWS_VERSION')

            dataDic[sec][caseKey+'_file_path'] = file
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
            dataDic[sec][tmpItemKey] = file

def ReadQaIniDataATTO(curpath, pattern, dataDic, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            if caseKey not in dataDic[sec]:
                fileMdTime = os.path.getmtime(file)
                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    pos = file.rfind('\\report')
                    imagepath = file[:pos]
                    image = ''
                    picCnt = 0
                    for i in os.listdir(imagepath):
                        if i.endswith('.bmp'):
                            image = os.path.join(imagepath,i)
                            
                            if os.path.isfile(image):
                                tempLst.append(image)
                                picCnt = picCnt + 1
                            else:
                                tempLst.append('')
                        if picCnt >= 2:
                            break
                            
                dataDic[sec][caseKey] = tempLst
            else:
                #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
                oldTime = dataDic[sec][GetTimeKeyName(caseKey)]
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖

                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime

                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    pos = file.rfind('\\report')
                    imagepath = file[:pos]
                    image = ''
                    picCnt = 0
                    for i in os.listdir(imagepath):
                        if i.endswith('.bmp'):
                            image = os.path.join(imagepath,i)
                            
                            if os.path.isfile(image):
                                tempLst.append(image)
                                picCnt = picCnt + 1
                            else:
                                tempLst.append('')
                        if picCnt >= 2:
                            break
                            
                dataDic[sec][caseKey] = tempLst

            #这里获取路径和windows系统。
            if 'windows_version' not in dataDic[sec] or dataDic[sec]['windows_version'] == '':
                dataDic[sec]['windows_version'] = GetCommonInfoByFilePath(file,'WINDOWS_VERSION')

            dataDic[sec][caseKey+'_file_path'] = file


#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName

def GetIndex(element,elementlist):
    elementIdx = -1
    try:
        elementIdx = elementlist.index(element)
    except:
        elementIdx = -1
    return elementIdx

def RemoveSpace(listData):
    for i in range(0,len(listData)):
        listData[i] = listData[i].strip()

#由文件获取指定信息
def GetCommonInfoByFilePath(file_path,info_name):
    CommnInfoCfg.clear()
    posInfo = re.search('\\\\Plan\d+\\\\T',file_path)
    if not posInfo:
        return ''

    pos = posInfo.start()
    commonCfgFile = file_path[0:pos] + '\\common_info.ini'
    
    if not os.path.exists(commonCfgFile):
        return ''

    CommnInfoCfg.read(commonCfgFile,encoding='gbk')
    if 'COMMON_INFO' not in CommnInfoCfg:
        return ''
    if info_name not in CommnInfoCfg['COMMON_INFO']:
        return ''

    infoValue = CommnInfoCfg['COMMON_INFO'][info_name]
    return infoValue

#获取当前日期
def GetDate():
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d', filemt)
    return strTime

def AppendErrDiskInfo(errTypeName,sampleID,errCode,pcNo,logFilePath):
    errDiskInfo = []
    errDiskInfo.append(sampleID)
    errDiskInfo.append(errCode)
    errDiskInfo.append(pcNo)
    errDiskInfo.append(logFilePath)
    if IsInErrDiskSet(errTypeName,sampleID) == False:
        ErrDiskInfo.g_dicErrDisk[errTypeName].append(errDiskInfo)

#判定此样本编号是否已经在错误列表中
def IsInErrDiskSet(errTypeName,sampleID):
    for errDisk in ErrDiskInfo.g_dicErrDisk[errTypeName]:
        sampleNo = errDisk[0]
        if sampleNo == sampleID:
            return True
    return False

#获取样本编号的值
def GetOrderNoFromSampleNo(_sampleNo = 'AI-JGS-001'):
    nOrderNo = -1 #无效编号
    pattern = '\d+$'
    ret = re.findall(pattern, _sampleNo)
    if ret:
        strOrder = ret[-1]
        nOrderNo = int(strOrder)
    return nOrderNo

def GetTimeKeyName(caseKey):
    timeKeyName = caseKey + '_file_time'
    return timeKeyName

#由文件获取指定信息
def GetIniInfoByFilePath(file_path,section,info_name):
    CommnInfoCfg.clear()

    commonCfgFile = file_path
    
    if not os.path.exists(commonCfgFile):
        return ''

    CommnInfoCfg.read(commonCfgFile,encoding='gbk')
    if section not in CommnInfoCfg:
        return ''
    if info_name not in CommnInfoCfg[section]:
        return ''

    infoValue = CommnInfoCfg[section][info_name]
    return infoValue


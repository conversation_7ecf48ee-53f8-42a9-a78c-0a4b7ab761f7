import PublicFuc
from datetime import datetime,timedelta
import pymssql
import time,csv,re,os
from openpyxl.styles import  Pat<PERSON><PERSON>ill,Alignment
import configparser
config = configparser.RawConfigParser()
def Run(curpath, workBook, alignment):
    ws = workBook['内部测试']
    ws.alignment = alignment
    ProIometer(curpath, ws)

    PublicFuc.WriteReportTime(ws,'Q',2)
    PublicFuc.WriteReportOperator(ws,'D',2)

def ProImtFuncCombinedWR(curpath, worksheet, pattern, imtKeyLst, startLine, lineCnt = 1):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    smartConbinedWRKey = ['F1F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E', 'P', 'K','L','M','N','O']

    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, '', 1)
    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey)
    if 'T-SS-SS-C29' in pattern:
        #C29需要将一行数据拆分成5行
        newDic = GetMultiLineDic(newDic, len(smartKeyNew))
    
    for sampleNo in newDic:
        for listItem in newDic[sampleNo]:
            listItem[len(imtKeyLst)] = str(listItem[len(imtKeyLst)]) + '/' + str(listItem[len(imtKeyLst)+1])
            del listItem[len(imtKeyLst)+1]
    
    newKey = imtKeyLst+smartConbinedWRKey
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey, lineCnt)

def GetMultiLineDic(oldDic, lenSmartData):
    newDic = {}
    for item in oldDic:
        newDic[item] = []
        #line内容如下['cap'，....,10个imt数据，smart数据]
        for line in oldDic[item]:
            otherData = line[:-(lenSmartData+12)]
            smartData = line[-lenSmartData:]
            imtData = line[-(lenSmartData+12):-lenSmartData]
            for i in range(5):
                #此处必须用深拷贝，不然会改变otherData
                newLine = otherData[:]
                newLine.append(imtData[2*i])
                newLine.append(imtData[2*i+1])
                newLine.append(imtData[-2])
                newLine.append(imtData[-1])
                newLine+=smartData
                newDic[item].append(newLine)
    return newDic

def ProImtFunc(curpath, worksheet, pattern, imtKeyLst, startLine, lineCnt = 1):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['SmartInfo', 'A5-A6']
    imtCol = ['C','B','E', 'Q', 'K','L','M','N','O','P']

    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, '', 2)
    newDic = PublicFuc.GetNewIoMeterDicEx(imtDic, len(imtKeyLst), smartKey)
    if 'T-SS_PCIE_M2-C58' in pattern:
        #C29需要将一行数据拆分成5行
        newDic = GetMultiLineDic(newDic, len(smartKeyNew))
    newKey = imtKeyLst+smartKeyNew
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey, lineCnt)

def ProIometer(curpath, worksheet):
    #Plan8结果处理
    imtC31Key = ['pc_no', 'Cap', 'qa_err_msg', '4K_Rand_96R4W_1M_Seq_4R96W_Iops', '4K_Rand_96R4W_1M_Seq_4R96W_MiBps','total_host_write','total_host_reads']
    pattern = '.+\\\\Plan8\\\\T-SS_PCIE_M2-C56\\\\IOmeter_4WR_1PD_55_FR_10IO_FS_4_1M_RRSW_24H\\\\\d{14}\\\\report.ini$'
    startLine = 26
    ProImtFunc(curpath, worksheet, pattern, imtC31Key, startLine, 2)
    #Plan9
    imtC30Key = ['pc_no','Cap','qa_err_msg','4K_Random_96R4W_Iops','4K_Random_96R4W_MiBps','total_host_write','total_host_reads']
    pattern = '.+\\\\Plan9\\\\T-SS_PCIE_M2-C57\\\\IOmeter_4R_1PD_FR_10IO_FS_RR_24H\\\\\d{14}\\\\report.ini$'
    startLine = 35
    ProImtFunc(curpath, worksheet, pattern, imtC30Key, startLine)
    #Plan10,T-SS-NV-C23结果处理 需要拆分成多行
    imtC29Key = ['pc_no','Cap','qa_err_msg','4K_Random_100W_Iops','4K_Random_100W_MiBps','4K_Random_100R_Iops','4K_Random_100R_MiBps','4K_Random_80R20W_Iops','4K_Random_80R20W_MiBps','4K_Random_50R50W_Iops','4K_Random_50R50W_MiBps','4K_Random_20R80W_Iops','4K_Random_20R80W_MiBps','total_host_write','total_host_reads']
    pattern = '.+\\\\Plan10\\\\T-SS_PCIE_M2-C58\\\\IOmeter_4WR_5PD_FR_32IO_4K_RWRR_12H\\\\\d{14}\\\\report.ini$'
    startLine = 42
    ProImtFunc(curpath, worksheet, pattern, imtC29Key, startLine,1)

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result']) 
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if '' == dic['id_a5'] or '' == dic['id_a6']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def proMarsCommon(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def ProSPORLoseDataRecoverData(curpath, worksheet, pattern,startLine):
    dataCsv = {}
    ReadSPORLoseDataRecoverCsvData(curpath,pattern,dataCsv)
    colLst = ['C','H','I','J']
    WriteSPORLoseData(worksheet, startLine,'SPOR_1sec_4k_Align4k_rnd', dataCsv, colLst)
    colLst = ['C','K','L','M']
    WriteSPORLoseData(worksheet, startLine,'SPOR_1sec_4k_Align4k_seq', dataCsv, colLst)
    colLst = ['C','N','O','P']
    WriteSPORLoseData(worksheet, startLine,'SPOR_1sec_4k_unAlign4k_rnd', dataCsv, colLst)
    colLst = ['C','Q','R','S']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_32k_Align4k_rnd', dataCsv, colLst)
    colLst = ['C','T','U','V']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_32k_Align4k_seq', dataCsv, colLst)
    colLst = ['C','W','X','Y']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_32k_unAlign4k_rnd', dataCsv, colLst)
    colLst = ['C','Z','AA','AB']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_32k_unAlign4k_seq', dataCsv, colLst)
    colLst = ['C','AC','AD','AE']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_Align4k_rnd', dataCsv, colLst)
    colLst = ['C','AF','AG','AH']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_Align4k_seq', dataCsv, colLst)
    colLst = ['C','AI','AJ','AK']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_unAlign4k_rnd', dataCsv, colLst)
    colLst = ['C','AL','AM','AN']
    WriteSPORLoseData(worksheet, startLine,'SPOR_4k_unAlign4k_seq', dataCsv, colLst)
    colLst = ['C','AO','AP','AQ']
    WriteSPORLoseData(worksheet, startLine,'SPOR_33sector_128k_Align4k_rnd', dataCsv, colLst)
    colLst = ['C','AR','AS','AT']
    WriteSPORLoseData(worksheet, startLine,'SPOR_33sector_128k_Align4k_seq', dataCsv, colLst)
    colLst = ['C','AU','AV','AW']
    WriteSPORLoseData(worksheet, startLine,'SPOR_33sector_128k_unAlign4k_rnd', dataCsv, colLst)
    colLst = ['C','AX','AY','AZ']
    WriteSPORLoseData(worksheet, startLine,'SPOR_33sector_128k_unAlign4k_seq', dataCsv, colLst)


#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteSPORLoseData(worksheet, startLine,caseName,dataDic, colLst):
    filledSampleNo = []
    curLine = startLine
    for i in range(2):
        curTxt = worksheet['%s%d'%('C', curLine)].value
        filledSampleNo.append(curTxt)
        curLine += 1

    for key in dataDic:
        if key not in filledSampleNo:
            continue
        if caseName not in dataDic[key]:
            continue
        curLine = startLine + filledSampleNo.index(key) #得到数据要填写的行数
        line = dataDic[key][caseName]
        if line == []:
            continue
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = int(line[index-1])
                worksheet['%s%d'%(col, curLine)].alignment = PublicFuc.alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue

def ReadSPORLoseDataRecoverCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue

        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName in dataDic:
            continue
       
        dataDic[keyName] = {}
        dataDic[keyName]['SPOR_1sec_4k_Align4k_rnd'] = []
        dataDic[keyName]['SPOR_1sec_4k_Align4k_seq'] = []
        dataDic[keyName]['SPOR_1sec_4k_unAlign4k_rnd'] = []
        dataDic[keyName]['SPOR_4k_32k_Align4k_rnd'] = []
        dataDic[keyName]['SPOR_4k_32k_Align4k_seq'] = []
        dataDic[keyName]['SPOR_4k_32k_unAlign4k_rnd'] = []
        dataDic[keyName]['SPOR_4k_32k_unAlign4k_seq'] = []

        dataDic[keyName]['SPOR_4k_Align4k_rnd'] = []
        dataDic[keyName]['SPOR_4k_Align4k_seq'] = []
        dataDic[keyName]['SPOR_4k_unAlign4k_rnd'] = []
        dataDic[keyName]['SPOR_4k_unAlign4k_seq'] = []

        dataDic[keyName]['SPOR_33sector_128k_Align4k_rnd'] = []
        dataDic[keyName]['SPOR_33sector_128k_Align4k_seq'] = []
        dataDic[keyName]['SPOR_33sector_128k_unAlign4k_rnd'] = []
        dataDic[keyName]['SPOR_33sector_128k_unAlign4k_seq'] = []

        pos = file.rfind('\\')
        imagepath = file[:pos]
        pos = imagepath.rfind('\\')
        imagepath = imagepath[:pos]

        #得到对应的csv文件路径
        rptIni = imagepath + "\\TotalInfo.csv"
        if not os.path.exists(rptIni):
            continue

        raw_file_data = []
        with open(rptIni,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                raw_file_data.append(row)

        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_1sec_4k_Align4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_1sec_4k_Align4k_seq')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_1sec_4k_unAlign4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_32k_Align4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_32k_Align4k_seq')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_32k_unAlign4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_32k_unAlign4k_seq')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_Align4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_Align4k_seq')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_unAlign4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_4k_unAlign4k_seq')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_33sector_128k_Align4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_33sector_128k_Align4k_seq')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_33sector_128k_unAlign4k_rnd')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'SPOR_33sector_128k_unAlign4k_seq')
        

#获取单个数据的内容
def GetSpecifiedData(dataDic,raw_file_data,caseName):
    for i in range(len(raw_file_data)):
        curRow = raw_file_data[i]
        title = curRow[0]
        if title.find(caseName) != -1:
            #找到了，提取对应的内容[掉电总次数,总丢失扇区，总丢失命令]
            if i+2 >= len(raw_file_data):
                return
            datarow = raw_file_data[i + 2]
            dataDic[caseName].append(datarow[0])
            dataDic[caseName].append(datarow[3])
            dataDic[caseName].append(datarow[2])
            return

#链接数据库的代码
def conn():
    connect = pymssql.connect('172.18.2.22','ysrms','ysrms@dj4admin','ys_rms') #服务器名,账户,密码,数据库名
    if connect:
        print("数据库连接成功!")
    else:
        print("数据库连接失败!")
    return connect
def ReadRebootInfo(test_no):
    con = conn()
    if not con:
        return None

    #继续查询
    cursor = con.cursor()   #创建一个游标对象,python里的sql语句都要通过cursor来执行
    sql = "select pc_no,sample_no,cur_cycle,test_begin_time,test_end_time,update_time from sleep_and_reboot where test_no ='" + test_no + "' and test_tool='reboot' order by test_begin_time desc"
    cursor.execute(sql)   #执行sql语句
    rows = cursor.fetchall()  #读取查询结果,
    cursor.close()   
    con.close()
    newRows = []
    if len(rows) > 8:
        newRows = rows[0:8]
    else:
        newRows = rows
    return newRows

def ReadSleepInfo(test_no):
    con = conn()
    if not con:
        return None

    #继续查询
    cursor = con.cursor()   #创建一个游标对象,python里的sql语句都要通过cursor来执行
    sql = "select pc_no,sample_no,cur_cycle,test_begin_time,test_end_time,update_time from sleep_and_reboot where test_no ='" + test_no + "' and test_tool='sleep' order by test_begin_time desc"
    cursor.execute(sql)   #执行sql语句
    rows = cursor.fetchall()  #读取查询结果,
    cursor.close()   
    con.close()
    newRows = []
    if len(rows) > 8:
        newRows = rows[0:8]
    else:
        newRows = rows
    return newRows

def WriteRebootOrSleepData(worksheet,startLine,dataList):
    curLine = startLine
    for data in dataList:
        pcNo = data[0]
        sampleNo = data[1]
        curCycle = data[2]
        beginTime = data[3]
        endTime = data[4]
        updateTime = data[5]
        testTime = 0 #测试时间
        bFinish = True
        bError = False #是否测试出错了
        if endTime == 0:
            #说明还没测完，但是要判定此电脑是否已经出问题
            bFinish = False
            testTime = updateTime - beginTime
            curtime = int(time.time())
            timeClap = curtime - updateTime
            if timeClap > 1800:
                bError = True #超过30分钟则认为出问题
        else:
            #需要计算测试时间
            testTime = endTime - beginTime
         
        hour,seconds = divmod(testTime, 3600)
        min,seconds = divmod(seconds, 60)
        fmtTime = '%d时%d分%d秒'%(hour,min,seconds)
        #fmtTime = str(timedelta(seconds=testTime))

        worksheet['%s%d'%('B', curLine)] = pcNo
        worksheet['%s%d'%('C', curLine)] = sampleNo
        worksheet['%s%d'%('J', curLine)] = curCycle
        worksheet['%s%d'%('K', curLine)] = fmtTime

        if bError:
            worksheet['%s%d'%('P', curLine)] = 'FAIL'
            worksheet['%s%d'%('P', curLine)].fill = PublicFuc.warnFill
        else:
            if endTime == 0:
                worksheet['%s%d'%('P', curLine)] = 'UNFINISH'
            else:
                worksheet['%s%d'%('P', curLine)] = 'PASS'
        curLine += 1

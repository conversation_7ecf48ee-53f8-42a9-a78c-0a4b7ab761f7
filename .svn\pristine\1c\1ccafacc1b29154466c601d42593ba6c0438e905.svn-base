import PublicFuc
from openpyxl.utils import get_column_letter

dataDic = {} #存放读取到的原始数据

def Run(curpath, workBook, wbCompetition,alignment):
    ws = workBook['兼容性长时间录像输出报告模板']
    ws.alignment = alignment
    ProYSMpegTool(curpath, ws,wbCompetition)
    PublicFuc.WriteReportTime(ws,'S',1)
    PublicFuc.WriteReportOperator(ws,'U',1)
    

def ProYSMpegTool(curpath, worksheet,wbCompetition):
     #BIT常温老化逻辑盘
    pattern = '.+\\\\Plan18\\\\T_EM_SD_C33\\\\CheckVideo\\\\\D+_\d{14}.csv$'
    startLine = 3
    
    #dataCol = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R']
    PublicFuc.ReadYSMpegToolCsvData(curpath,pattern,dataDic)

    InitReportTemplateInWorkSheet(worksheet)
    startLine = WriteData(worksheet,startLine,dataDic,wbCompetition)

#从依赖物料中解析出视频记录仪的编号
def GetDriveRecorderNo(rawNo):
    cardNo = ''
    idx = rawNo.find('DriveRecorder')
    if idx == -1:
        return cardNo
    idxStart = idx + 14

    idxEnd = rawNo.find('$',idx)
    if idx == -1:
        return cardNo

    cardNo = rawNo[idxStart:idxEnd]
    return cardNo

#1个样片多条记录
def WriteData(worksheet, startLine, dataDic,wbCompetition):
    curLine = startLine

    #rowIdx = 1
    wsCompetition = None
    if wbCompetition != None:
        wsCompetition = wbCompetition['Driving recorder(行车记录仪)']
    for key in dataDic:
        for line in dataDic[key]:
            worksheet['%s%d'%('A', curLine)] = line[0] #phyNo
            worksheet['%s%d'%('B', curLine)] = line[1] #portNo
            driveRecordNo = GetDriveRecorderNo(line[3]) #摄像头编号
            worksheet['%s%d'%('C', curLine)] = driveRecordNo
            worksheet['%s%d'%('I', curLine)] = line[2] #样片编号
            worksheet['%s%d'%('J', curLine)] = line[4] #容量
            
            worksheet['%s%d'%('L', curLine)] = line[7] #打开失败数量
            worksheet['%s%d'%('M', curLine)] = line[8] #解码失败数量
            worksheet['%s%d'%('N', curLine)] = line[10] #出错帧数比
            worksheet['%s%d'%('O', curLine)] = line[17] #文件漏录

            #写竞品数据
            if wsCompetition != None:
                competitionData = GetCompetitionData(wsCompetition,driveRecordNo)
                worksheet['%s%d'%('E', curLine)] = competitionData[0] #打开失败
                worksheet['%s%d'%('F', curLine)] = competitionData[1] #解码失败
                worksheet['%s%d'%('G', curLine)] = competitionData[2] #停录
                worksheet['%s%d'%('H', curLine)] = competitionData[3] #漏录

            #判定测试结果
            descResult = 'PASS'
            if int(line[7]) > 0 or int(line[8]) > 0 or int(line[17]) > 0:
                descResult = 'FAIL'
                worksheet['%s%d'%('K', curLine)].fill = PublicFuc.warnFill

            worksheet['%s%d'%('K', curLine)] = descResult #检测结果

            #记录是否存在其它错误信息
            audioDecodeFile = int(line[12]) #音频解码失败数量
            fileLackWarn = int(line[16]) #文件漏录警告数量
            fileException = int(line[18]) #文件漏录警告数量

            descOtherProblem = '否'
            if fileException > 0 or fileException > 0 or audioDecodeFile > 0:
                descOtherProblem = '是'
                worksheet['%s%d'%('P', curLine)].fill = PublicFuc.warnFill
                
            worksheet['%s%d'%('P', curLine)] = descOtherProblem #是否存在其它问题

            curLine += 1
            #rowIdx += 1

    return  curLine   

#获取竞品数据
def GetCompetitionData(ws,driveRecordNo):
    competitionData = ['']*4
    driveRecordNo = driveRecordNo.upper()
    driveRecordNo = RemoveSampleNoTailPart(driveRecordNo)
    for columnNo in range(13,ws.max_column):
        curDriveRecordNo = ws['%s%d'%(get_column_letter(columnNo), 11)].value
        if curDriveRecordNo != None:
            curDriveRecordNo = curDriveRecordNo.upper()
            curDriveRecordNo = RemoveSampleNoTailPart(curDriveRecordNo)
        if curDriveRecordNo == driveRecordNo:
            tmpData = ws['%s%d'%(get_column_letter(columnNo), 51)].value #打开失败的情况
            if tmpData == None:
                tmpData = ''
            competitionData[0] = tmpData
            tmpData = ws['%s%d'%(get_column_letter(columnNo), 50)].value #解码失败的情况
            if tmpData == None:
                tmpData = ''
            competitionData[1] = tmpData
            tmpData = ws['%s%d'%(get_column_letter(columnNo), 48)].value #停录
            if tmpData == None:
                tmpData = ''
            competitionData[2] = tmpData
            tmpData = ws['%s%d'%(get_column_letter(columnNo), 49)].value #漏录
            if tmpData == None:
                tmpData = ''
            competitionData[3] = tmpData
            break
    return competitionData

#去掉尾巴的子编号
def RemoveSampleNoTailPart(sampleNo):
    resultNo = sampleNo
    indexPos = sampleNo.rfind('-')
    if indexPos != -1:
        resultNo = resultNo[0:indexPos]
    return resultNo

 #按照实际数据条数生成表格样式
def InitReportTemplateInWorkSheet(worksheet):
    recordCnt = 0
    for key in dataDic:
        recordCnt += len(dataDic[key])
 
    for rowIdx in range(recordCnt):
        for col in range(16):
            worksheet['%s%d'%(get_column_letter(col+1), 3+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), 3+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')

#初始化制定区域边框为所有框线
def format_border(s_column, s_index, e_column , e_index):
    for row in tuple(sheet[s_column + str(s_index):e_column + str(e_index)]):
        for cell in row:
            cell.border = my_border('thin', 'thin', 'thin', 'thin')
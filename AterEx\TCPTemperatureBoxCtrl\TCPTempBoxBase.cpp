#include "stdafx.h"
#include "TCPTempBoxBase.h"

TCPTempBoxBase::TCPTempBoxBase()
{
    m_errCode = 0;
    m_bIsReady = false;
    m_nPort = 0;
}

TCPTempBoxBase::~TCPTempBoxBase()
{
    CloseConnection();
}

BOOL TCPTempBoxBase::InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs)
{
    CAutoLock autoLock(&m_critiLock);
    
    if (!IsConnectionParamChange(_pServerIP, _nPort) && m_bIsReady) {
        return TRUE; // 已经初始化过了，不需要重新初始化
    }
    
    if (!m_tcpComm.InitConnection(_pServerIP, _nPort, _nTimeoutMs)) {
        m_errCode = m_tcpComm.GetLastErrorCode();
        return FALSE;
    }
    
    // 初始化成功，需要保存本次初始化的参数信息
    m_serverIP = _pServerIP;
    m_nPort = _nPort;
    m_bIsReady = true; // 标识初始化完成，准备工作
    m_errCode = 0;
    
    return TRUE;
}

bool TCPTempBoxBase::IsConnectionParamChange(const char* _pServerIP, UINT _nPort)
{
    if (m_serverIP == _pServerIP && m_nPort == _nPort) {
        return false;
    }
    return true;
}

void TCPTempBoxBase::CloseConnection()
{
    CAutoLock autoLock(&m_critiLock);
    m_tcpComm.CloseConnection();
    m_bIsReady = false;
}

bool TCPTempBoxBase::IsReady()
{
    CAutoLock autoLock(&m_critiLock);
    return m_bIsReady && m_tcpComm.IsConnected();
}

DWORD TCPTempBoxBase::GetErrCode()
{
    return m_errCode;
}
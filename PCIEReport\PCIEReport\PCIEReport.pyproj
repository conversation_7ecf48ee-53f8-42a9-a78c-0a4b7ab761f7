<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>f9d7bf5d-4d72-4612-9909-be37e4906547</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>PCIEReport.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>SDReport</Name>
    <RootNamespace>SDReport</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="InnerTest.py" />
    <Compile Include="PCIEReport.py" />
    <Compile Include="Performance.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="PerformanceDingRong.py" />
    <Compile Include="PublicFuc.py" />
    <Compile Include="RebootSleepLaterPerformance.py">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="env\">
      <Id>env</Id>
      <Version>3.7</Version>
      <Description>env (Python 3.7 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <ItemGroup>
    <Content Include="requirements.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
﻿Build started 2025/7/25 11:00:08.
     1>Project "D:\NewWorkSpace\9-AterPlan11\AterEx\AterEx.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         All outputs are up-to-date.
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\pyinclude\include" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /Od /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp
         SztopsBoxCtrl.cpp
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(15): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(16): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(19): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(22): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(25): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(27): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(29): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(31): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(33): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(36): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(39): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(42): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(44): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(47): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(49): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(51): warning C4481: nonstandard extension used: override specifier 'override'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(8): warning C4480: nonstandard extension used: specifying underlying type for enum 'SZTOPS_COMMAND'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(72): warning C4244: 'argument' : conversion from 'int' to 'U16', possible loss of data
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(331): warning C4100: '_nVal' : unreferenced formal parameter
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(341): warning C4100: '_nVal' : unreferenced formal parameter
       ResourceCompile:
         All outputs are up-to-date.
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\AterEx.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" LibPublicwin32.lib python38.lib LibPhyDevice.lib LibDecompression.lib json.lib Delayimp.lib /DELAYLOAD:UDiskMap.dll /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.exe.intermediate.manifest" /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\AterEx.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\bin\AterEx.lib" /MACHINE:X86 "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterExDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AutoLock.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AutoStatus.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\BitMapButton.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\BurnInTest9_1.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\CaseAtomicPort.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\CFGMgr.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\HeaderCtrlCl.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\ListCtrlExClEdit.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\ColdStartTest.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\CommonInfo.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\cpu-z.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\CRC.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\DeviceInfoQuery.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\DialogTransarentNotify.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\EMMCConfigDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\FillMaterialNoTool.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\Format.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\h2test.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\MainDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\MultiPartitionTest1.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\pc_sleeper.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\ProErrDisk.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\PublicDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\RaidFormat.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\RebootByTool.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SATAGenManager.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SATASpeedDownDetection.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\ServerReport.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SkynetServiceCtrol.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SleepByTool.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SpeedDownDetectionDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\StatusListBox.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\TabSheet.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SztopsBoxCtrl.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\TCPCommunity.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\TCPTempBoxBase.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\TemperatureMgr.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\TestDiskChecker.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\TestPlanDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\MultiBit9Test.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\MultiIOMonkeyTest.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\PowerController.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\RAID0Format.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\RAID1Format.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\RAID5Format.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SleepWakeUp.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\VideoAutoRecord.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\tool_base.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\UDP.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\UDPTemperatureBoxCtrl.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\UpdateDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\USBConfigDlg.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\Utility.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\VTETest.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\CRC.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\PowerMgr.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SerialPort.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SerialPowerCtrl.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\SerialPowerCtrlWSD.obj"
     1>D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\CRC.obj : warning LNK4042: object specified more than once; extras ignored
         Generating code
     1>d:\newworkspace\9-aterplan11\aterex\wsdpowerbox\serialpowerctrl\serialport.cpp(222): warning C4701: potentially uninitialized local variable 'mystop' used
     1>d:\newworkspace\9-aterplan11\aterex\utility.cpp(1869): warning C4715: 'GetDiskLetter' : not all control paths return a value
     1>d:\newworkspace\9-aterplan11\aterex\maindlg.cpp(3651): warning C4715: 'MainDlg::UpdatePowerServerIP' : not all control paths return a value
     1>d:\newworkspace\9-aterplan11\aterex\maindlg.cpp(2512): warning C4706: assignment within conditional expression
     1>d:\newworkspace\9-aterplan11\aterex\maindlg.cpp(2523): warning C4706: assignment within conditional expression
         Finished generating code
     1>json.lib(json_reader.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'json.lib(json_reader.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>json.lib(json_value.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'json.lib(json_value.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>json.lib(json_writer.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'json.lib(json_writer.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(qrencode.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(qrencode.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(mmask.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(mmask.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(split.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(split.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(qrspec.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(qrspec.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(mqrspec.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(mqrspec.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(qrinput.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(qrinput.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(mask.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(mask.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(rsecc.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(rsecc.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibQRCode.lib(bitstream.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibQRCode.lib(bitstream.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibSQLOperate.lib(ISQLOperate.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibSQLOperate.lib(ISQLOperate.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibSQLOperate.lib(SQLOperate.obj) : warning LNK4099: PDB 'vc100.pdb' was not found with 'LibSQLOperate.lib(SQLOperate.obj)' or at 'D:\NewWorkSpace\9-AterPlan11\obj\AterEx\vc100.pdb'; linking object as if no debug info
     1>LibDecompression.lib(BenchCon.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ConsoleClose.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ExtractCallbackConsole.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(List.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Main.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MainAr.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OpenCallbackConsole.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PercentPrinter.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateCallbackConsole.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UserInputUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(I7zLib.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(IDecompression.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Link.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CommandLineParser.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CRC.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(IntToString.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ListFileUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MyString.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MyVector.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(NewHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StdInStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StdOutStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StringConvert.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StringToInt.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UTFConvert.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Wildcard.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DLL.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Error.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileDir.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileFind.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileIO.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileName.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MemoryLock.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PropVariant.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PropVariantConversions.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(System.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Time.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CreateCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CWrappers.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FilePathAutoRename.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileStreams.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FilterCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(InBuffer.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(InOutTempBuffer.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LimitedStreams.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LockedStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MemBlocks.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MethodId.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MethodProps.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OffsetStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OutBuffer.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OutMemStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ProgressMt.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ProgressUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StreamBinder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StreamObjects.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StreamUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(VirtThread.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BZip2Crc.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BZip2Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CopyCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CopyRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Deflate64Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DeflateRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ImplodeDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ImplodeHuffmanDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzma2Decoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzma2Encoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzma2Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzmaRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PpmdRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PpmdZip.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ShrinkDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzx86Converter.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzxDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(QuantumDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bcj2Coder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bcj2Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BcjCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BcjRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BitlDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BranchCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BranchMisc.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BranchRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ByteSwap.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DeltaFilter.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzOutWindow.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zDecode.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zEncode.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zExtract.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zFolderInStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zFolderOutStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zHandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zProperties.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zSpecStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zUpdate.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarHandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarUpdate.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipAddCommon.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipHandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipItem.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipUpdate.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CoderMixer2.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CoderMixer2MT.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DummyOutStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FindSignature.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(HandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(InStreamWithCRC.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ItemNameUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MultiStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OutStreamWithCRC.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ParseProperties.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabBlockInStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bz2Handler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DeflateProps.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(GzHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzmaHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(SplitHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(XzHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ArchiveCommandLine.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ArchiveExtractCallback.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ArchiveOpenCallback.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bench.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DefaultName.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(EnumDirItems.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Extract.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ExtractingFilePath.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LoadCodecs.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OpenArchive.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PropIDUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(SetProperties.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(SortUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TempFiles.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Update.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateAction.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateCallback.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdatePair.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateProduce.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zAesRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(RandGen.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(WzAes.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipCrypto.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipStrong.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
         AterEx.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\AterEx.exe
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\AterEx.exe;#1" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.exe.intermediate.manifest" "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\AterEx\AterEx.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:03.75

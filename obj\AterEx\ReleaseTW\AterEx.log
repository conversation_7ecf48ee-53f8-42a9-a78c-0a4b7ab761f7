﻿Build started 2025/7/24 17:10:58.
     1>Project "D:\NewWorkSpace\9-AterPlan11\AterEx\AterEx.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         All outputs are up-to-date.
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\pyinclude\include" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /Od /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\AterEx.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\AterEx\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp TCPTemperatureBoxCtrl\TCPCommunity.cpp TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp
         SztopsBoxCtrl.cpp
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPTempBoxBase.h : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(15): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(16): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(19): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(22): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(25): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(27): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(29): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(31): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(33): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(36): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(39): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(42): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(44): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(47): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(49): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(51): warning C4481: nonstandard extension used: override specifier 'override'
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(16): error C3668: 'CSztopsBoxCtrl::IsReady' : method with override specifier 'override' did not override any base class methods
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(19): error C3668: 'CSztopsBoxCtrl::SetTargetTemperature' : method with override specifier 'override' did not override any base class methods
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(22): error C3668: 'CSztopsBoxCtrl::GetTargetTemperature' : method with override specifier 'override' did not override any base class methods
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(25): error C3668: 'CSztopsBoxCtrl::GetTemperature' : method with override specifier 'override' did not override any base class methods
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(36): error C3668: 'CSztopsBoxCtrl::SetTemperatureChangeTime' : method with override specifier 'override' did not override any base class methods
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(39): error C3668: 'CSztopsBoxCtrl::GetTemperatureChangeTime' : method with override specifier 'override' did not override any base class methods
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\SztopsBoxCtrl.h(51): error C3668: 'CSztopsBoxCtrl::GetErrCode' : method with override specifier 'override' did not override any base class methods
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(7): warning C4480: nonstandard extension used: specifying underlying type for enum 'SZTOPS_COMMAND'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(51): error C2039: 'IsReady' : is not a member of 'TCPTempBoxBase'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPTempBoxBase.h(7) : see declaration of 'TCPTempBoxBase'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(71): warning C4244: 'argument' : conversion from 'int' to 'U16', possible loss of data
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(74): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(100): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(110): error C2039: 'ReceiveData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(162): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(171): error C2039: 'ReceiveData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(228): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(233): error C2039: 'ReceiveData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(265): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(291): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(300): error C2039: 'ReceiveData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(371): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(397): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\SztopsBoxCtrl.cpp(406): error C2039: 'ReceiveData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
         TCPCommunity.cpp
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(56): warning C4244: 'argument' : conversion from 'UINT' to 'u_short', possible loss of data
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(75): error C2601: 'TCPCommunity::CloseConnection' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(83): error C2039: 'IsConnected' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(84): error C2601: 'IsConnected' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(88): error C2039: 'SendData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(89): error C2601: 'SendData' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(114): error C2039: 'ReceiveData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(115): error C2601: 'ReceiveData' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(142): error C2039: 'SendRawData' : is not a member of 'TCPCommunity'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h(9) : see declaration of 'TCPCommunity'
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(143): error C2601: 'SendRawData' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(158): error C2601: 'TCPCommunity::ReceiveRawData' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(177): error C2601: 'TCPCommunity::GetLastErrorCode' : local function definitions are illegal
                 TCPTemperatureBoxCtrl\TCPCommunity.cpp(37): this line contains a '{' which has not yet been matched
     1>TCPTemperatureBoxCtrl\TCPCommunity.cpp(180): fatal error C1075: end of file found before the left brace '{' at 'TCPTemperatureBoxCtrl\TCPCommunity.cpp(37)' was matched
         TCPTempBoxBase.cpp
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPTempBoxBase.h : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPCommunity.h : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(20): error C3861: 'IsConnectionParamChange': identifier not found
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(37): error C2039: 'IsConnectionParamChange' : is not a member of 'TCPTempBoxBase'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPTempBoxBase.h(7) : see declaration of 'TCPTempBoxBase'
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(39): error C2065: 'm_serverIP' : undeclared identifier
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(39): error C2065: 'm_nPort' : undeclared identifier
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(52): error C2039: 'IsReady' : is not a member of 'TCPTempBoxBase'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPTempBoxBase.h(7) : see declaration of 'TCPTempBoxBase'
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(54): error C2065: 'm_critiLock' : undeclared identifier
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(55): error C2065: 'm_bIsReady' : undeclared identifier
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(55): error C2065: 'm_tcpComm' : undeclared identifier
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(55): error C2228: left of '.IsConnected' must have class/struct/union
                 type is ''unknown-type''
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(58): error C2039: 'GetErrCode' : is not a member of 'TCPTempBoxBase'
                 d:\newworkspace\9-aterplan11\aterex\tcptemperatureboxctrl\TCPTempBoxBase.h(7) : see declaration of 'TCPTempBoxBase'
     1>TCPTemperatureBoxCtrl\TCPTempBoxBase.cpp(60): error C2065: 'm_errCode' : undeclared identifier
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\AterEx\AterEx.vcxproj" (build target(s)) -- FAILED.

Build FAILED.

Time Elapsed 00:00:01.20

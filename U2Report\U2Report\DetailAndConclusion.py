import PublicFuc,logging
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import ErrDiskInfo


diH2VerifyData ={} #存放文件拷贝相关数据

DETAIL_DATA_START_ROW_NO = 5
mp_column_name_map = {} #量产工具低格高格测试结果的列映射关系
h2_column_name_map = {} #DUT的h2测试结果列映射关系
totalDataDic = {} #所有数据的汇总
conclusionDicByCap = {}#汇总表格数据字典

titleFill = PatternFill('solid')
contentFill = PatternFill('solid')

CONCLUSION_DATA_START_ROW_NO = 2
CONCLUSION_DATA_START_COLUMN_NO = 9

FW_VERSION = '' #主控版本
MP_VERSION = '' #量产工具版本
FLASH_ID = ''  #flash的ID
FLASH_NAME = ''  #flash的型号
FLASH_MCU = '' #主控版本号
TEST_TIME = '' #测试时间

#整个表格从左到右的数据为Format1(可能为高格或低格）+ H2_1 + H2_2 + Format2(可能为高格或低格) + H2_3 + H2_VERIFY(rention之后的校验)
def Run(curpath, workBook, alignment):
    ws = workBook['详细测试结果']
    ws.alignment = alignment

    global TEST_TIME
    TEST_TIME = PublicFuc.GetDate()


    if not ProDetailInfo(curpath, ws):
        return False

    ws = workBook['测试小结']
    ProConclusionSheet(curpath, ws)
    #ProDutH2Verify(curpath, ws)

def ProDetailInfo(curpath, worksheet):
    InitH2CsvColumnNameMap(h2_column_name_map)
    #读Plan1的数据
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C1\\\\低格\\\\.+.csv$' 
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format1_low')
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format2_high')
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C22\\\\H2-1\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_1')
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C22\\\\H2-2\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_2')
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C22\\\\H2-3\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_3')

    #读取Plan10的数据
    pattern = '.+\\\\Plan10\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format1_high')
    pattern = '.+\\\\Plan10\\\\T_GE_U2_C22\\\\H2-1\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_1')
    pattern = '.+\\\\Plan10\\\\T_GE_U2_C22\\\\H2-2\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_2')

    #读取Plan15的数据
    pattern = '.+\\\\Plan15\\\\T_GE_U2_C1\\\\低格\\\\.+.csv$' 
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format2_low')
    pattern = '.+\\\\Plan15\\\\T_GE_U2_C22\\\\H2-1\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_3')

    #读取Plan16的数据
    pattern = '.+\\\\Plan16\\\\T_GE_U2_C1\\\\低格\\\\.+.csv$' 
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format1_low')
    pattern = '.+\\\\Plan16\\\\T_GE_U2_C22\\\\H2-1\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_1')
    pattern = '.+\\\\Plan16\\\\T_GE_U2_C22\\\\H2-2\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_2')

    #读取Plan21的数据
    pattern = '.+\\\\Plan21\\\\T_GE_U2_C1\\\\低格\\\\.+.csv$' 
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format1_low')

    #读取Plan22的数据
    pattern = '.+\\\\Plan22\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$' 
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format1_high')

    #读取Plan17的数据
    pattern = '.+\\\\Plan17\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadMPRawCsvData(curpath,pattern,totalDataDic,'format2_high')
    pattern = '.+\\\\Plan17\\\\T_GE_U2_C22\\\\H2-3\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_3')

    #读取Plan4 dataretention数据
    pattern = '.+\\\\Plan4\\\\T_GE_U2_C2\\\\Data Retention校验\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2_VERIFY')

    for sampleNo in totalDataDic:
        if 'format1_low' in totalDataDic[sampleNo] and 'format1_high' in totalDataDic[sampleNo]:
            logging.info('同时存在高格+2次H2数据和低格+2次H2数据')
            return False

        if 'format2_low' in totalDataDic[sampleNo] and 'format2_high' in totalDataDic[sampleNo]:
            logging.info('同时存在高格+H2数据和低格+H2数据')
            return False

    WriteDetailData2WorkSheet(worksheet)

    return True

def IsNSMode(strMode):
    strMode.rstrip()
    tmpStr = strMode[len(strMode)-2:]
    tmpStr.upper()
    if tmpStr == "NS":
        return True
    else:
        return False

def IsHSMode(strMode):
    strMode.rstrip()
    tmpStr = strMode[len(strMode)-2:]
    tmpStr.upper()
    if tmpStr == "HS":
        return True
    else:
        return False

def ProConclusionSheet(curpath, worksheet):
    #汇总
    GetEmptyConclusionDicIndexByCap()
    InitSummaryData()
    CalcRatioOfConclusionDic()
    WriteConclusionData2WorkSheet(worksheet)
    PublicFuc.WriteReportTime(worksheet,'S',1)
    PublicFuc.WriteReportOperator(worksheet,'X',1)

#得到空的以容量为索引的记录
#def GetEmptyConclusionDicIndexByCap():
#    for key in combinedDataDic:
#        tmprow = combinedDataDic.get(key,[])
#        if tmprow == []:
#            continue

#        tmpCap = tmprow[combined_column_name_map['low_format_cap']]
#        if tmpCap != '' and tmpCap != '0' and tmpCap != 0:
#            conclusionDicByCap[tmpCap] = {'HS':[0]*13,'NS':[0]*13}
        
#        tmpCap = tmprow[combined_column_name_map['high_format_cap']]
#        if tmpCap != '' and tmpCap != '0' and tmpCap != 0:
#            conclusionDicByCap[tmpCap] = {'HS':[0]*13,'NS':[0]*13}

def PreProcessCapacityKey(_rawKey):
        newKey = 0
        if 'M' in _rawKey:
            newKey = _rawKey[:_rawKey.find('M')]
        return int(newKey)

#按照实际模式数量生成汇总表格
def InitConlusionReportTemplateInWorkSheet(worksheet):
    CapColumnCnt = len(conclusionDicByCap)
    for rowIdx in range(CONCLUSION_DATA_START_ROW_NO,CONCLUSION_DATA_START_ROW_NO+18):
        for col in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 3):
            worksheet['%s%d'%(get_column_letter(col), rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col), rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
    
    #填充表格名称
    for sampleNo in totalDataDic:
        if 'format1_low' in totalDataDic[sampleNo]:
            worksheet['%s%d'%('F', 4)] = '低格2+2次H2'
            worksheet['%s%d'%('E', 4)] = MP_VERSION
            worksheet['%s%d'%('B', 4)] = FLASH_ID
            worksheet['%s%d'%('A', 4)] = FLASH_NAME
            worksheet['%s%d'%('D', 4)] = FLASH_MCU
            worksheet['%s%d'%('G', 4)] = TEST_TIME
        if 'format1_high' in totalDataDic[sampleNo]:
            worksheet['%s%d'%('F', 4)] = '高格+2次H2'
            worksheet['%s%d'%('E', 4)] = MP_VERSION
            worksheet['%s%d'%('B', 4)] = FLASH_ID
            worksheet['%s%d'%('A', 4)] = FLASH_NAME
            worksheet['%s%d'%('D', 4)] = FLASH_MCU
            worksheet['%s%d'%('G', 4)] = TEST_TIME
        if 'format2_low' in totalDataDic[sampleNo]: 
            worksheet['%s%d'%('F', 12)] = '低格2+H2'
            worksheet['%s%d'%('E', 12)] = MP_VERSION
            worksheet['%s%d'%('B', 12)] = FLASH_ID
            worksheet['%s%d'%('A', 12)] = FLASH_NAME
            worksheet['%s%d'%('D', 12)] = FLASH_MCU
            worksheet['%s%d'%('G', 12)] = TEST_TIME
        if 'format2_high' in totalDataDic[sampleNo]:
            worksheet['%s%d'%('F', 12)] = '高格+H2'
            worksheet['%s%d'%('E', 12)] = MP_VERSION
            worksheet['%s%d'%('B', 12)] = FLASH_ID
            worksheet['%s%d'%('A', 12)] = FLASH_NAME
            worksheet['%s%d'%('D', 12)] = FLASH_MCU
            worksheet['%s%d'%('G', 12)] = TEST_TIME

    #填充表格背景填充
    titleCell=worksheet['%s%d'%(get_column_letter(8), 2)]
    tpfill = titleCell.fill
    titleFill.bgColor = tpfill.bgColor
    titleFill.fgColor = tpfill.fgColor
    titleFont= Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    
    #contentFont= Font('宋体',size=11,color=colors.BLACK,bold=False,italic=False)

    str = '%s%d'%(get_column_letter(8), 4)
    
    contentFont= Font('宋体',size=11,color=colors.BLACK,bold=False,italic=False)

    #初始化标题的字体和背景
    for columnIdx in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 3):
        for rowIdx in range(CONCLUSION_DATA_START_ROW_NO,CONCLUSION_DATA_START_ROW_NO+2):
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].fill = titleFill
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].alignment = PublicFuc.alignment
            #worksheet['%s%d'%(get_column_letter(11+columnIdx), ERR_CODE_TITLE_START_ROW_NO+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].font = titleFont

    #初始化内容字体和背景
    for rowIdx in range(CONCLUSION_DATA_START_ROW_NO+2,CONCLUSION_DATA_START_ROW_NO+18):
        contentCell=worksheet['%s%d'%(get_column_letter(8), rowIdx)]
        tmpfill = contentCell.fill
        contentFill.bgColor = tmpfill.bgColor
        contentFill.fgColor = tmpfill.fgColor
        for columnIdx in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 2):                
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].fill = contentFill          
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].alignment = PublicFuc.alignment
            if rowIdx == 5 or rowIdx == 8 or rowIdx == 11 or rowIdx == 13 or rowIdx == 16 or rowIdx == 19:
                 worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].number_format = '0.00%'

    #填充标题
    #keyList = sorted(conclusionDicByCap.keys())
    rawkeyList = conclusionDicByCap.keys()
    keyList = sorted(rawkeyList,key=PreProcessCapacityKey,reverse=True)
    i = 0
    for cap in keyList:
        tmpResult = conclusionDicByCap[cap]
        str = '%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2), CONCLUSION_DATA_START_ROW_NO)
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2), CONCLUSION_DATA_START_ROW_NO)] = cap
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2), CONCLUSION_DATA_START_ROW_NO+1)] = 'HS'
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2+1), CONCLUSION_DATA_START_ROW_NO+1)] = 'NS'
        i += 1
     
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO)] = '测试不良'
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2+1), CONCLUSION_DATA_START_ROW_NO)] = '测试总数/总良率'
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2+2), CONCLUSION_DATA_START_ROW_NO)] = '备注'

    #合并单元格
    i = 0
    for cap in keyList:
        worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+i*2, end_row=CONCLUSION_DATA_START_ROW_NO, end_column=CONCLUSION_DATA_START_COLUMN_NO+i*2+1)
        i += 1

    #合并的表头测试不良、测试总数/总良率
    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2, end_row=CONCLUSION_DATA_START_ROW_NO+1, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2)
    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+1, end_row=CONCLUSION_DATA_START_ROW_NO+1, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+1)

    #合并的表头最后一列
    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+1, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)

    #合并最后一列上部备注
    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO+2, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+9, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)
    #合并最后一列中部备注
    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO+10, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+14, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)
    #合并最后一列下部备注
    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO+15, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+17, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)

#获取MP1总pass的量产通过的数量
def GetTotalPassMP1Cnt():
    totalMPCnt = 0
    for cap in conclusionDicByCap:
        for mpMode in conclusionDicByCap[cap]:
            totalMPCnt += conclusionDicByCap[cap][mpMode][0]
    return totalMPCnt

#获取总pass的H2_1数量
def GetTotalPassH2_1_Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        for mpMode in conclusionDicByCap[cap]:
            totalH2 += conclusionDicByCap[cap][mpMode][3]
    return totalH2

#获取总pass的H2_2数量
def GetTotalPassH2_2_Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        for mpMode in conclusionDicByCap[cap]:
            totalH2 += conclusionDicByCap[cap][mpMode][6]
    return totalH2

#获取MP2总pass的量产通过的数量
def GetTotalPassMP2Cnt():
    totalMPCnt = 0
    for cap in conclusionDicByCap:
        for mpMode in conclusionDicByCap[cap]:
            totalMPCnt += conclusionDicByCap[cap][mpMode][8]
    return totalMPCnt

#获取总pass的H2_3数量
def GetTotalPassH2_3_Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        for mpMode in conclusionDicByCap[cap]:
            totalH2 += conclusionDicByCap[cap][mpMode][11]
    return totalH2

#获取总pass的H2_3数量
def GetTotalPassH2_VERIFY_Cnt():
    totalH2 = 0
    if len(conclusionDicByCap) == 0:
        #说明没有按容量来索引，很可能单独使用的这个数据，没有量产数据，那么就从原始数据中去寻找错误校验数据总和
        for sampleNo in totalDataDic:
            if 'H2_VERIFY' in totalDataDic[sampleNo]:
                line = totalDataDic[sampleNo]['H2_VERIFY']
                errcode = line[2]
                if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                    totalH2 += 1
        return totalH2
    for cap in conclusionDicByCap:
        for mpMode in conclusionDicByCap[cap]:
            totalH2 += conclusionDicByCap[cap][mpMode][14]
    return totalH2

#判断第一个是否是低格格式化
def IsFormat1_LowFormat():
    for sampleNo in totalDataDic:
        if 'format1_low' in totalDataDic[sampleNo]:
            return True
    return False

def IsFormat2_LowFormat():
    for sampleNo in totalDataDic:
        if 'format2_low' in totalDataDic[sampleNo]:
            return True
    return False

def IsFormat1_HighFormat():
    for sampleNo in totalDataDic:
        if 'format1_high' in totalDataDic[sampleNo]:
            return True
    return False

def IsFormat2_HighFormat():
    for sampleNo in totalDataDic:
        if 'format2_high' in totalDataDic[sampleNo]:
            return True
    return False

#按照实际模式数量生成汇总表格
def ConvertWorkSheetFormat(worksheet):
    CapColumnCnt = len(conclusionDicByCap)

    #初始化内容字体和背景
    for rowIdx in range(CONCLUSION_DATA_START_ROW_NO+2,CONCLUSION_DATA_START_ROW_NO+18): 
        if rowIdx == 5 or rowIdx == 8 or rowIdx == 11 or rowIdx == 13 or rowIdx == 17 or rowIdx == 19:
            for columnIdx in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 2):
                worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].number_format = 'Percent'
                     
            

def WriteConclusionData2WorkSheet(worksheet):
    #绘制excel模板
    InitConlusionReportTemplateInWorkSheet(worksheet)
    CapColumnCnt = len(conclusionDicByCap)
    #capIdx = 0
    rawkeyList = conclusionDicByCap.keys()
    keyList = sorted(rawkeyList,key=PreProcessCapacityKey,reverse=True)
    
    #写安容量和量产模式区分的汇总表格数据
    for capIdx,cap in enumerate(keyList):
        #填MP数据
        hsData = conclusionDicByCap[cap]['HS']
        for i,val in enumerate(conclusionDicByCap[cap]['HS']):
            worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*2), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val

        nsData = conclusionDicByCap[cap]['NS']
        for i,val in enumerate(conclusionDicByCap[cap]['NS']):
            worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*2+1), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val 

    #写不良率列
    totalMp1Cnt = GetTotalMP1Cnt()
    totalMp2Cnt = GetTotalMP2Cnt()
    totalH2_1_Cnt = GetTotalH2_1_Cnt()
    totalH2_2_Cnt = GetTotalH2_2_Cnt()
    totalH2_3_Cnt = GetTotalH2_3_Cnt()
    totalH2_VERIFY_Cnt = GetTotalH2_VERIFY_Cnt()
    totalPassMp1Cnt = GetTotalPassMP1Cnt()
    totalPassMp2Cnt = GetTotalPassMP2Cnt()
    totalPassH2_1_Cnt =  GetTotalPassH2_1_Cnt()
    totalPassH2_2_Cnt = GetTotalPassH2_2_Cnt()
    totalPassH2_3_Cnt = GetTotalPassH2_3_Cnt()
    totalPassH2_VERIFY_Cnt = GetTotalPassH2_VERIFY_Cnt()
    totalFailMp1Cnt = totalMp1Cnt - totalPassMp1Cnt
    totalFailMp2Cnt = totalMp2Cnt - totalPassMp2Cnt
    totalFailH2_1_Cnt =  totalH2_1_Cnt - totalPassH2_1_Cnt
    totalFailH2_2_Cnt = totalH2_2_Cnt - totalPassH2_2_Cnt
    totalFailH2_3_Cnt = totalH2_3_Cnt - totalPassH2_3_Cnt
    totalFailH2_VERIFY_Cnt = totalH2_VERIFY_Cnt - totalPassH2_VERIFY_Cnt

    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+2)] = totalFailMp1Cnt
    #worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2+1), CONCLUSION_DATA_START_ROW_NO+2)] = totalMp1Cnt
    tmpRate = ''
    if totalMp1Cnt != 0:
        tmpRate = (float(totalFailMp1Cnt)/float(totalMp1Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+3)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+4)] = 0 #固定无意义
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+5)] = totalFailH2_1_Cnt
    tmpRate = ''
    if totalH2_1_Cnt != 0:
        tmpRate = (float(totalFailH2_1_Cnt)/float(totalH2_1_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+6)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+7)] = 0 #固定无意义
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+8)] = totalFailH2_2_Cnt
    tmpRate = ''
    if totalH2_2_Cnt != 0:
        tmpRate = (float(totalFailH2_2_Cnt)/float(totalH2_2_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+9)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+10)] = totalFailMp2Cnt
    tmpRate = ''
    if totalMp2Cnt != 0:
        tmpRate = (float(totalFailMp2Cnt)/float(totalMp2Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+11)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+12)] = 0 #固定无意义
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+13)] = totalFailH2_3_Cnt
    tmpRate = ''
    if totalH2_3_Cnt != 0:
        tmpRate = (float(totalFailH2_3_Cnt)/float(totalH2_3_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+14)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+15)] = 0 #固定无意义
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+16)] = totalFailH2_VERIFY_Cnt
    tmpRate = ''
    if totalH2_VERIFY_Cnt != 0:
        tmpRate = (float(totalFailH2_VERIFY_Cnt)/float(totalH2_VERIFY_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+17)] = tmpRate

    #写总体情况列
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1 + CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+2)] = totalMp1Cnt
    tmpRate = ''
    if totalMp1Cnt != 0:
        tmpRate = (float(totalPassMp1Cnt)/float(totalMp1Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+3)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+4)] = totalH2_1_Cnt
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+5)] = totalPassH2_1_Cnt
    tmpRate = ''
    if totalH2_1_Cnt != 0:
        tmpRate = (float(totalPassH2_1_Cnt)/float(totalH2_1_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+6)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+7)] = totalH2_2_Cnt
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+8)] = totalPassH2_2_Cnt
    tmpRate = ''
    if totalH2_2_Cnt != 0:
        tmpRate = (float(totalPassH2_2_Cnt)/float(totalH2_2_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+9)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+10)] = totalMp2Cnt
    tmpRate = ''
    if totalMp2Cnt != 0:
        tmpRate = (float(totalPassMp2Cnt)/float(totalMp2Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+11)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+12)] = totalH2_3_Cnt
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+13)] = totalPassH2_3_Cnt
    tmpRate = ''
    if totalH2_3_Cnt != 0:
        tmpRate = (float(totalPassH2_3_Cnt)/float(totalH2_3_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+14)] = tmpRate
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+15)] = totalH2_VERIFY_Cnt
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+16)] = totalPassH2_VERIFY_Cnt
    tmpRate = ''
    if totalH2_VERIFY_Cnt != 0:
        tmpRate = (float(totalPassH2_VERIFY_Cnt)/float(totalH2_VERIFY_Cnt))
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO +1+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO+17)] = tmpRate

def GetTotalMP1Cnt():
    totalMPCnt = 0
    for sampleNo in totalDataDic:
        if 'format1_low' in totalDataDic[sampleNo]:
            totalMPCnt += 1
        if 'format1_high' in totalDataDic[sampleNo]:
            totalMPCnt += 1
    return totalMPCnt

def GetTotalMP2Cnt():
    totalMPCnt = 0
    for sampleNo in totalDataDic:
        if 'format2_low' in totalDataDic[sampleNo]:
            totalMPCnt += 1
        if 'format2_high' in totalDataDic[sampleNo]:
            totalMPCnt += 1
    return totalMPCnt

def GetTotalH2_1_Cnt():
    totalCnt = 0
    for sampleNo in totalDataDic:
        if 'H2_1' in totalDataDic[sampleNo]:
            if 'format1_low' in totalDataDic[sampleNo]:
                if totalDataDic[sampleNo]['format1_low'][2].upper() == 'PASS':
                    totalCnt += 1
            if 'format1_high' in totalDataDic[sampleNo]:
                if totalDataDic[sampleNo]['format1_high'][2].upper() == 'PASS':
                    totalCnt += 1

    return totalCnt

def GetTotalH2_2_Cnt():
    totalCnt = 0
    for sampleNo in totalDataDic:
        if 'H2_2' in totalDataDic[sampleNo]:
            if 'format1_low' in totalDataDic[sampleNo]:
                if totalDataDic[sampleNo]['format1_low'][2].upper() == 'PASS':
                    totalCnt += 1
            if 'format1_high' in totalDataDic[sampleNo]:
                if totalDataDic[sampleNo]['format1_high'][2].upper() == 'PASS':
                    totalCnt += 1
    return totalCnt

def GetTotalH2_3_Cnt():
    totalCnt = 0
    for sampleNo in totalDataDic:
        if 'H2_3' in totalDataDic[sampleNo]:
            if 'format2_low' in totalDataDic[sampleNo]:
                if totalDataDic[sampleNo]['format2_low'][2].upper() == 'PASS':
                    totalCnt += 1
            if 'format2_high' in totalDataDic[sampleNo]:
                if totalDataDic[sampleNo]['format2_high'][2].upper() == 'PASS':
                    totalCnt += 1
    return totalCnt

def GetTotalH2_VERIFY_Cnt():
    totalCnt = 0
    for sampleNo in totalDataDic:
        if 'H2_VERIFY' in totalDataDic[sampleNo]:
            totalCnt += 1

    return totalCnt

#计算比率
def CalcRatioOfConclusionDic():
    totalMp1Cnt = GetTotalMP1Cnt()
    totalMp2Cnt = GetTotalMP2Cnt()
    #totalH2_1_Cnt = GetTotalH2_1_Cnt()
    #totalH2_2_Cnt = GetTotalH2_2_Cnt()
    #totalH2_3_Cnt = GetTotalH2_3_Cnt()
    #totalH2_VERIFY_Cnt = GetTotalH2_VERIFY_Cnt()

    for cap in conclusionDicByCap:
        for mp_mode in conclusionDicByCap[cap]:
            #计算MP1
            curCnt = conclusionDicByCap[cap][mp_mode][0]
            tmpRate = ''
            if totalMp1Cnt != 0:
                tmpRate = (float(curCnt)/totalMp1Cnt)
            conclusionDicByCap[cap][mp_mode][1] = tmpRate

            #计算H2_1
            tmpRate = ''
            validH2OfCurMode = conclusionDicByCap[cap][mp_mode][3]
            totalH2OfCurMode = conclusionDicByCap[cap][mp_mode][2]
            if totalH2OfCurMode != 0:
                tmpRate = (float(validH2OfCurMode)/float(totalH2OfCurMode))
            conclusionDicByCap[cap][mp_mode][4] = tmpRate

            #计算H2_2
            tmpRate = ''
            validH2OfCurMode = conclusionDicByCap[cap][mp_mode][6]
            totalH2OfCurMode = conclusionDicByCap[cap][mp_mode][5]
            if totalH2OfCurMode != 0:
                tmpRate = (float(validH2OfCurMode)/float(totalH2OfCurMode))
            conclusionDicByCap[cap][mp_mode][7] = tmpRate

            #计算MP2
            curCnt = conclusionDicByCap[cap][mp_mode][8]
            tmpRate = ''
            if totalMp2Cnt != 0:
                tmpRate = (float(curCnt)/totalMp2Cnt)
            conclusionDicByCap[cap][mp_mode][9] = tmpRate

            #计算H2_3
            tmpRate = ''
            validH2OfCurMode = conclusionDicByCap[cap][mp_mode][11]
            totalH2OfCurMode = conclusionDicByCap[cap][mp_mode][10]
            if totalH2OfCurMode != 0:
                tmpRate = (float(validH2OfCurMode)/float(totalH2OfCurMode))
            conclusionDicByCap[cap][mp_mode][12] = tmpRate

            #计算H2_VERIFY
            tmpRate = ''
            validH2OfCurMode = conclusionDicByCap[cap][mp_mode][14]
            totalH2OfCurMode = conclusionDicByCap[cap][mp_mode][13]
            if totalH2OfCurMode != 0:
                tmpRate = (float(validH2OfCurMode)/float(totalH2OfCurMode))
            conclusionDicByCap[cap][mp_mode][15] = tmpRate

#判定模式是否是我们需要统计的合法模式
def IsModeValid(mode):
    if mode != 'HS' and mode != 'NS':
        return False
    else:
        return True

def InitSummaryData():
    for sampleNo in totalDataDic:
        if 'cap' not in totalDataDic[sampleNo]:
            continue#没有容量信息，无法进行匹配

        curCap = totalDataDic[sampleNo]['cap'] #获取当前数据的容量
        if curCap not in conclusionDicByCap:
            continue

        verifympMode = '' #校验使用的某次量产模式
        mpMode = '' #量产模式
        if 'format1_low' in totalDataDic[sampleNo]:
            mpMode = totalDataDic[sampleNo]['format1_low'][4].upper()
            if IsNSMode(mpMode):
                mpMode = 'NS'
            elif IsHSMode(mpMode):
                mpMode = 'HS'
            if IsModeValid(mpMode):
                verifympMode = mpMode
                errCode = totalDataDic[sampleNo]['format1_low'][2].upper()
                if errCode == 'PASS':
                    conclusionDicByCap[curCap][mpMode][0] += 1 #有效模式且不出错的才添加到量产中

        if 'format1_high' in totalDataDic[sampleNo]:
            mpMode = totalDataDic[sampleNo]['format1_high'][4].upper()
            if IsNSMode(mpMode):
                mpMode = 'NS'
            elif IsHSMode(mpMode):
                mpMode = 'HS'
            if IsModeValid(mpMode):
                verifympMode = mpMode
                errCode = totalDataDic[sampleNo]['format1_high'][2].upper()
                if errCode == 'PASS':
                    conclusionDicByCap[curCap][mpMode][0] += 1 #有效模式且不出错的才添加到量产中
        
        if 'H2_1' in totalDataDic[sampleNo] and mpMode != '' and IsModeValid(mpMode):
            lineH2 = totalDataDic[sampleNo]['H2_1']
            conclusionDicByCap[curCap][mpMode][2] += 1#总数量
            if lineH2[2].upper() == 'PASS':
                conclusionDicByCap[curCap][mpMode][3] += 1#PASS的H2数量

        if 'H2_2' in totalDataDic[sampleNo] and mpMode != '' and IsModeValid(mpMode):
            lineH2 = totalDataDic[sampleNo]['H2_2']
            conclusionDicByCap[curCap][mpMode][5] += 1#总数量
            if lineH2[2].upper() == 'PASS':
                conclusionDicByCap[curCap][mpMode][6] += 1#PASS的H2数量

        mpMode = ''
        if 'format2_low' in totalDataDic[sampleNo]:
            mpMode = totalDataDic[sampleNo]['format2_low'][4].upper()
            if IsNSMode(mpMode):
                mpMode = 'NS'
            elif IsHSMode(mpMode):
                mpMode = 'HS'
            if IsModeValid(mpMode):
                verifympMode = mpMode
                errCode = totalDataDic[sampleNo]['format2_low'][2].upper()
                if errCode == 'PASS':
                    conclusionDicByCap[curCap][mpMode][8] += 1 #有效模式且不出错的才添加到量产中

        if 'format2_high' in totalDataDic[sampleNo]:
            mpMode = totalDataDic[sampleNo]['format2_high'][4].upper()
            if IsNSMode(mpMode):
                mpMode = 'NS'
            elif IsHSMode(mpMode):
                mpMode = 'HS'
            if IsModeValid(mpMode):
                verifympMode = mpMode
                errCode = totalDataDic[sampleNo]['format2_high'][2].upper()
                if errCode == 'PASS':
                    conclusionDicByCap[curCap][mpMode][8] += 1 #有效模式且不出错的才添加到量产中

        if 'H2_3' in totalDataDic[sampleNo] and mpMode != '' and IsModeValid(mpMode):
            lineH2 = totalDataDic[sampleNo]['H2_3']
            conclusionDicByCap[curCap][mpMode][10] += 1#总数量
            if lineH2[2].upper() == 'PASS':
                conclusionDicByCap[curCap][mpMode][11] += 1#PASS的H2数量

        if 'H2_VERIFY' in totalDataDic[sampleNo] and verifympMode != '' and IsModeValid(verifympMode):
            lineH2 = totalDataDic[sampleNo]['H2_VERIFY']
            conclusionDicByCap[curCap][verifympMode][13] += 1#总数量
            if lineH2[2].upper() == 'PASS':
                conclusionDicByCap[curCap][verifympMode][14] += 1#PASS的H2数量

#得到空的以容量为索引的记录
def GetEmptyConclusionDicIndexByCap():
    for sampleNo in totalDataDic:
        if 'cap' not in totalDataDic[sampleNo]:
            continue#没有容量信息
        tmpCap = totalDataDic[sampleNo]['cap']
        if tmpCap != '' and tmpCap != '0M' and tmpCap != 0 and tmpCap != '0':
            if tmpCap in conclusionDicByCap:
                continue#已经存在，不统计
        else:
            continue #非法容量不统计

        if 'format1_low' in totalDataDic[sampleNo]:
            conclusionDicByCap[tmpCap] = {'HS':[0]*16,'NS':[0]*16}
        if 'format1_high' in totalDataDic[sampleNo]:
            conclusionDicByCap[tmpCap] = {'HS':[0]*16,'NS':[0]*16}
        if 'format2_low' in totalDataDic[sampleNo]:
            conclusionDicByCap[tmpCap] = {'HS':[0]*16,'NS':[0]*16}
        if 'format2_high' in totalDataDic[sampleNo]:
            conclusionDicByCap[tmpCap] = {'HS':[0]*16,'NS':[0]*16}

#写详细数据
def WriteDetailData2WorkSheet(worksheet):
    #绘制excel模板
    InitDetailReportTemplateInWorkSheet(worksheet)

    rowIdx = 0
    keyList = sorted(totalDataDic.keys())

    format1ColLst = ['C','D','E','F','G']
    H2_1_ColLst = ['H','I','J','K']
    H2_2_ColLst = ['L','M','N','O']
    format2ColLst = ['C','P','Q','R','S']
    H2_3_ColLst = ['T','U','V','W']
    H2_Verify_ColLst = ['X','Y','Z','AA']
    for sampleNo in keyList:
        nOrderNo = PublicFuc.GetOrderNoFromSampleNo(sampleNo)
        if nOrderNo == -1:
            continue
        rowIdx = nOrderNo - 1 #编号都是从1开始。
        worksheet['%s%d'%('A', DETAIL_DATA_START_ROW_NO+rowIdx)] = sampleNo
        if 'format1_low' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['format1_low']
            for index,col in enumerate(format1ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format1_low',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format1_low',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('低格2量产_Err',sampleNo,errcode,pcNo,file)
                    else:
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index].upper()

        if 'format1_high' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['format1_high']
            for index,col in enumerate(format1ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format1_high',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format1_high',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('高格测试_Err',sampleNo,errcode,pcNo,file)
                    else:
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index].upper()

        if 'H2_1' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['H2_1']
            for index,col in enumerate(H2_1_ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_1',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_1',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('H2_Err',sampleNo,errcode,pcNo,file)

        if 'H2_2' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['H2_2']
            for index,col in enumerate(H2_2_ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_2',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_2',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('2次H2_Err',sampleNo,errcode,pcNo,file)

        if 'format2_low' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['format2_low']
            for index,col in enumerate(format2ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format2_low',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format2_low',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('低格2量产_Err',sampleNo,errcode,pcNo,file)
                    else:
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index].upper()

        if 'format2_high' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['format2_high']
            for index,col in enumerate(format2ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format2_high',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('format2_high',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('高格测试_Err',sampleNo,errcode,pcNo,file)
                    else:
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index].upper()

        if 'H2_3' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['H2_3']
            for index,col in enumerate(H2_3_ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_3',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_3',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('H2_Err',sampleNo,errcode,pcNo,file)

        if 'H2_VERIFY' in totalDataDic[sampleNo]:
            line = totalDataDic[sampleNo]['H2_VERIFY']
            for index,col in enumerate(H2_Verify_ColLst):
                worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)] = line[index]
                if index == 2:
                    errcode = line[index]
                    if errcode.upper() != 'PASS' and errcode.upper() != 'TRUE' and errcode != '':
                        worksheet['%s%d'%(col, DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_VERIFY',ErrDiskInfo.g_pcnoKey)
                        pcNo = totalDataDic[sampleNo][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_VERIFY',ErrDiskInfo.g_filepathKey)
                        file = totalDataDic[sampleNo][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('Data Retention_Err',sampleNo,errcode,pcNo,file)

        rowIdx += 1
    PublicFuc.WriteReportTime(worksheet,'AD',1)
    PublicFuc.WriteReportOperator(worksheet,'AG',1)


def InitDetailReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('微软雅黑',size=10,color=colors.BLACK,bold=False,italic=False)

    nRowCnt = len(totalDataDic)
    if nRowCnt > 0:
        keySortLst = sorted(totalDataDic.keys(), reverse=False)
        nMaxSampleNo = PublicFuc.GetOrderNoFromSampleNo(keySortLst[-1])
        if nMaxSampleNo != -1:
            nRowCnt = nMaxSampleNo

    for rowIdx in range(nRowCnt):
        for col in range(27):
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].font = cellfont
            worksheet['%s%d'%('B', DETAIL_DATA_START_ROW_NO+rowIdx)] = FLASH_NAME

    for sampleNo in totalDataDic:
        strTitle = ''
        if 'format1_low' in totalDataDic[sampleNo]:
            strTitle += FLASH_MCU+'工具设置：'+ MP_VERSION + '\n低格2+2次H2' + '（测试日期：' + TEST_TIME + '）'
            worksheet['%s%d'%('D', 2)] = strTitle
        strTitle = ''
        if 'format1_high' in totalDataDic[sampleNo]:
            strTitle += FLASH_MCU+'工具设置：'+ MP_VERSION + '\n高格+2次H2' + '（测试日期：' + TEST_TIME + '）'
            worksheet['%s%d'%('D', 2)] = strTitle
        strTitle = ''
        if 'format2_low' in totalDataDic[sampleNo]:
            strTitle += FLASH_MCU+'工具设置：'+ MP_VERSION + '\n低格2+H2' + '（测试日期：' + TEST_TIME + '）'
            worksheet['%s%d'%('P', 2)] = strTitle
        strTitle = ''
        if 'format2_high' in totalDataDic[sampleNo]:
            strTitle += FLASH_MCU+'工具设置：'+ MP_VERSION + '\n高格+H2' + '（测试日期：' + TEST_TIME + '）'
            worksheet['%s%d'%('P', 2)] = strTitle

def ReadDUTH2CsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[h2_column_name_map['Flash编号']]
                wSpeed = row[h2_column_name_map['(H2)写速度']]
                rSpeed = row[h2_column_name_map['(H2)读速度']]
                result = row[h2_column_name_map['(H2)错误']]
                firstErr = row[h2_column_name_map['first_err_offset']]
                tempRow = [wSpeed,rSpeed,result,firstErr]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                    pcNo = row[h2_column_name_map['测试PC编号']]
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                    dataDic[key][tmpItemKey] = pcNo
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    dataDic[key][tmpItemKey] = file
                else:
                    #如果已经有数据，看是否是新数据，新数据才覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow
                    pcNo = row[h2_column_name_map['测试PC编号']]
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                    dataDic[key][tmpItemKey] = pcNo
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    dataDic[key][tmpItemKey] = file

def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 3
    columnNameMap['(H2)写速度'] = 14
    columnNameMap['(H2)读速度'] = 15
    columnNameMap['(H2)错误'] = 17 #PASS,or 错误码
    columnNameMap['first_err_offset'] = 18
    columnNameMap['测试PC编号'] = 5

def ReadMPRawCsvData(curpath,pattern,dataDic,caseKey):
    #fileIdx = 1
    csvHeaderColumn = []
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
                InitMPCsvColumnNameMap(mp_column_name_map,csvHeaderColumn)

            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[mp_column_name_map['FlashNO']]
                flashID = row[mp_column_name_map['FlashID']]
                cap = row[mp_column_name_map['LogCap']]
                errcode = row[mp_column_name_map['MP_Result']]
                mpTime = row[mp_column_name_map['MP_Time']]
                mpMode = row[mp_column_name_map['MPStatus']]
                tempRow = [flashID,cap,errcode,mpTime,mpMode]

                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global FLASH_ID
                global FLASH_NAME
                global FLASH_MCU
                if FW_VERSION == '':
                    FW_VERSION = row[mp_column_name_map['fw_version']]
                if MP_VERSION == '':
                    MP_VERSION = row[mp_column_name_map['mp_version']]
                if FLASH_ID == '':
                    FLASH_ID = flashID
                if FLASH_NAME == '':
                    FLASH_NAME = row[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    FLASH_MCU = row[mp_column_name_map['ControlName']]
                #end

                if key not in dataDic:
                    dataDic[key] = {}
                if 'cap' not in dataDic[key]:
                    dataDic[key]['cap'] = ''
                if cap != '':
                    dataDic[key]['cap'] = cap

                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                    pcNo = row[mp_column_name_map['测试PC编号']]
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                    dataDic[key][tmpItemKey] = pcNo
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    dataDic[key][tmpItemKey] = file
                else:
                    #如果已经有数据，看是否是新数据，新数据才覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow #新数据覆盖

                    pcNo = row[mp_column_name_map['测试PC编号']]
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                    dataDic[key][tmpItemKey] = pcNo
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    dataDic[key][tmpItemKey] = file

                

def InitMPCsvColumnNameMap(columnNameMap,listHeader):
    #columnNameMap['端口'] = 0
    PublicFuc.RemoveSpace(listHeader)
    if listHeader == []:
        return
    
    columnNameMap.clear()
    columnNameMap['FlashNO'] = PublicFuc.GetIndex('Flash编号',listHeader)
    columnNameMap['LogCap'] = PublicFuc.GetIndex('LogCap',listHeader)  #开卡容量
    columnNameMap['MP_Result'] = PublicFuc.GetIndex('MP_Result',listHeader) #通过Pass 否则错误码
    columnNameMap['MP_Time'] = PublicFuc.GetIndex('MP_Time',listHeader) #量产时间
    #columnNameMap['BadBlkNum'] = PublicFuc.GetIndex('BadBlkNum',listHeader)
    columnNameMap['MPStatus'] = PublicFuc.GetIndex('MPStatus',listHeader) #模式
    columnNameMap['FlashID'] = PublicFuc.GetIndex('FlashID',listHeader)
    columnNameMap['测试PC编号'] = PublicFuc.GetIndex('测试PC编号',listHeader)
    #columnNameMap['PC_Name'] = PublicFuc.GetIndex('PC_Name',listHeader)

    columnNameMap['mp_version'] = PublicFuc.GetIndex('PCVersion',listHeader) #量产工具版本
    columnNameMap['fw_version'] = PublicFuc.GetIndex('FWVersion',listHeader) #固件版本
    columnNameMap['FlashName'] = PublicFuc.GetIndex('FlashName',listHeader) #Flash型号
    columnNameMap['ControlName'] = PublicFuc.GetIndex('MCUType',listHeader) #主控

def ProDutH2Verify(curpath, worksheet):

    pattern = '.+\\\\Plan4\\\\T_GE_U2_C2\\\\Data Retention校验\\\\.+.csv$'
    ReadDUTH2VerifyCsvData(curpath,pattern,diH2VerifyData,'H2_VERIFY')
    WriteData2WorkSheet(worksheet)
    PublicFuc.WriteReportTime(worksheet,'I',1)
    PublicFuc.WriteReportOperator(worksheet,'K',1)

def ReadDUTH2VerifyCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[3]
                wSpeed = row[14]
                rSpeed = row[15]
                result = row[17]
                firstErr = row[18]
                tempRow = [wSpeed,rSpeed,result,firstErr]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow


def WriteData2WorkSheet(worksheet):
    #绘制excel模板
    InitReportTemplateInWorkSheet(worksheet)

    rowIdx = 0
    keyList = sorted(diH2VerifyData.keys())
    for sampleNo in keyList:
        sampleDic = diH2VerifyData[sampleNo]
        worksheet['%s%d'%(get_column_letter(1), DETAIL_DATA_START_ROW_NO+rowIdx)] = rowIdx+1 #编号
        worksheet['%s%d'%(get_column_letter(3), DETAIL_DATA_START_ROW_NO+rowIdx)] = sampleNo
        if 'H2_VERIFY' in sampleDic:
            tmpRow = sampleDic['H2_VERIFY']      
            for col in range(len(tmpRow)):
                worksheet['%s%d'%(get_column_letter(4+col), DETAIL_DATA_START_ROW_NO+rowIdx)] = tmpRow[col]
                if (col == 2) and tmpRow[col].upper() != 'PASS' and tmpRow[col] != '':
                    worksheet['%s%d'%(get_column_letter(4+col), DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
            rowIdx += 1

def InitReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('微软雅黑',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(diH2VerifyData)):
        for col in range(7):
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].font = cellfont
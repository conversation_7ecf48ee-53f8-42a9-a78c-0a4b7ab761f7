#pragma once
#include <Windows.h>
#include <WinSock2.h>
#include <string>

#pragma comment(lib, "ws2_32.lib")

class TCPCommunity
{
public:
    TCPCommunity();
    ~TCPCommunity();

    // 初始化TCP连接
    BOOL InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs = 5000);
    
    // 关闭连接
    void CloseConnection();
    
    // 检查连接状态
    bool IsConnected();
    
    // 发送数据（自动添加6字节帧头）
    DWORD SendData(const char* _pData, int _nDataLen);
    
    // 接收数据（自动处理6字节帧头）
    int ReceiveData(char* _pBuffer, int _nBufferSize, UINT _nTimeoutMs = 3000);
    
    // 获取最后错误码
    DWORD GetLastErrorCode();

private:
    SOCKET m_socket;
    bool m_bConnected;
    DWORD m_dwLastError;
    std::string m_serverIP;
    UINT m_nPort;
    
    // 发送原始数据
    DWORD SendRawData(const char* _pData, int _nDataLen);
    
    // 接收原始数据
    int ReceiveRawData(char* _pBuffer, int _nBufferSize, UINT _nTimeoutMs);
    
    // 初始化Winsock
    BOOL InitWinsock();
    
    // 清理Winsock
    void CleanupWinsock();
};
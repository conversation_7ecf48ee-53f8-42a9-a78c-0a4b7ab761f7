import configparser
import os,re,time,logging
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment
from datetime import datetime,timedelta
import csv

strSsdTempDir = '' #唯一的临时文件目录

testno = '' #测试单号，用于从数据库中统计睡眠和休眠信息
minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
alignmentNewLine = Alignment(horizontal='center',vertical='center',wrap_text=True)
warnFill = PatternFill('solid', fgColor='FF0000')
commonSmartKey = ['F1','F2','A5','A6','05','0C','A3','A4','A7','AF','B2','B5','B6','C0','C3','C4','C5','C6','C7']
errDiskLst = []
marsKey = ['test_result','end_por','start_por','end_id_f1','start_id_f1','end_id_f2','start_id_f2','end_circle','start_circle','end_time','start_time',\
            'average_write_vel','max_write_vel','min_write_vel','average_read_vel','max_read_vel','min_read_vel','write_overtime']
for item in commonSmartKey:
    smart = 'id_'+item.lower()
    marsKey.append(smart)

fileLst = []
config = configparser.RawConfigParser()
def GetAllFile(curpath):
    global testno
    for dirpath,dirnames,filenames in os.walk(curpath):
        if testno == '':
            idx = dirpath.find('testno_')
            if idx != -1:
                testno = dirpath[idx+len('testno_'):len(dirpath)]
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)

def WriteErrDiskFile(strFile):
    if 0 != len(errDiskLst):
        with open(strFile,'w+') as file: 
            for errLst in errDiskLst:
                strErr = '样片:%s    PC:%s    Err:%s    Time:%s\n'%(errLst[0],errLst[1],errLst[2],errLst[3])
                file.write(strErr)


def ReadQaIniData(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
            tempLst = []
            pcNo = ''
            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                    if 'qa_err_msg' == key:
                        filemt= time.localtime(os.stat(file).st_mtime)  
                        strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                        errDiskLst.append([sec,pcNo,value,strTime])
                else:
                    tempLst.append('')
            if len(dataDic[sec]) < recordCnt and [] != tempLst:
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec].append(tempLst)

def ReadMarsIniData(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])

def ReadMarsIniDataWithIniPath(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            dataDic[keyName]['ini_path'] = file
            if 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])

def WriteDataAndImage(worksheet, startLine, dataDic, colLst, keyLst, imgWidth, imgHeight):
    imageLine = startLine+20
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        #最后一行数据是经过处理获取的最值
        limitLine = dataDic[key][-1]
        for line in dataDic[key][:-1]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    try:
                        floatValue = float(line[index-1])
                        #和最值相差20%需要填充红色警告
                        if keyLst[index-1] not in minKey:
                            if floatValue <= limitLine[index-1]*0.8:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                        else:
                            if floatValue >= limitLine[index-1]*1.2:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    except:
                        continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+10
        imageLine += 1
#1个样片多条记录
def WriteData(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                        if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                            if line[index-1] >= 400:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    worksheet['%s%d'%(col, curLine)].alignment = alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += lineCnt
            startLine += 1
    return startLine
#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteDataNormal(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    sortedKeys = sorted(dataDic.keys())
    for key in sortedKeys:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                        if line[index-1] >= 400:
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                    if index-1 < len(keyLst) and keyLst[index-1] == 'result':
                        if line[index-1] != '' and line[index-1] !='TRUE':
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += lineCnt

#获取每个编号每列数据的最值
def GetMaxOrMinValueLst(keyLst,dataDic):
    for key in dataDic:
        resultLst = []
        for index,col in enumerate(keyLst):
            tempLst = [line[index] for line in dataDic[key]]
            limitData = 0
            bFirstData = True
            for data in tempLst:
                try:
                    tempData = float(data)
                    #部分列需要取最小值，例如时间等
                    if bFirstData:
                        limitData = tempData
                        bFirstData = False
                        continue
                    if col in minKey:
                        if tempData < limitData:
                            limitData = tempData
                    else:
                        if tempData > limitData:
                            limitData = tempData
                except:
                    continue
            resultLst.append(limitData)
        dataDic[key].append(resultLst)

def FmtStrHex(strHex):
    #去掉十六进制前面的多个0
    strNew = strHex.lstrip('0')
    if '' == strNew:
        strNew = '0'
    return strNew

def GetImtResultPath(strImtBmpPath, key):
    pos = strImtBmpPath.rfind('\\')
    strPath = strImtBmpPath[:pos+1] + 'inst%s_IometerResults.csv'%key
    return strPath

def GetNewIoMeterDic(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-1:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result'])
        if '' == dic['end_circle'] or '' == dic['start_circle']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['end_circle'])-int(dic['start_circle']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['max_write_vel'])
        newDic[key].append(dic['min_write_vel'])
        newDic[key].append(dic['average_read_vel'])
        newDic[key].append(dic['max_read_vel'])
        newDic[key].append(dic['min_read_vel'])
        newDic[key].append(dic['write_overtime'])
        if '' == dic['id_a5'] or '' == dic['id_a6']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def GetNewBitDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G)/F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            write = 0
            if smartLst[0] != '':
                write = (int(smartLst[0],16)*32)//1024
            read = 0
            if smartLst[1] != '':
                read = (int(smartLst[1],16)*32)//1024
            newLst.append('%d/%d'%(write,read))
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic


#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName


def ReadRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                row.append(file)
                dataDic.append(row)
import os,re,sys,logging,traceback,csv,configparser
from tkinter import font
import openpyxl
from openpyxl.styles import Alignment,Border,Side,colors,Font,PatternFill
from openpyxl.utils import get_column_letter

curpath = os.path.split(sys.argv[0])[0]
pattern = '^-?\d+_-?\d+\.htm'
titlePattern = '^\[\d+.'
errStrFlag = '''<span class="t" style="color: red;">'''
errEndFlag = '</span>'
titleFlag = '''<a class="collapse" style="color: red;" href="#">'''
titleEndFlag = '''</a>'''
rateFlag = '''Clock rate = '''
textEndFlag = '</span>'
nextTitleFlag = '''<a class="collapse"'''

dicData = {}
setPhase = set()
logname = os.path.join(curpath, 'VTEResult.log')
resultFile = os.path.join(curpath, 'VTE_Phase_Result.xlsx')
iniFile = os.path.join(curpath, 'VTETestInfo.ini')
config = configparser.RawConfigParser()

def my_border(t_border, b_border, l_border, r_border):
    border = Border(
        top=Side(border_style=t_border, color=colors.BLACK),
        bottom=Side(border_style=b_border, color=colors.BLACK),
        left=Side(border_style=l_border, color=colors.BLACK),
        right=Side(border_style=r_border, color=colors.BLACK)
        )
    return border

alignment = Alignment(horizontal='center',vertical='center',wrapText=True)
leftAlign = Alignment(horizontal='left',vertical='center',wrapText=True)
border = my_border('thin', 'thin', 'thin', 'thin')
normalFont = Font(name='Arial', size=9)
errFont = Font(name='Arial', size=9, color='FF0000')
passFont = Font(name='Arial', size=9, color=colors.BLUE)
errFill = PatternFill('solid',fgColor='FF0000')
boldFont = Font(name='Arial', size=9, bold=True)

#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)

def setCell(cell, value, align=alignment, font=normalFont):
    cell.alignment = align
    cell.border = border
    cell.font = font
    cell.value = value

def setCellFont(cell, value, font=normalFont):
    cell.font = font
    cell.value = value

def proHtml(dataLst, htmlPath):
    tempDic = {}
    sortTitleLst = []
    with open(htmlPath, encoding='utf-8') as f:
        strContent = f.read()
        startPos = 0
        tempDic = {}
        while True:
            rate = ''
            posErr = strContent.find(errStrFlag, startPos)
            if -1 == posErr:
                break
            pos1 = strContent.find(errEndFlag, posErr)
            strErr = strContent[posErr+len(errStrFlag):pos1]
            pos1 = strContent.find(nextTitleFlag, posErr)
            strText = strContent[posErr:pos1]
            pos1 = strText.find(rateFlag)
            if -1 != pos1:
                pos2 = strText.find(textEndFlag,pos1)
                rate = strText[pos1+len(rateFlag):pos2]
            posTitle = strContent.rfind(titleFlag, 0, posErr)
            if -1 == posTitle:
                startPos = posErr+1
                continue
            pos1 = strContent.find(titleEndFlag, posTitle)
            title = strContent[posTitle+len(titleFlag):pos1]
            pos1 = title.find(']')
            strTitle = title[1:pos1]
            titleDesc = title[pos1+1:].strip()
            if not re.match(titlePattern, title):
                while True:
                    posTitle = strContent.rfind(titleFlag, 0, posTitle)
                    if -1 == posTitle:
                        logging.error('%s节点找不到章节'%strTitle)
                        return False
                    title = strContent[posTitle+len(titleFlag):]
                    if re.match(titlePattern, title):
                        pos1 = title.find(']')
                        strTitle = '%s-%s'%(title[1:pos1], strTitle)
                        break
                    posTitle = posTitle-1
            if strTitle not in tempDic:
                sortTitleLst.append(strTitle)
                tempDic[strTitle] = {}
                tempDic[strTitle]['desc'] = titleDesc
                tempDic[strTitle]['data'] = []
            tempDic[strTitle]['data'].append([strErr,rate])
            startPos = posErr+1
    for title in sortTitleLst:
        strDesc = ''
        strRate = ''
        rateSet = set()
        for item in tempDic[title]['data']:
            strDesc += item[0]+'\n'
            if '' != item[1]:
                rateSet.add(item[1])
                strRate += item[1]+'\n'
        if 0 != len(rateSet):
            rateLst = list(rateSet)
            strRate = '\n'.join(rateLst)
        dataLst.append([title, tempDic[title]['desc'], strDesc, strRate])
    return True

try:
    logging.info('Start!')
    fileLst = os.listdir(curpath)
    for file in fileLst:
        if re.match(pattern, file):
            pos = file.find('_')
            phase1 = int(file[:pos])
            pos1 = file.find('.')
            phase2 = int(file[pos+1:pos1])
            setPhase.add(phase2)
            if phase1 not in dicData:
                dicData[phase1] = {}
            dicData[phase1][phase2] = []
            fullPath = os.path.join(curpath, file)
            if not proHtml(dicData[phase1][phase2], fullPath):
                os._exit(0)

    if 0 != len(dicData):
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = 'VTE_Phase_Result_Summary'
        ws.column_dimensions['A'].width = 15
        setCell(ws['A1'], 'phase1\phase2')
        wsErr = wb.create_sheet('VTE_Phase_Result_List')
        #wsErr.alignment =  Alignment(horizontal='left',vertical='center')
        #ws.alignment =  Alignment(horizontal='center',vertical='center')
        lstPhase = list(setPhase)
        lstPhase.sort()
        curCol = 2
        for phase in lstPhase:
            setCell(ws['%s1'%get_column_letter(curCol)], phase)
            curCol += 1
        lstKey = list(dicData.keys())
        lstKey.sort()
        startRow = 12
        setCell(wsErr['A%d'%(startRow)], 'phase1')
        setCell(wsErr['B%d'%(startRow)], 'phase2')
        setCell(wsErr['C%d'%(startRow)], '')
        setCell(wsErr['D%d'%(startRow)], '')
        setCell(wsErr['E%d'%(startRow)], '')
        setCell(wsErr['F%d'%(startRow)], '')
        wsErr.merge_cells(start_row=startRow, end_row=startRow, start_column=3, end_column=6)
        setCell(wsErr['C%d'%(startRow)], 'Result')
        setCell(wsErr['G%d'%(startRow)], 'Clock rate')
        wsErr.column_dimensions['A'].width = 15
        wsErr.column_dimensions['D'].width = 15
        wsErr.column_dimensions['E'].width = 50
        wsErr.column_dimensions['F'].width = 35
        wsErr.column_dimensions['G'].width = 15

        curRow = startRow+1
        wsRow = 2
        for phase1 in lstKey:
            setCell(ws['A%d'%wsRow], phase1)
            wsCol = 1
            for phase2 in lstPhase:
                if phase2 not in dicData[phase1]:
                    continue
                wsCol += 1
                phaseStartRow = curRow
                dataLst = dicData[phase1][phase2]
                if 0 == len(dataLst):
                    for i in range(7):
                        setCell(wsErr['%s%d'%(get_column_letter(i+1),curRow)], '')
                    setCell(wsErr['C%d'%(curRow)], 'PASS', font=passFont)
                    setCell(ws['%s%d'%(get_column_letter(wsCol), wsRow)], 'PASS', font=passFont)
                    curRow += 1
                else:
                    failStartRow = curRow
                    setCell(ws['%s%d'%(get_column_letter(wsCol), wsRow)], 'FAIL', font=errFont)
                    for item in dataLst:
                        for i in range(3):
                            setCell(wsErr['%s%d'%(get_column_letter(i+1),curRow)], '')
                        nCol = 4
                        for data in item:
                            if 7 != nCol:
                                setCell(wsErr['%s%d'%(get_column_letter(nCol),curRow)], data, align=leftAlign, font=errFont)
                            else:
                                setCell(wsErr['%s%d'%(get_column_letter(nCol),curRow)], data, align=leftAlign)
                            nCol+=1
                        curRow+=1
                    setCell(wsErr['C%d'%(failStartRow)], 'FAIL')
                    wsErr['C%d'%(failStartRow)].fill = errFill
                setCell(wsErr['A%d'%(phaseStartRow)], phase1)
                setCell(wsErr['B%d'%(phaseStartRow)], phase2)
            wsRow += 1
        if os.path.exists(iniFile):
            config.read(iniFile,encoding = 'gbk')
            strPath = config['info']['phaseFile']
            strPhaseName = os.path.split(strPath)[1]
            setCellFont(wsErr['A1'], '文件名', boldFont)
            setCellFont(wsErr['B1'], strPhaseName)
            setCellFont(wsErr['A2'], '节点名称', boldFont)
            setCellFont(wsErr['B2'], config['info']['phaseNode'])
            setCellFont(wsErr['A3'], 'Phase1名称', boldFont)
            setCellFont(wsErr['B3'], config['info']['phase1'])
            setCellFont(wsErr['A4'], 'Phase2名称', boldFont)
            setCellFont(wsErr['B4'], config['info']['phase2'])
            setCellFont(wsErr['A5'], 'Phase1区间', boldFont)
            setCellFont(wsErr['B5'], '%s 至 %s'%(config['info']['phase1_min'],config['info']['phase1_max']))
            setCellFont(wsErr['A6'], 'Phase1步长', boldFont)
            setCellFont(wsErr['B6'], config['info']['phase1_step'])
            setCellFont(wsErr['A7'], 'Phase2区间', boldFont)
            setCellFont(wsErr['B7'], '%s 至 %s'%(config['info']['phase2_min'],config['info']['phase2_max']))
            setCellFont(wsErr['A8'], 'Phase2步长', boldFont)
            setCellFont(wsErr['B8'], config['info']['phase2_step'])
            setCellFont(wsErr['A9'], '脚本名称', boldFont)
            setCellFont(wsErr['B9'], os.path.split(config['info']['script'])[1])
            setCellFont(wsErr['A10'], '样片编号', boldFont)
            setCellFont(wsErr['B10'], config['info']['diskNo'])
        wb.save(resultFile)
    logging.info('End!')
except:
    logging.info('Error!')
    strErr = traceback.format_exc()
    logging.error(strErr)
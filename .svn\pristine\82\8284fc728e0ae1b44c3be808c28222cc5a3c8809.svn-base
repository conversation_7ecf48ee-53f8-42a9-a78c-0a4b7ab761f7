#pragma  once
#include "TCPTempBoxBase.h"
#include <public.h>
#include <Windows.h>
#include <string>

class CSztopsBoxCtrl : public TCPTempBoxBase
{
public:
	CSztopsBoxCtrl();

	~CSztopsBoxCtrl();

	BOOL InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs = 5000);
	void CloseConnection() override;
	bool IsReady() override;

	//设置目标温度,单位：0.1度
	void SetTargetTemperature(int _nVal) override;

	//获取目标温度,单位：0.1度
	int GetTargetTemperature() override;

	//获取温度，单位：0.1度
	int GetTemperature() override;

	bool IsRunning() override;//是否正在运行

	bool IsAlarm() override;//是否报警

	bool Run() override;

	void Stop() override;

	//设置升降温时间，单位秒
	void SetTemperatureChangeTime(int _nVal) override;

	//获取升降温时间，单位秒
	int GetTemperatureChangeTime() override;

	//设置通讯组号,0-20
	void SetTS1(int _nVal) override;

	int GetTS1() override;

	//设置运行模式0-定值，1-程式
	void SetMode(int _nVal = 0) override;

	int GetMode() override;

	DWORD GetErrCode() override;

private:
	// 移除了CRC相关方法和串口相关成员变量
};

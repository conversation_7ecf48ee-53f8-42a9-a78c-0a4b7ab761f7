﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseTW|Win32">
      <Configuration>ReleaseTW</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseTW|x64">
      <Configuration>ReleaseTW</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{637A7694-7B15-4A39-BE84-18EEA02FCBDA}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Device</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.18362.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)Lib\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)Lib\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IntDir>$(SolutionDir)output\obj\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IntDir>$(SolutionDir)output\obj\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <TargetName>LibDeviceIO_d</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>LibDeviceIO_d</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)lib\</OutDir>
    <IntDir>$(SolutionDir)output\obj\$(ProjectName)\$(Configuration)\</IntDir>
    <TargetName>LibDeviceIO</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'">
    <OutDir>$(SolutionDir)lib\</OutDir>
    <IntDir>$(SolutionDir)output\obj\$(ProjectName)\$(Configuration)\</IntDir>
    <TargetName>LibDeviceIO</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)Lib\x64\</OutDir>
    <IntDir>$(SolutionDir)output\obj\$(ProjectName)\$(Configuration)\</IntDir>
    <TargetName>LibDeviceIO</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'">
    <OutDir>$(SolutionDir)Lib\x64\</OutDir>
    <IntDir>$(SolutionDir)output\obj\$(ProjectName)\$(Configuration)\</IntDir>
    <TargetName>LibDeviceIO</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\Device\Dev;$(SolutionDir)Include\Device\Include;$(SolutionDir)Lib\Include;$(SolutionDir)Lib\Dev</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <MinimalRebuild>false</MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Lib\Include;$(SolutionDir)Lib\Dev;$(SolutionDir)Public\Device\Lib;$(SolutionDir)Public\Device\Lib\Include;$(SolutionDir)Public\Device\Lib\Dev</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <MinimalRebuild>false</MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\Device\Dev;$(SolutionDir)Include\Device\Include;$(SolutionDir)Lib\Include;$(SolutionDir)Lib\Dev</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <AdditionalDependencies>
      </AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;ATER_TW;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\Device\Dev;$(SolutionDir)Include\Device\Include;$(SolutionDir)Lib\Include;$(SolutionDir)Lib\Dev</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <AdditionalDependencies>
      </AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Lib\Include;$(SolutionDir)Lib\Dev</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <AdditionalDependencies>
      </AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Lib\Include;$(SolutionDir)Lib\Dev</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <AdditionalDependencies>
      </AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\Lib\Include\该目录说明.txt" />
    <None Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Include\Device\Dev\Device.h" />
    <ClInclude Include="..\Lib\Dev\define.h" />
    <ClInclude Include="..\Lib\Dev\Device.h" />
    <ClInclude Include="..\Lib\Dev\pcie\pcie.h" />
    <ClInclude Include="..\Lib\Dev\ssd\ssd.h" />
    <ClInclude Include="..\Lib\Dev\usb\usb.h" />
    <ClInclude Include="..\Lib\Include\public.h" />
    <ClInclude Include="..\Lib\Include\TypeDef.h" />
    <ClInclude Include="define.h" />
    <ClInclude Include="DeviceIo.h" />
    <ClInclude Include="NVMeDeviceIo\NVMeDeviceIo\define.h" />
    <ClInclude Include="NVMeDeviceIo\NVMeDeviceIo\NVMeDeviceIo.h" />
    <ClInclude Include="NVMeDeviceIo\USB2M2DeviceIo\USB2M2DeviceIo.h" />
    <ClInclude Include="SATADeviceIo\AtaDeviceIo\AtaDeviceIo.h" />
    <ClInclude Include="SATADeviceIo\AtaDeviceIo\AtaDirectDeviceIo\AtaDirectDeviceIo.h" />
    <ClInclude Include="SATADeviceIo\SATADeviceIo.h" />
    <ClInclude Include="SATADeviceIo\ScsiDeviceIo\ScsiDeviceIo.h" />
    <ClInclude Include="SATADeviceIo\ScsiDeviceIo\ScsiDirectDeviceIo\ScsiDirectDeviceIo.h" />
    <ClInclude Include="SATADeviceIo\TranslateBuff\TranslateBuff.h" />
    <ClInclude Include="USBDevieIo\EmmcDeviceIo\EMMCDeviceIo.h" />
    <ClInclude Include="USBDevieIo\SdDeviceIo\SdDeviceIo.h" />
    <ClInclude Include="USBDevieIo\U20DeviceIo\U20DeviceIo.h" />
    <ClInclude Include="USBDevieIo\USBDeviceIo.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Device.cpp" />
    <ClCompile Include="DeviceIo.cpp" />
    <ClCompile Include="NVMeDeviceIo\NVMeDeviceIo\NVMeDeviceIo.cpp" />
    <ClCompile Include="NVMeDeviceIo\USB2M2DeviceIo\USB2M2DeviceIo.cpp" />
    <ClCompile Include="SATADeviceIo\AtaDeviceIo\AtaDeviceIo.cpp" />
    <ClCompile Include="SATADeviceIo\AtaDeviceIo\AtaDirectDeviceIo\AtaDirectDeviceIo.cpp" />
    <ClCompile Include="SATADeviceIo\SATADeviceIo.cpp" />
    <ClCompile Include="SATADeviceIo\ScsiDeviceIo\ScsiDeviceIo.cpp" />
    <ClCompile Include="SATADeviceIo\ScsiDeviceIo\ScsiDirectDeviceIo\ScsiDirectDeviceIo.cpp" />
    <ClCompile Include="SATADeviceIo\TranslateBuff\TranslateBuff.cpp" />
    <ClCompile Include="USBDevieIo\EmmcDeviceIo\EMMCDeviceIo.cpp" />
    <ClCompile Include="USBDevieIo\SdDeviceIo\SdDeviceIo.cpp" />
    <ClCompile Include="USBDevieIo\U20DeviceIo\U20DeviceIo.cpp" />
    <ClCompile Include="USBDevieIo\USBDeviceIo.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
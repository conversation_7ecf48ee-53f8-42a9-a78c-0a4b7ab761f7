﻿Build started 2025/7/23 17:32:46.
     1>Project "D:\NewWorkSpace\9-AterPlan11\UsbPort\UsbPort.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\UsbPort.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D NDEBUG /D _LIB /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\UsbPort.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D NDEBUG /D _LIB /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo"D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt IUsbPort.cpp USBPort.cpp
         IUsbPort.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         USBPort.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
     1>USBPort.cpp(537): warning C4244: '=' : conversion from 'DWORD' to 'BYTE', possible loss of data
     1>USBPort.cpp(880): warning C4244: 'return' : conversion from 'ULONG' to 'WORD', possible loss of data
     1>USBPort.cpp(1238): warning C4244: '=' : conversion from 'UINT' to 'BYTE', possible loss of data
     1>USBPort.cpp(1255): warning C4244: '=' : conversion from 'int' to 'BYTE', possible loss of data
     1>USBPort.cpp(1429): warning C4389: '==' : signed/unsigned mismatch
     1>USBPort.cpp(2135): warning C4101: 'nBytes' : unreferenced local variable
     1>USBPort.cpp(2134): warning C4101: 'hubInfo' : unreferenced local variable
     1>USBPort.cpp(2132): warning C4189: 'portIdxOfHub' : local variable is initialized but not referenced
     1>USBPort.cpp(2348): warning C4389: '==' : signed/unsigned mismatch
     1>USBPort.cpp(2374): warning C4189: 'bConnectedDisk' : local variable is initialized but not referenced
     1>USBPort.cpp(2490): warning C4389: '!=' : signed/unsigned mismatch
     1>USBPort.cpp(2505): warning C4389: '!=' : signed/unsigned mismatch
     1>USBPort.cpp(2453): warning C4189: 'buf' : local variable is initialized but not referenced
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:"D:\NewWorkSpace\9-AterPlan11\Lib\UsbPort.lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" /NOLOGO /LTCG "D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\IUsbPort.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\USBPort.obj"
         UsbPort.vcxproj -> D:\NewWorkSpace\9-AterPlan11\Lib\UsbPort.lib
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\UsbPort.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\UsbPort\ReleaseTW\UsbPort.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\UsbPort\UsbPort.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:02.27

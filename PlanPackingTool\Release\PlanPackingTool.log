﻿Build started 2025/7/23 17:32:45.
     1>Project "D:\NewWorkSpace\9-AterPlan11\PlanPackingTool\PlanPackingTool.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "Release\PlanPackingTool.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\pyinclude\include" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /Od /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D _MBCS /D _AFXDLL /D LANG_ENU /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"Release\PlanPackingTool.pch" /Fo"Release\\" /Fd"Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"D:\NewWorkSpace\9-AterPlan11\Include\pyinclude\include" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /Od /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D _MBCS /D _AFXDLL /D LANG_ENU /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"Release\PlanPackingTool.pch" /Fo"Release\\" /Fd"Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt AnalyzeXml\tinyxml2.cpp PlanPackingTool.cpp PlanPackingToolDlg.cpp
         tinyxml2.cpp
         PlanPackingTool.cpp
         PlanPackingToolDlg.cpp
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D NDEBUG /D _AFXDLL /D AFX_RESOURCE_DLL /D AFX_TARG_ENU /l"0x0409" /IRelease\ /nologo /fo"Release\PlanPackingTool.res" PlanPackingTool.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\PlanPackingTool.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" /MANIFEST /ManifestFile:"Release\PlanPackingTool.exe.intermediate.manifest" /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\bin\PlanPackingTool.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\bin\PlanPackingTool.lib" /MACHINE:X86 Release\PlanPackingTool.res
         Release\tinyxml2.obj
         Release\PlanPackingTool.obj
         Release\PlanPackingToolDlg.obj
         Release\stdafx.obj
         Generating code
         Finished generating code
     1>LibDecompression.lib(BenchCon.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ConsoleClose.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ExtractCallbackConsole.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(List.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Main.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MainAr.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OpenCallbackConsole.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PercentPrinter.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateCallbackConsole.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UserInputUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(I7zLib.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(IDecompression.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Link.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CommandLineParser.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CRC.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(IntToString.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ListFileUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MyString.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MyVector.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StdInStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StdOutStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StringConvert.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StringToInt.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UTFConvert.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Wildcard.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DLL.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Error.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileDir.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileFind.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileIO.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileName.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MemoryLock.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PropVariant.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PropVariantConversions.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(System.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Time.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CreateCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CWrappers.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FilePathAutoRename.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FileStreams.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FilterCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(InBuffer.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(InOutTempBuffer.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LimitedStreams.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LockedStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MemBlocks.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MethodId.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MethodProps.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OffsetStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OutBuffer.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OutMemStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ProgressMt.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ProgressUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StreamBinder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StreamObjects.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(StreamUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(VirtThread.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BZip2Crc.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BZip2Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CopyCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CopyRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Deflate64Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DeflateRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ImplodeDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ImplodeHuffmanDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzma2Decoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzma2Encoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzma2Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzmaRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PpmdRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PpmdZip.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ShrinkDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Lzx86Converter.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzxDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(QuantumDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bcj2Coder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bcj2Register.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BcjCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BcjRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BitlDecoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BranchCoder.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BranchMisc.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(BranchRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ByteSwap.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DeltaFilter.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzOutWindow.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zDecode.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zEncode.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zExtract.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zFolderInStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zFolderOutStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zHandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zProperties.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zSpecStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zUpdate.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarHandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TarUpdate.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipAddCommon.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipHandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipItem.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipUpdate.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CoderMixer2.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CoderMixer2MT.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DummyOutStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(FindSignature.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(HandlerOut.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(InStreamWithCRC.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ItemNameUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(MultiStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OutStreamWithCRC.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ParseProperties.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabBlockInStream.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabHeader.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabIn.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(CabRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bz2Handler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DeflateProps.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(GzHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LzmaHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(SplitHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(XzHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ArchiveCommandLine.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ArchiveExtractCallback.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ArchiveOpenCallback.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Bench.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(DefaultName.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(EnumDirItems.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Extract.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ExtractingFilePath.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(LoadCodecs.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(OpenArchive.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(PropIDUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(SetProperties.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(SortUtils.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(TempFiles.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(Update.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateAction.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateCallback.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdatePair.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(UpdateProduce.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(7zAesRegister.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(RandGen.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(WzAes.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipCrypto.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
     1>LibDecompression.lib(ZipStrong.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
         PlanPackingTool.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\PlanPackingTool.exe
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\PlanPackingTool.exe;#1" /manifest Release\PlanPackingTool.exe.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "Release\PlanPackingTool.unsuccessfulbuild".
         Touching "Release\PlanPackingTool.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\PlanPackingTool\PlanPackingTool.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:08.61

<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>047d287e-1b1c-4f69-b34b-e85265af606f</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>SsdReport.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>SsdReport</Name>
    <RootNamespace>SsdReport</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="CRCTest.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="FactoryWAF.py" />
    <Compile Include="FlashSummary.py" />
    <Compile Include="Flash_InitPerformance.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Flash_Reliability.py" />
    <Compile Include="Format.py" />
    <Compile Include="IdlePerformance.py" />
    <Compile Include="NewItem.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="NormalBit.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Performance.py" />
    <Compile Include="PerformanceDingRong.py" />
    <Compile Include="PublicFuc.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Reliability.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="RS_Performance.py" />
    <Compile Include="SsdReport.py" />
    <Compile Include="Stability.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Summary.py" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="env\">
      <Id>env</Id>
      <Version>3.7</Version>
      <Description>env (Python 3.7 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <ItemGroup>
    <Content Include="requirements.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
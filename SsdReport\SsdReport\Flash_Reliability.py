import PublicFuc
from datetime import datetime,timedelta

def Run(curpath, workBook, alignment):
    ws = workBook['03-可靠性测试-1']
    ws.alignment = alignment
    ProMarsCase1(curpath, ws)
    #ProBitCase(curpath, ws)
    #ProImtCase(curpath, ws)
    PublicFuc.WriteReportTime(ws,'W',2)
    PublicFuc.WriteReportOperator(ws,'D',2)

    ws2 = workBook['04-可靠性测试-2']
    ws2.alignment = alignment
    ProMarsCase2(curpath, ws2)
    PublicFuc.WriteReportTime(ws2,'W',2)
    PublicFuc.WriteReportOperator(ws2,'D',2)

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result']) 
        if 'cycle' in keyLst:
            if '' == dic['end_circle'] or '' == dic['start_circle']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_circle'])-int(dic['start_circle']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if '' == dic['id_a5'] or '' == dic['id_a6']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt = 2):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName, recordCnt)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = PublicFuc.GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def ProMarsCase1(curpath, worksheet):
    keyLst = ['pc_no','cap','result','cycle','wrdata','runtime','SmartInfo','wavg','wmax','wmin','ravg','rmax','rmin','wovertime','A5-A6']
    #高温老化物理盘70度-plan502
    colLst = ['C','B','E','U','H','I','J','K','L','M','N','O','P','Q','R','T']
    pattern = '.+\\\\Plan502\\\\T-SS-SS-E02\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'BurnInTest48H'
    startLine = 13
    proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, 32)

    #高温读干扰物理盘75度-plan501
    colLst = ['C','B','E','V','I','J','K','S','L','M','N','O','P','Q','R','U']
    pattern = '.+\\\\Plan501\\\\T-SS-SS-E01\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'ReadDisturb48H'
    startLine = 49
    proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, 16)

    #高温读干扰物理盘75度-plan510 72小时
    colLst = ['C','B','E','V','I','J','K','S','L','M','N','O','P','Q','R','U']
    pattern = '.+\\\\Plan501\\\\T-SS-SS-A26\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'ReadDisturb'
    startLine = 69
    proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, 16)

def ProMarsCase2(curpath, worksheet): 
    #高温保持
    FillRetentionReport(worksheet,curpath,40)#低写高读、120度
    FillRetention2Report(worksheet,curpath,4)#低写高读504、505、70度
    FillRetention3Report(worksheet,curpath,4)#高写低读506、507、70度

def FillRetentionReport(worksheet,curpath,recordCnt):
    pattern = '.+\\\\Plan508\\\\T-SS-SS-E09\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    startLine = 13
    caseDicH2before = {} #高温前的数据
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    #pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    #pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no','cap','result','wavgH2','ravgH2','SmartInfo','ravg1','ravg2','ravg3','SmartInfo','A5-A6']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before,caseDicH2verify1,caseDicH2verify2,caseDicH2verify3)
    colLst = ['C','B','E','U','G','H','L','I','J','K','O','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

#低写高读
def FillRetention2Report(worksheet,curpath,recordCnt):
    pattern = '.+\\\\Plan504\\\\T-SS-SS-E05\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    startLine = 63
    caseDicH2before = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan505\\\\T-SS-SS-E06\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    #pattern = '.+\\\\Plan505\\\\T-SS-SS-E06\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    #pattern = '.+\\\\Plan505\\\\T-SS-SS-E06\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no','cap','result','wavgH2','ravgH2','SmartInfo','ravg1','ravg2','ravg3','SmartInfo','A5-A6']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before,caseDicH2verify1,caseDicH2verify2,caseDicH2verify3)
    colLst = ['C','B','E','U','G','H','L','I','J','K','O','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

#高写低读
def FillRetention3Report(worksheet,curpath,recordCnt):
    pattern = '.+\\\\Plan506\\\\T-SS-SS-E07\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    startLine = 56
    caseDicH2before = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan507\\\\T-SS-SS-E08\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    #pattern = '.+\\\\Plan507\\\\T-SS-SS-E08\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    #pattern = '.+\\\\Plan507\\\\T-SS-SS-E08\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no','cap','result','wavgH2','ravgH2','SmartInfo','ravg1','ravg2','ravg3','SmartInfo','A5-A6']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before,caseDicH2verify1,caseDicH2verify2,caseDicH2verify3)
    colLst = ['C','B','E','U','G','H','L','I','J','K','O','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def GetNewRetentionReportDic(h2Dic, h2verify1Dic, h2verify2Dic, h2verify3Dic):
    newDic = {}
    for key in h2Dic:
        newDic[key] = []
        dic = h2Dic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result']) 
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['average_read_vel'])

        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)

        if key in h2verify1Dic:#如果键值在里面
            newDic[key].append(h2verify1Dic[key]['average_read_vel'])
            if newDic[key][2] == '':
                newDic[key][2] = h2verify1Dic[key]['test_result'] #如果前面的没错，则用后面的错误信息填充
        else:
            newDic[key].append('')

        if key in h2verify2Dic:
            newDic[key].append(h2verify2Dic[key]['average_read_vel'])
            if newDic[key][2] == '':
                newDic[key][2] = h2verify2Dic[key]['test_result'] #如果前面的没错，则用后面的错误信息填充
        else:
            newDic[key].append('')

        newDic[key].append('')#第3次校验无
        if key in h2verify1Dic:
            dic = h2verify1Dic[key]
            smart = ''
            #统计不为0的smart信息
            for innerKey in dic.keys():
                if innerKey.startswith('id_'):
                    if '' == dic[innerKey]:
                        continue
                    if 0 != int(dic[innerKey],16):
                        pos = innerKey.find('id_')
                        id = innerKey[pos+len('id_'):].upper()
                        if id in PublicFuc.commonSmartKey:
                            smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
            if '' != smart:
                smart = smart[:-1]
            newDic[key].append(smart)
            if '' == dic['id_a5'] or '' == dic['id_a6']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
        else:
            newDic[key].append('')
            newDic[key].append('')
        
    return newDic   

def ProImtCase(curpath, worksheet):
    #仿真监控测试
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E','T','L','M','N','O','P','S']
    imtKeyLst = ['pc_no', 'Cap', 'qa_err_msg', 'Seq_1M_OverWrite_168H_Iops', 'Seq_1M_OverWrite_168H_MiBps']
    pattern = '.+\\\\Plan34\\\\T-SS-SS-C09\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, '', 1,4)
    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey)
    newKey = imtKeyLst+smartKeyNew
    startLine = 262
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)

def ProBitCommon(curpath, ws, pattern, bitCol, startLine, diskCnt = 4):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1F2', 'SmartInfo', 'A5-A6']
    bitKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration']
    bitDic = {}
    newKey = bitKey+smartKey
    PublicFuc.ReadQaIniData(curpath, pattern, bitDic, newKey, '', 1, diskCnt)
    newDic = PublicFuc.GetNewBitDic(bitDic, len(bitKey), smartKey)
    newKey = bitKey+smartKeyNew
    PublicFuc.WriteData(ws, startLine, newDic, bitCol, newKey)

def ProBitCase(curpath, ws):
    bitCol = ['C','B','E','U','H','J','I','K','T']
    #bit高温老化逻辑盘
    pattern = '.+\\\\Plan59\\\\T-SS-SS-B08\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 55
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #bit高温老化物理盘
    pattern = '.+\\\\Plan60\\\\T-SS-SS-B09\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 63
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #bit低温老化逻辑盘
    pattern = '.+\\\\Plan61\\\\T-SS-SS-B10\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 95
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #bit低温老化物理盘
    pattern = '.+\\\\Plan62\\\\T-SS-SS-B11\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 103
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #plan38bit老化
    pattern = '.+\\\\Plan38\\\\T-SS-SS-B05\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 115
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #写%x容量后老化
    bitCol = ['C','B','E','R','H','K','I','M','Q']
    pattern = '.+\\\\Plan44\\\\T-SS-SS-C13\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 198
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    pattern = '.+\\\\Plan45\\\\T-SS-SS-C14\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 214
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    pattern = '.+\\\\Plan46\\\\T-SS-SS-C32\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 206
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    pattern = '.+\\\\Plan47\\\\T-SS-SS-C33\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 222
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    #SLC老化+满盘老化
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1F2', 'SmartInfo', 'A5-A6']
    bitKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration']
    slcBitDic = {}
    pattern = '.+\\\\Plan48\\\\T-SS-SS-C34\\\\BurnInTest_Slc\\\\\d{14}\\\\report.ini$'
    newKey = bitKey+smartKey
    PublicFuc.ReadQaIniData(curpath, pattern, slcBitDic, newKey, '', 1, 0)
    slcBitDic = PublicFuc.GetNewBitDic(slcBitDic, len(bitKey), smartKey)
    
    fullBitDic = {}
    pattern = '.+\\\\Plan48\\\\T-SS-SS-C34\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, fullBitDic, newKey, '', 1, 0)
    fullBitDic = PublicFuc.GetNewBitDic(fullBitDic, len(bitKey), smartKey)
    
    bitDic = {}
    for key in slcBitDic:
        if 4 == len(bitDic):
            break
        if key not in fullBitDic:
            continue
        slcRecord = slcBitDic[key][0]
        fullRecord = fullBitDic[key][0]
        slcRecord[3] += fullRecord[3]
        bitRecord = slcRecord[:6]+fullRecord[3:]
        bitDic[key] = []
        bitDic[key].append(bitRecord)

    newKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration','F1F2','BitCycle','Duration','F1F2','SmartInfo', 'A5-A6']
    bitCol = ['C','B','E','T','H','J','I','K','N','L','O','S']
    startLine = 230
    PublicFuc.WriteData(ws, startLine, bitDic, bitCol, newKey)
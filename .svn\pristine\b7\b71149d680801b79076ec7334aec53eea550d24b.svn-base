import os,sys,logging,traceback,time,json

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
if len(sys.argv) > 1:
    #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
    reportPath = sys.argv[1]

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'EMMC_Report_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)


try:
    import shutil,tempfile
    import requests
    import openpyxl
    from openpyxl.styles import Alignment
    import Performance,Wl,PublicFuc,Wl_TOOLS,Platform_pf,PowerOffTest,Coroutines,MarsEnvironment,Endurance,DataRetention
    import urllib.parse

    if len(sys.argv) > 1:
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt')
        jsonData = {}
        jsonData['token'] = 'd7faec554b2d83ff9592d5bb507e12b1d9915f21a29490638f3a735117ba6076'
        jsonData['secret'] = 'SEC4afd2f150ce4a5808c2a6337d59de4bc098c0a37f2140d4ea741519ef75e09da'
        jsonData['atLst'] = '13686893642'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg' 
        bRmsRun = True
    templateFile = os.path.join(curpath, 'emmc_report_template.xlsx') 
    resultFileName = 'Emmc汇总报告.xlsx'
    resultFile = os.path.join(reportPath, resultFileName) 
    errDiskFile = os.path.join(reportPath, 'ErrDisk.txt')

    logging.info('程序开始运行！')
    PublicFuc.GetAllFile(reportPath)
    wb = openpyxl.load_workbook(filename = templateFile)
    alignment = Alignment(horizontal='center',vertical='center')
    PowerOffTest.Run(reportPath, wb, alignment)
    Coroutines.Run(reportPath, wb, alignment)
    MarsEnvironment.Run(reportPath, wb, alignment)
    Endurance.Run(reportPath, wb, alignment)
    DataRetention.Run(reportPath, wb, alignment)
    Performance.Run(reportPath, wb, alignment)
    #Wl.Run(reportPath, wb, alignment)
    Wl_TOOLS.Run(reportPath, wb, alignment)
    #Platform_pf.Run(reportPath, wb, alignment)
    PublicFuc.WriteErrDiskFile(errDiskFile)

    wb.save(resultFile)
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = 'eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/report/download/?nid=EMMC&key=%s'%enTestNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&file='%('EMMC',enTestNo)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFile):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://gateway.yeestor.com:8789/wo/report/status"
        headers = {'Content-Type': 'application/json','ORDER-NO':enTestNo}
        requests.request("POST", woUrl, headers=headers, data=json.dumps(woDic))
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        jsonData['type'] = 'text'
        jsonData['text'] = 'eReport报告合并异常！@13686893642\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)




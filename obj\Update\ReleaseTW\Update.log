﻿Build started 2025/7/23 17:32:46.
     1>Project "D:\NewWorkSpace\9-AterPlan11\Update\Update.vcxproj" on node 4 (build target(s)).
     1>C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Microsoft.CppBuild.targets(299,5): warning MSB8004: Output Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Output Directory.
     1>InitializeBuildStatus:
         Creating "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
     1>C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\include\winbase.h(13684): warning C4067: unexpected tokens following preprocessor directive - expected a newline
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"D:\NewWorkSpace\9-AterPlan11\Include\\" /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W4 /WX- /O2 /Oi /Oy- /GL /D WIN32 /D _WINDOWS /D NDEBUG /D ATER_TW /D _MBCS /D _AFXDLL /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.pch" /Fo"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\\" /Fd"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\AterEx\StatusListBox\StatusListBox.cpp Update.cpp UpdateDlg.cpp
         StatusListBox.cpp
     1>..\AterEx\StatusListBox\StatusListBox.cpp(54): warning C4189: 'lineCount' : local variable is initialized but not referenced
     1>..\AterEx\StatusListBox\StatusListBox.cpp(261): warning C4239: nonstandard extension used : 'initializing' : conversion from 'CSize' to 'CSize &'
                 A non-const reference may only be bound to an lvalue
     1>..\AterEx\StatusListBox\StatusListBox.cpp(272): warning C4100: 'pDC' : unreferenced formal parameter
         Update.cpp
         UpdateDlg.cpp
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D NDEBUG /D _AFXDLL /l"0x0409" /I"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\\" /nologo /fo"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.res" Update.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\Update.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" LibPublicwin32.lib LibDecompression.lib /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.exe.intermediate.manifest" /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\Update\Update.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\bin\Update.lib" /MACHINE:X86 "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\StatusListBox.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\UpdateDlg.obj"
         Generating code
         Finished generating code
     1>LibDecompression.lib(NewHandler.obj) : warning LNK4206: precompiled type information not found; 'f:\svn\teststudio\soucode\7z920\cpp\7zip\bundles\alone\release\stdafx.obj' not linked or overwritten; linking object as if no debug info
         Update.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\Update.exe
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\Update.exe;#1" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.exe.intermediate.manifest" "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\Update\ReleaseTW\Update.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\Update\Update.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:15.07

import os,sys,logging,traceback,time,shutil,tempfile,json

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
xl = None
if len(sys.argv) > 1:
    reportPath = sys.argv[1]

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'U3_Report_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)

try:
    import requests
    import openpyxl
    from openpyxl.styles import Alignment
    import Plan1,Plan2,Plan6,PublicFuc,Plan4,Plan5,Plan14,DetailAndConclusion,Summary,ErrDiskInfo,Plan33,Plan34,Plan35
    from win32com.client import Dispatch
    import urllib.parse

    errorFileName = 'ErrDisk.xlsx'
    resultFileName = 'U3_版本全面测试用例及报告汇总.xlsx'
    templateFile = os.path.join(curpath, 'u3_report_template.xlsx') 
    resultFile = os.path.join(reportPath, resultFileName) 
    errDiskFile = os.path.join(reportPath, 'ErrDisk.txt')
    errDiksTemplateFile = os.path.join(curpath, 'err_disk_template.xlsx')
    resultFileOfErrDisk = os.path.join(reportPath, errorFileName)
    addpath = os.path.join(reportPath,'ManReport')
    if len(sys.argv) > 1:
        #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
        PublicFuc.MPDATA_DIR = reportPath.replace('Report','MPData')
        PublicFuc.REPORT_DIR = reportPath
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt') 
        jsonData = {}
        jsonData['token'] = '84f23150ca7bba479ca6c1a69c2cb69af535f149f3609b0b1f479eefd08d10ff'
        jsonData['secret'] = 'SEC6ab5b245b5fa0feb98bd6e028d0a2bfb044a1665c3b732d344a4e5eb4eb92bdb'
        jsonData['atLst'] = '18312006726'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg'
        bRmsRun = True

    logging.info('程序开始运行！')
    PublicFuc.GetAllFile(reportPath)
    wb = openpyxl.load_workbook(filename = templateFile)
    alignment = Alignment(horizontal='center',vertical='center')
    #Plan1.Run(reportPath, wb, alignment)
    Plan2.Run(reportPath, wb, alignment)
    #Plan6.Run(reportPath, wb, alignment)
    #Plan4.Run(reportPath, wb, alignment)
    Plan5.Run(reportPath, wb, alignment)
    Plan14.Run(reportPath, wb, alignment)
    Plan33.Run(reportPath, wb, alignment)
    Plan34.Run(reportPath, wb, alignment)
    Plan35.Run(reportPath, wb, alignment)
    DetailAndConclusion.Run(reportPath, wb, alignment)
    Summary.Run(reportPath, wb, alignment)
    PublicFuc.WriteErrDiskFile(errDiskFile)
    wb.save(resultFile)

    if os.path.exists(errDiksTemplateFile):
        wb = openpyxl.load_workbook(filename = errDiksTemplateFile)
        alignment = Alignment(horizontal='center',vertical='center')   
        ErrDiskInfo.Run(reportPath, wb, alignment)       
        wb.save(resultFileOfErrDisk)

    if os.path.exists(addpath):
        excelLst = ['xls','xlsx']
        dst = resultFile
        xl = Dispatch("Excel.Application")
        xl.Visible = 0
        wb2 = xl.Workbooks.Open(Filename=dst,UpdateLinks=0)
        oldPos = len(wb2.Worksheets)-1
        for src_plan in os.listdir(addpath):
            src_plan = os.path.join(addpath,src_plan)
            src_time = os.listdir(src_plan)
            src_time = sorted(src_time,reverse=True)
            src_time = os.path.join(src_plan,src_time[0])
            for src_xls in os.listdir(src_time):
                pos = src_xls.rfind('.')
                if -1 != pos:
                    suffix = src_xls[pos+1:]
                    if suffix.lower() in excelLst:
                        src_xls = os.path.join(src_time,src_xls)
                        wb1 = xl.Workbooks.Open(Filename=src_xls,UpdateLinks=0)
                        for shes in wb1.sheets: 
                            ws1 = wb1.Worksheets(shes.name)
                            ws1.Copy(wb2.Sheets[len(wb2.Worksheets)-1])
                        wb1.Close(False)
        #Copy无法复制到最后一个sheet，因此要把最后一个sheet移到原有的位置
        wb2.Sheets[len(wb2.Worksheets)-1].Move(wb2.Sheets[oldPos])
        wb2.Worksheets[0].Activate()
        wb2.Close(SaveChanges=True) 
        xl.Quit()
        del xl
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = '[U3]-eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/report/download/?nid=U3&key=%s'%enTestNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&file='%('U3',enTestNo)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFileOfErrDisk):
            tempDic = {}
            tempDic['name'] = errorFileName
            tempDic['type'] = 'error'
            tempDic['url'] = strUrl+errorFileName
            woDic['reportInfoList'].append(tempDic)
        if os.path.exists(resultFile):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://gateway.yeestor.com:8789/wo/report/status"
        headers = {'Content-Type': 'application/json','ORDER-NO':enTestNo}
        requests.request("POST", woUrl, headers=headers, data=json.dumps(woDic))
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        jsonData['type'] = 'text'
        jsonData['text'] = '[U3]-eReport报告合并异常！@18312006726\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
    if None != xl:
        try:
            wb2.Close(False)
        except:
            logging.error(traceback.format_exc())
        try:
            xl.Quit()
        except:
            logging.error(traceback.format_exc())
        del xl


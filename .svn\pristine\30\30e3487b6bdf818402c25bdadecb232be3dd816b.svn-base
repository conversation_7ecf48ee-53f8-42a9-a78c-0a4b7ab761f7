﻿#pragma once
#include <define.h>
#include "..\..\DeviceIo.h"


namespace PCIeIO
{
	class CNVMeDeviceIo : public SGDeviceIo::CDeviceIo
	{
	public:
		CNVMeDeviceIo(void);
		virtual ~CNVMeDeviceIo(void);

	public:
		virtual Bool DeviceIO(void *_pCMD, U32 _nCMDSize, U8 *_pBuffer, U32 _bufferSize) override;

		virtual void CloseDevice() override;

		virtual Bool OpenDevice(U8 _phyDiskNo, bool bShare/* = True*/, Bool bOvelapped = False) override;

		virtual Bool GetIdentify(void *_pCMD, U32 _nCMDSize, U8 *_pBuffer, U32 _bufferSize) override;

	private:
		Bool CNVMeDeviceIo::SendNVMeInbox(HANDLE hDeviceIOCTL, void *_pSrtCommandSet, BYTE *bIOBuf) const;
	};
}


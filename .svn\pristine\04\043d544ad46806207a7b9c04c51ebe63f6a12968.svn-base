﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
    <None Include="res\StatusUpload.rc2">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="StatusUpload.def">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="commonFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IStatusUpload.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StatusUpload.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PcStatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestStatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AutoLock.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestResult.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UpLoadSleepReboot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="commonFunction.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\StatusUpload\IStatusUpload.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StatusUpload.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PcStatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TestStatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AutoLock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TestResult.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UpLoadSleepReboot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="StatusUpload.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>
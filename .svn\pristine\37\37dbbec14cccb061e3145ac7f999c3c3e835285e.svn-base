#pragma once


// CDialogTransarentNotify dialog

class CDialogTransarentNotify : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogTransarentNotify)

public:
	CDialogTransarentNotify(CWnd* pParent = NULL);   // standard constructor
	virtual ~CDialogTransarentNotify();

// Dialog Data
	enum { IDD = IDD_DIALOG_NOTIFY_WND };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();
	afx_msg void OnPaint();
	CBrush m_brush;
	CFont m_fontTitle;
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
};

import PublicFuc,re,copy
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  Pat<PERSON>Fill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

def Run(curpath, workBook, alignment):
    ws = workBook['主页']
    ws.alignment = alignment
    ProDataRetention(ws,workBook)
    ProHTempBurnin(ws,workBook)
    ProHTempReadDisturb48H(ws,workBook)
    ProHTempReadDisturb72H(ws,workBook)
    ProHWriteLRead(ws,workBook)
    ProLWriteHRead(ws,workBook)
    ProCDMPicture(ws,workBook)
    ProASSDPicture(ws,workBook)
   
#获取测试失败的mark信息
def GetAllSmartInfo(dataList):
    strResult = ''
    for line in dataList:
        if len(line) < 2:
            continue
        strResult = strResult + '[' + line[0] + ']:  ' + line[1]
        strResult = strResult+'\r\n'
    return strResult

def GetSpecialDataCnt(_startRow,_endRow,_colName,ws,step = 1):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow,_endRow+1,step):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt

#判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False
    
    isValid = False
    for idx in range(1,len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid

#获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow,_endRow,_colNameList,ws,step = 1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1,step):
        oneRow = []
        for _colName in _colNameList:
            if _colName == '':
                oneRow.append('')
            else:
                celPosSample = '%s%d'%(_colName, rowNo)
                celValue = ws[celPosSample].value
                oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic

#获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialSingleColDataList(_startRow,_endRow,_colName,ws,step = 1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1,step):
        oneRow = []  
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        dic.append(celValue)
    return dic


def ProDataRetention(ws,workBook):
    wsTmp = workBook['04-可靠性测试-2']
    MIN_LINE = 13
    MAX_LINE = 52
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','O']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L7'] = smartInfo


def ProHTempBurnin(ws,workBook):
    wsTmp = workBook['03-可靠性测试-1']
    MIN_LINE = 13
    MAX_LINE = 44
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L5'] = smartInfo

def ProHTempReadDisturb48H(ws,workBook):
    wsTmp = workBook['03-可靠性测试-1']
    MIN_LINE = 49
    MAX_LINE = 64
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','S']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L6'] = smartInfo

def ProHTempReadDisturb72H(ws,workBook):
    wsTmp = workBook['03-可靠性测试-1']
    MIN_LINE = 69
    MAX_LINE = 84
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','S']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L6'] = smartInfo

def ProHWriteLRead(ws,workBook):
    wsTmp = workBook['04-可靠性测试-2']
    MIN_LINE = 56
    MAX_LINE = 59
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','O']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L8'] = smartInfo

def ProLWriteHRead(ws,workBook):
    wsTmp = workBook['04-可靠性测试-2']
    MIN_LINE = 63
    MAX_LINE = 66
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','O']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L9'] = smartInfo

def ProCDMPicture(ws,workBook):
    wsTmp = workBook['05-初始_性能测试']
    MIN_LINE = 12
    MAX_LINE = 13
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    sampleNo = GetSpecialSingleColDataList(MIN_LINE,MAX_LINE,'C',wsTmp)
    if len(sampleNo) < 1:
        return

    for i in range(len(sampleNo)):
        pos = '%s%d'%(get_column_letter(2+i*7), 12)
        ws[pos] = sampleNo[i]
    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor=='A14':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'B13')
        elif image.anchor == 'D14':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I13')


def ProASSDPicture(ws,workBook):
    wsTmp = workBook['05-初始_性能测试']
    MIN_LINE = 18
    MAX_LINE = 19
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    sampleNo = GetSpecialSingleColDataList(MIN_LINE,MAX_LINE,'C',wsTmp)
    if len(sampleNo) < 1:
        return

    for i in range(len(sampleNo)):
        pos = '%s%d'%(get_column_letter(2+i*7), 16)
        ws[pos] = sampleNo[i]
    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor=='A20':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'B17')
        elif image.anchor == 'D20':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I17')


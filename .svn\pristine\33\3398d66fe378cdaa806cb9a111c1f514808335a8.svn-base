#include "TranslateBuff.h"
#include <define.h>
#include <ssd\ssd.h>


CTranslateBuff::CTranslateBuff()
{

}


CTranslateBuff::~CTranslateBuff()
{

}


Bool CTranslateBuff::Translate2PrivateWRStruct(U8* _outputBuf, U32 _outputBufSize, void* _pCMD, U32 _nCMDSize, U8 *_pBuffer, U32 _bufferSize)
{
	if (!_outputBuf || !_pCMD || 0 == _outputBufSize)
	{
		return False;
	}
	else
	{
		U32 priCmdBlkInfoSize = _nCMDSize - sizeof(SGSsd::DATA_BLOCK_HEADER);

		if (priCmdBlkInfoSize + _bufferSize > _outputBufSize)
		{
			return False;
		}
		else
		{
			U32 offset = 0;
			memcpy_s(_outputBuf, priCmdBlkInfoSize, static_cast<U8*>(_pCMD)+sizeof(SGSsd::DATA_BLOCK_HEADER), priCmdBlkInfoSize);
			offset += priCmdBlkInfoSize;
			memcpy_s(_outputBuf + offset, _bufferSize, _pBuffer, _bufferSize);

			return True;
		}
	}	
}


Bool CTranslateBuff::Translate2PrivateRDStruct(U8* _outputBuf, U32 _outputBufSize, U8 *_pBuffer, U32 _bufferSize)
{
	if (!_outputBuf || !_pBuffer || 0 == _outputBufSize)
	{
		return False;
	}
	else
	{
		U32 minSize = min(_outputBufSize, _bufferSize);
		memcpy_s(_outputBuf, minSize, _pBuffer, minSize);

		return True;
	}
}
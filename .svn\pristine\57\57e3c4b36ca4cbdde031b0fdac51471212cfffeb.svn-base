pageinfo = [[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null],
	[0,0,0,0,null]];
pagedata = [ ["./autologin.htm","Auto-login to Windows","Auto-login You can use Rebooter to set and clear the Windows Auto-login feature. By entering your Domain, User name and password then clicking on ...",""],
["./autorun.htm","Auto-run applications","Auto-run applications This page gives a brief description of how an application can be setup to run automatically when Windows starts. This is an ...",""],
["./commandline.htm","Command line options","Command line options There are three command line options. If no command line option is specified, Rebooter runs in interactive mode. -Reboot Perf...",""],
["./contacts.htm","Contacting PassMark Software","Contacting PassMark Software On the Web You can contact PassMark&#8482; on the web at http://www.passmark.com E-Mail For technical support questions, su...",""],
["./copyright.htm","Copyright and License","Copyright &#38; License information PassMark&#174; Software Pty Ltd (‘PassMark’) End User Licence Agreement (‘EULA’) IMPORTANT! PLEASE READ THE FOLLOWING T...",""],
["./cycle.htm","Reboot cycling and looping","Cyclic rebooting This page gives a brief description of how a PC can be set-up to reboot itself in a cycle. Step1 – Select and save Rebooter setti...",""],
["./faq.htm","Problems and FAQ","Q. Why trying to reboot, I get a message like, &#8220;Could not adjust privileges...&#8221; or &#8220;Could not open security token&#8221; In Windows NT/2000/XP/2003/Vist...",""],
["./forcetypes.htm","Forcing a reboot","Forced reboot Three options are available. Ask to close Default value. Each running application is given the chance to block the shutdown of Windo...",""],
["./overview.htm","Introduction and Overview","Rebooter by PassMark Software - Overview Rebooter is a small utility program developed by PassMark­&#8482; Software to help automate the PC hardware tes...",""],
["./reboottypes.htm","Reboot and Restart types","Reboots and Restarts There are four options available. Power-off This would shut down the system and turn off the power. The system must support t...",""],
["./systemreq.htm","System requirements","System Requirements 80486 50Mhz or faster Windows 95, 98, Me, NT4, 2000, XP, 2003 server, Vista, Windows 7, Windows 2012 Server, Windows 8, 10 16M...",""],
["./whats_new.htm","What&#39;s new","What new - Version history V1.3.1007 30/January/2017 Increased the amount of log lines displayed to 10,000 V1.3.1006 13/December/2016 Added suppor...",""]];

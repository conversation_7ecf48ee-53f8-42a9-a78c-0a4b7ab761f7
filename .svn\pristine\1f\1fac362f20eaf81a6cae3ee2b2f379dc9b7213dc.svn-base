Version 1.1.0 
'TEST SETUP ====================================================================
'Test Description
	
'Run Time
'	hours      minutes    seconds
	0          5          0
'Ramp Up Time (s)
	5
'Default Disk Workers to Spawn
	NUMBER_OF_CPUS
'Default Network Workers to Spawn
	0
'Record Results
	ALL
'Worker Cycling
'	start      step       step type
	1          1          LINEAR
'Disk Cycling
'	start      step       step type
	1          1          LINEAR
'Queue Depth Cycling
'	start      end        step       step type
	1          32         2          EXPONENTIAL
'Test Type
	NORMAL
'END test setup
'RESULTS DISPLAY ===============================================================
'Record Last Update Results,Update Frequency,Update Type
	ENABLED,1,LAST_UPDATE
'Bar chart 1 statistic
	Total I/Os per Second
'Bar chart 2 statistic
	Total MBs per Second (Decimal)
'Bar chart 3 statistic
	Average I/O Response Time (ms)
'Bar chart 4 statistic
	Maximum I/O Response Time (ms)
'Bar chart 5 statistic
	% CPU Utilization (total)
'Bar chart 6 statistic
	Total Error Count
'END results display
'ACCESS SPECIFICATIONS =========================================================
'Access specification name,default assignment
	512K-READ,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,100,100,0,0,1,524288,0
'Access specification name,default assignment
	512K-WRITE,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,100,0,0,0,1,524288,0
'Access specification name,default assignment
	4KREAD,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,100,100,0,1,4096,0
'Access specification name,default assignment
	4KWRITE,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,0,100,0,1,4096,0
'END access specifications
'MANAGER LIST ==================================================================
'Manager ID, manager name
	1,DESKTOP-S1KSH25
'Manager network address
	
'Worker
	Worker 1
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 2
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 3
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 4
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 5
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 6
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 7
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 8
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	2097152,0,0
'End default target settings for worker
'Assigned access specs
	512K-READ
	512K-WRITE
	4KREAD
	4KWRITE
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'End manager
'END manager list
Version 1.1.0 

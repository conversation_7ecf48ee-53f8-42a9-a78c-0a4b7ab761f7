﻿<!DOCTYPE html>
<html>
<head>
   <title>What's new</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Version history,Whats new" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "whats_new.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold;"> &nbsp;What new - Version history</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="overview.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="cycle.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="faq.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p><span style="font-weight: bold;">V1.3.1007 30/January/2017</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="text-indent: 25px;">Increased the amount of log lines displayed to 10,000</p>
<p style="text-indent: 25px;">&nbsp;</p>
<p><span style="font-weight: bold;">V1.3.1006 13/December/2016</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="text-indent: 25px;">Added support for Hybrid shutdown, available in Windows 8 and newer. If not supported then this option should just do a normal shutdown.</p>
<p style="text-indent: 25px;">Added a new option to run an external application at the end of the cycle count</p>
<p style="text-indent: 25px;">Added a new command line option to load a config file</p>
<p style="text-indent: 25px;">Added a new command line option to auto exit at last cycle</p>
<p style="text-indent: 25px;">Fixed a bug where the cycle count could be wrong</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.3.1005 5/August/2015</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Fixed a bug where if Rebooter was launched with -p it wasn't applying it to any further auto start entries created (when used with the &quot;Auto load&quot; option) </p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.3.1004 18/September/2013</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Windows 8.1 support added. Rebooter could display log entries on the Windows Tiled screen after a reboot, this has been corrected.</p>
<p style="margin: 0px 0px 0px 24px;">Rebooter is now digitally signed.</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.3.1003 19/June/2009</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Fixed a bug where the &quot;-reboot&quot; command line option would not set the auto start on system startup flag, and hence would only reboot once (regardless on the current configuration).</p>
<p style="margin: 0px 0px 0px 24px;">Windows 7 support added. </p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.3.1002 29/October/2007</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Fixed a bug in setting auto-login when running under 64-bit versions of Windows.</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.3.1001 20/March/2007</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Added a command line parameter -p to force Rebooter to use the rebooter.exe directory rather than the User's personal directory for configuration and log files etc. This may be useful when running rebooter from a USB drive. Note: If BurnInTest is started with the -p &nbsp;command line parameter then BurnInTest will start rebooter with the -p command line parameter.</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.3.1000 20/February/2007</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Windows Vista release. The help file has been replaced with Browser based help. The configuration and log files have been moved to the User's personal directory on Windows 2000 and above (unless rebooter is run from a BurnInTest installation with a valid BurnInTest key.dat file, e.g. from a CD or flash drive). </p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.2.1001 12/April/2006</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Corrected a bug where a change to the “Auto Load Rebooter at startup” setting was only enabled when Rebooter was started (but not when the user actually changed the setting). The disabling of this setting, as per the user configuration, has been added.</p>
<p style="margin: 0px 0px 0px 24px;">Corrected a bug where an obscure log file directory name would cause Rebooter to exit.</p>
<p style="margin: 0px 0px 0px 24px;">Command line parameters may now be specified in the Application field.</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.2.1000 31/March/2006</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Allow an application to be run between reboots. The application specified in the “application” field on the main window will be run after the timer has expired (reached 0). Rebooter will wait for the external application to close before continuing. If no application is specified, then no application will be run.</p>
<p style="margin: 0px 0px 0px 24px;">If the log file directory does not exist, it will now be created.</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.1.1003 1/February/2006</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Change start and stop buttons to reset the reboot counter</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.0.1003 7/March/2005</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Added a logging window and log file options</p>
<p style="margin: 0px 0px 0px 24px;">Added pause button</p>
<p style="margin: 0px 0px 0px 24px;">Added stop cycle button</p>
<p style="margin: 0px 0px 0px 24px;">Added option to auto load Rebooter at startup</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.0.1001 13/April/2002</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">Changed name of the Rebooter configuration file to avoid a conflict with BurnInTest.</p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p><span style="font-weight: bold;">V1.0.1000 14/Mar/2002</span></p>
<p><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 0px 0px 0px 24px;">First release</p>
<p><span style="color: #000000;">&nbsp;</span></p>

</td></tr></table>

</body>
</html>

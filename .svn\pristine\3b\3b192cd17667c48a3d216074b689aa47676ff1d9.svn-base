﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>Sleeper by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

   <!-- This line includes the general project style sheet (not required) -->
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- This block defines the styles of the TOC headings, change them as needed -->
   <style type="text/css">
       .navtitle { font-size: 14pt; font-weight: bold; margin-bottom: 16px; }
       .navbar   { font-size: 10pt; }

       .heading1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }

       .hilight1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }

       TD.toc { padding-bottom: 2px; padding-right: 4px }
   </style>
</head>
<body style="background: #FFFFFF; url(null) fixed no-repeat">
<p class="navtitle">Sleeper by PassMark Software</p>
<p class="navbar">
<b>Contents</b>
 | <a href="sleeper_kwindex_static.html">Index</a>

</p><hr size="1" />

<!-- Place holder for the TOC - this variable is REQUIRED! -->
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_overview.htm" target="hmcontent"><span id="s1" class="heading1">Introduction and Overview</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_sleepstates.htm" target="hmcontent"><span id="s2" class="heading1">Sleep State Types</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_ui.htm" target="hmcontent"><span id="s3" class="heading1">The User Interface</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="warnings_and_errors.htm" target="hmcontent"><span id="s4" class="heading1">Warnings and Errors</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_config.htm" target="hmcontent"><span id="s5" class="heading1">Configuration Options</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_commandline.htm" target="hmcontent"><span id="s6" class="heading1">Command line options</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_systemreq.htm" target="hmcontent"><span id="s7" class="heading1">System requirements</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_contacts.htm" target="hmcontent"><span id="s8" class="heading1">Contacting PassMark Software</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="hid_copyright.htm" target="hmcontent"><span id="s9" class="heading1">Copyright and License</span></a></td></tr></table>


<hr size="1" /><p style="font-size: 8pt">Sleeper 2.3</p>
</body>
</html>


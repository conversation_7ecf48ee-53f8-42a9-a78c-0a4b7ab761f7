Version 1.1.0 
'TEST SETUP ====================================================================
'Test Description
	
'Run Time
'	hours      minutes    seconds
	24         0          0
'Ramp Up Time (s)
	5
'Default Disk Workers to Spawn
	NUMBER_OF_CPUS
'Default Network Workers to Spawn
	0
'Record Results
	ALL
'Worker Cycling
'	start      step       step type
	1          1          LINEAR
'Disk Cycling
'	start      step       step type
	1          1          LINEAR
'Queue Depth Cycling
'	start      end        step       step type
	1          32         2          EXPONENTIAL
'Test Type
	NORMAL
'END test setup
'RESULTS DISPLAY ===============================================================
'Record Last Update Results,Update Frequency,Update Type
	ENABLED,1,LAST_UPDATE
'Bar chart 1 statistic
	Total I/Os per Second
'Bar chart 2 statistic
	Total MBs per Second (Decimal)
'Bar chart 3 statistic
	Average I/O Response Time (ms)
'Bar chart 4 statistic
	Maximum I/O Response Time (ms)
'Bar chart 5 statistic
	% CPU Utilization (total)
'Bar chart 6 statistic
	Total Error Count
'END results display
'ACCESS SPECIFICATIONS =========================================================
'Access specification name,default assignment
	4KRandom100%Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,100,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,0,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Read80%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,80,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Read50%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,50,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Read20%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,20,100,0,1,4096,0
'Access specification name,default assignment
	4RW_1PD_55_FR_10IO_FS_4_1M_SWRR_24H,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,50,4,0,0,10,4096,0
	1048576,50,96,100,0,10,1048576,0
'Access specification name,default assignment
	4RW_1PD_55_FR_10IO_FS_512_2M_SWRR_24H,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,50,4,0,0,10,524288,0
	2097152,50,96,100,0,10,2097152,0
'Access specification name,default assignment
	4WR_1PD_55_FR_10IO_FS_4_1M_RRSW_24H,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,50,96,100,0,10,4096,0
	1048576,50,4,0,0,10,1048576,0
'Access specification name,default assignment
	4WR_1PD_55_FR_10IO_FS_512_2M_RRSW_24H,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,50,96,100,0,10,524288,0
	2097152,50,4,0,0,10,2097152,0
'Access specification name,default assignment
	4W_1PD_FR_10IO_FS_RR_24H,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,96,100,0,10,4096,0
'END access specifications
'MANAGER LIST ==================================================================
'Manager ID, manager name
	1,MB0783
'Manager network address
	
'Worker
	Worker 1
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	10,DISABLED,1,ENABLED,55
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	4W_1PD_FR_10IO_FS_RR_24H
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'End manager
'END manager list
Version 1.1.0 

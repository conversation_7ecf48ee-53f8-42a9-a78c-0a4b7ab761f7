#pragma once
class CPrivateCmdCommunication
{
public:
	CPrivateCmdCommunication(void);
	~CPrivateCmdCommunication(void);

	std::string ReadUSBSampleNo(char _cletter);

	std::string ReadSDSampleNo(char _cletter);

	std::string ReadEMMCSampleNo(char _cletter);

	std::string ReadUSBSampleNoByPhyID(int _nPhyID);

	std::string ReadSDSampleNoByPhyID(int _nPhyID);

	std::string ReadEMMCSampleNoByPhyID(int _nPhyID);

	std::string GetErrInfo();
private:
	std::string m_strErrInfo;
};


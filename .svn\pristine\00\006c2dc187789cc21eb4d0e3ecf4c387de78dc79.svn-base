#pragma once
class IISQLOperate;
#include <StatusUpload/IStatusUpload.h>
#include <afxmt.h>

class CTestStatus
{
public:
	CTestStatus(void);
	virtual ~CTestStatus(void);

	bool UpdateTestStatus(TEST_STATUS* _pStatus);

	void ReleaseResource();

	bool UpdateCaseTestStatus(TEST_CASE_STATUS* _pStatus);

protected:
	bool TryConnect();
private:
	IISQLOperate *m_pSqlOperator;

	std::string m_strErrInfo;
	bool m_bConnected;
	CCriticalSection m_csLock;
};


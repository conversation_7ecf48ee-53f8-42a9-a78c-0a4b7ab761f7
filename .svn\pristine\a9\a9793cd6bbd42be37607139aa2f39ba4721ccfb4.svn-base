<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>f9d7bf5d-4d72-4612-9909-be37e4906547</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>SDReport.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>SDReport</Name>
    <RootNamespace>SDReport</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="BurnIn.py" />
    <Compile Include="CardReader.py" />
    <Compile Include="DataCoverage.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="EmptyChunk.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="YSEnvironmentTest.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ErrDiskInfo.py" />
    <Compile Include="Function.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="HTLTBurnin.py" />
    <Compile Include="MP50Times.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MPFormat.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MPTool48H.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MPToolGivenCount.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MP_Function.py" />
    <Compile Include="MP_H2.py" />
    <Compile Include="Nano.py" />
    <Compile Include="Performance.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="PerformanceCompetition.py" />
    <Compile Include="PerformanceSimple.py" />
    <Compile Include="PowerOffTest.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="PublicFuc.py" />
    <Compile Include="Retry.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="SDReport.py" />
    <Compile Include="Summary.py" />
    <Compile Include="VideoCheckTest.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="YSMpengVideoCheckTest.py" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="env\">
      <Id>env</Id>
      <Version>3.7</Version>
      <Description>env (Python 3.7 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <ItemGroup>
    <Content Include="requirements.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
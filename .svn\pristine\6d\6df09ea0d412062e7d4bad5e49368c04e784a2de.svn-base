Version 1.1.0 
'TEST SETUP ====================================================================
'Test Description
	
'Run Time
'	hours      minutes    seconds
	24         0          0
'Ramp Up Time (s)
	5
'Default Disk Workers to Spawn
	NUMBER_OF_CPUS
'Default Network Workers to Spawn
	0
'Record Results
	ALL
'Worker Cycling
'	start      step       step type
	1          1          LINEAR
'Disk Cycling
'	start      step       step type
	1          1          LINEAR
'Queue Depth Cycling
'	start      end        step       step type
	1          10         1          EXPONENTIAL
'Test Type
	NORMAL
'END test setup
'RESULTS DISPLAY ===============================================================
'Record Last Update Results,Update Frequency,Update Type
	DISABLED,1,LAST_UPDATE
'Bar chart 1 statistic
	Total I/Os per Second
'Bar chart 2 statistic
	Total MBs per Second (Decimal)
'Bar chart 3 statistic
	Average I/O Response Time (ms)
'Bar chart 4 statistic
	Maximum I/O Response Time (ms)
'Bar chart 5 statistic
	% CPU Utilization (total)
'Bar chart 6 statistic
	Total Error Count
'END results display
'ACCESS SPECIFICATIONS =========================================================
'Access specification name,default assignment
	Default,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	2048,100,67,100,0,1,2048,0
'Access specification name,default assignment
	4WR_1PD_55_FR_10IO_FS_512_2M_RRSW_24H,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,50,96,100,0,1,524288,0
	2097152,50,4,0,0,10,2097152,0
'END access specifications
'MANAGER LIST ==================================================================
'Manager ID, manager name
	1,JON-PC
'Manager network address
	
'Worker
	Worker 1
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	10,DISABLED,1,ENABLED,55
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	4WR_1PD_55_FR_10IO_FS_512_2M_RRSW_24H
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 2
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	10,DISABLED,1,ENABLED,55
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	4WR_1PD_55_FR_10IO_FS_512_2M_RRSW_24H
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 3
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	10,DISABLED,1,ENABLED,55
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	4WR_1PD_55_FR_10IO_FS_512_2M_RRSW_24H
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'Worker
	Worker 4
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	10,DISABLED,1,ENABLED,55
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	4WR_1PD_55_FR_10IO_FS_512_2M_RRSW_24H
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'End manager
'END manager list
Version 1.1.0 

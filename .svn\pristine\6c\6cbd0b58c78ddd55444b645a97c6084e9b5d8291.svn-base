/* Text Styles */
hr { color: #000000}
body, table, tr, th /* Normal */
{
 font-size: 10pt;
 font-family: Arial,Helvetica,sans-serif;
 font-style: normal;
 font-weight: normal;
 color: #000000;
 text-decoration: none;
}
span.f_CodeExample /* Code Example */
{
 font-size: 8pt;
 font-family: 'Courier New';
}
span.f_Comment /* Comment */
{
}
span.f_CourierFixed /* Courier Fixed */
{
 font-size: 9pt;
 font-family: 'Courier New';
}
span.f_Heading1 /* Heading1 */
{
 font-size: 14pt;
 font-weight: bold;
 color: #ffffff;
}
span.f_ImageCaption /* Image Caption */
{
 font-size: 8pt;
 font-weight: bold;
}
span.f_Notes /* Notes */
{
}
/* Paragraph styles */
p /* Normal */
{
 text-align: left;
 text-indent: 0px;
 padding: 0px 0px 0px 0px;
 margin: 0px 0px 0px 0px;
}
.p_CodeExample /* Code Example */
{
 font-size: 8pt;
 line-height: 1.0;
 white-space: nowrap;
 page-break-inside: avoid;
}
.p_Comment /* Comment */
{
 font-size: 10pt;
}
.p_Heading1 /* Heading1 */
{
 font-size: 100%;
 font-weight: normal;
 text-align: left;
 text-indent: 0px;
 padding: 0px 0px 0px 0px;
 margin: 0px 0px 0px 0px;
}
.p_ImageCaption /* Image Caption */
{
 font-size: 8pt;
}
.p_Notes /* Notes */
{
 font-size: 10pt;
}


#hmpopupdiv /* used for javascript text popups */
{
  display: none;
  position: absolute;
  z-index: 1000;
  background-color: #FFFFFF;
  padding: 6px;
  border:1px solid #000000;
  border-radius: 5px;
  box-shadow: 5px 5px 5px #888;
}

#hmlightbox /* image lightbox */
{
  border: none;
  padding: 8px;
  background: #FFF;
  font-size: 110%;
  font-weight: bold;
  color: #777;
  box-shadow: 5px 5px 5px #888;
}

a.dropdown-toggle /* dropdown toggle caption */
{
  cursor: pointer;
}
a.inline-toggle /* inline toggle caption */
{
  cursor: pointer;
}
img.dropdown-toggle-icon /* toggle icon */
{
  cursor: pointer;
}
/* CSS for responsive image maps */

.hmImageMap a.hmHotspotRect { display:block; position:absolute; border: 1px solid transparent; background:#000; opacity:0.01; filter:alpha(opacity=1)  }
.hmImageMap a.hmHotspotEllipse { display:block; position:absolute; border-radius:50%; border: 1px solid transparent; background:#000; opacity:0.01; filter:alpha(opacity=1) }
.hmImageMap:hover a.hmHotspotRect {opacity:0.3; filter:alpha(opacity=30); }
.hmImageMap:hover a.hmHotspotEllipse { opacity:0.3; filter:alpha(opacity=30); }
a.hmHotspotRect:hover { border:1px solid #000; background:#FFF; opacity:0.3; filter:alpha(opacity=30) }
a.hmHotspotEllipse:hover { border:1px solid #000; background:#FFF; opacity:0.3; filter:alpha(opacity=30) }

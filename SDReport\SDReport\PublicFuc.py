import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta
import ErrDiskInfo
from win32com.client import Dispatch

MPDATA_DIR = ""
REPORT_DIR = ""

minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
warnFill = PatternFill('solid', fgColor='FF0000')
unfinishedFill = PatternFill('solid', fgColor='FFFF33')
maxFill = PatternFill('solid', fgColor='64C8FF')
commonSmartKey = ['F1','F2','A5','A6','05','0C','A3','A4','A7','AF','B2','B5','B6','C0','C3','C4','C5','C6','C7']

errDiskLst = []
fileLst = []
config = configparser.RawConfigParser()
def GetAllFile(curpath):
    for dirpath,dirnames,filenames in os.walk(curpath):
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)

def WriteErrDiskFile(strFile):
    if 0 != len(errDiskLst):
        with open(strFile,'w+') as file: 
            for errLst in errDiskLst:
                strErr = '样片:%s    PC:%s    Err:%s    Time:%s\n'%(errLst[0],errLst[1],errLst[2],errLst[3])
                file.write(strErr)


def ReadYSMpegToolCsvData(curpath, pattern, dataDic):
    fileIdx = 1
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        file_data = []
        with open(file,encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中            
                file_data.append(row)

        dataDic[fileIdx]=file_data
        fileIdx += 1


#1个样片多条记录
def WriteCsvData(worksheet, startLine, dataDic):
    curLine = startLine

    rowIdx = 1
    for key in dataDic:
        for line in dataDic[key]:
            for col in range(len(line)+1):#columCnt + 1
                try:
                    if 0 == col:
                        #第一列是编号，直接填行号
                        worksheet['%s%d'%(get_column_letter(1), curLine)] = rowIdx
                    elif 1 == col:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[1]
                    elif 2 == col:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[0]
                    else:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[col-1]
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)]
                    worksheet['%s%d'%(get_column_letter(col+1), curLine)].alignment = alignment
                    worksheet['%s%d'%(get_column_letter(col+1), curLine)].border = my_border('thin', 'thin', 'thin', 'thin')
                    
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            rowIdx += 1

    return  curLine   


    #定义边框样式
def my_border(t_border, b_border, l_border, r_border):
    border = Border(top=Side(border_style=t_border, color=colors.BLACK),
                    bottom=Side(border_style=b_border, color=colors.BLACK),
                    left=Side(border_style=l_border, color=colors.BLACK),
                    right=Side(border_style=r_border, color=colors.BLACK))
    return border

#从SSD那边拷贝过来的
def ReadQaIniDataEx(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
                fileMdTime = os.path.getmtime(file)
                dataDic[sec]['_file_time'] = fileMdTime

            tempLst = []
            pcNo = ''

            oldTime = dataDic[sec]['_file_time']
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime - 1:#减掉1是为了避免第一次的数据因为时间精度卡点问题未被统计，减1。
                continue#数据不是新的，不做读取覆盖

            dataDic[sec]['_file_time'] = fileMdTime

            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                    if 'qa_err_msg' == key:
                        filemt= time.localtime(os.stat(file).st_mtime)  
                        strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                        errDiskLst.append([sec,pcNo,value,strTime])
                else:
                    tempLst.append('')
            if len(dataDic[sec]) < recordCnt and [] != tempLst:
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec].append(tempLst)

def ReadQaIniData(curpath, pattern, dataDic, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            if caseKey not in dataDic[sec]:
                #代表此种数据为此样片的第一次数据
                tempLst = []
                pcNo = ''
                fileMdTime = os.path.getmtime(file)
                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                            errCode = value.upper()
                            if errCode != '' and errCode != 'PASS' and errCode != 'TRUE':
                                if caseKey == 'HT_BIT':
                                    AppendErrDiskInfo('HT BIT_Err',sec,value,pcNo,file)
                                elif caseKey == 'LT_BIT':
                                    AppendErrDiskInfo('LT BIT_Err',sec,value,pcNo,file)
                                
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst
            else:
                #代表此种数据为此样片再次遇到的数据，此时如果遇到的是最新数据，需要覆盖。
                tempLst = []
                pcNo = ''

                oldTime = dataDic[sec][GetTimeKeyName(caseKey)]
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖

                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime

                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                            errCode = value.upper()
                            if errCode != '' and errCode != 'PASS' and errCode != 'TRUE':
                                if caseKey == 'HT_BIT':
                                    AppendErrDiskInfo('HT BIT_Err',sec,value,pcNo,file)
                                elif caseKey == 'LT_BIT':
                                    AppendErrDiskInfo('LT BIT_Err',sec,value,pcNo,file)
                                
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst

def ReadMarsIniData(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue

        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][GetTimeKeyName(caseKey)] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])
                if testResult != '' and testResult != 'PASS' and testResult != 'TRUE':
                    if caseName == 'AT_H2':
                        AppendErrDiskInfo('H2_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)
                    elif caseName == 'AT_CopyFile':
                        AppendErrDiskInfo('Copy File_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst
        else:
            #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
            oldTime = dataDic[keyName][GetTimeKeyName(caseKey)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            dataDic[keyName][GetTimeKeyName(caseKey)] = fileMdTime

            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])
                if testResult != '' and testResult != 'PASS' and testResult != 'TRUE':
                    if caseName == 'AT_H2':
                        AppendErrDiskInfo('H2_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)
                    elif caseName == 'AT_CopyFile':
                        AppendErrDiskInfo('Copy File_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst


def ReadMarsIniDataEx(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding='gbk')
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']

            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            for key in config[caseName]:
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
            dataDic[keyName]['file_path'] = file
        else:
            #重复样片，取最新的
            oldTime = dataDic[keyName][GetTimeKeyName(caseName)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖
            
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            #dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']


            for key in config[caseName]:
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
            dataDic[keyName]['file_path'] = file

def WriteDataAndImage(worksheet, startLine, imageLine, dataDic, caseKey, colLst, keyLst, imgWidth = 260, imgHeight = 240, startCol = 2, colCnt = 3):
    curLine = startLine
    imageCol = startCol
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                if  'Cap' == keyLst[0]:
                    keyNo = '%s-%sG'%(key, line[0])
                worksheet['%s%d'%(col, curLine)] = keyNo
            else:
                worksheet['%s%d'%(col, curLine)] = line[index]
        # 列表最后一项是图片路径
        if '' != line[-1]:
            img = Image(line[-1])
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        curLine += 1
        imageCol += colCnt

#1个样片多条记录
def WriteData(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1,resultColNameList = []):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                        if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                            if line[index-1] >= 400:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                        if col in resultColNameList:
                            strResult = line[index-1].upper()
                            if strResult != 'TRUE' and strResult != 'PASS' and strResult != '':
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    worksheet['%s%d'%(col, curLine)].alignment = alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += lineCnt
            startLine += 1
    return startLine
#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteDataNormal(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1,resultColNameList = []):
    curLine = startLine
    for key in dataDic:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    if 'A5-A6' == keyLst[index-1]:
                        if line[index-1] >= 400:
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                    if col in resultColNameList:
                        strResult = line[index-1].upper()
                        if strResult == 'UNFINISHED':
                            #填写独特的颜色
                            worksheet['%s%d'%(col, curLine)].fill = unfinishedFill
                        elif strResult != 'TRUE' and strResult != 'PASS' and strResult != '':
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += lineCnt

#获取每个编号每列数据的最值
def GetMaxOrMinValueLst(keyLst,dataDic):
    for key in dataDic:
        resultLst = []
        for index,col in enumerate(keyLst):
            tempLst = [line[index] for line in dataDic[key]]
            limitData = 0
            bFirstData = True
            for data in tempLst:
                try:
                    tempData = float(data)
                    #部分列需要取最小值，例如时间等
                    if bFirstData:
                        limitData = tempData
                        bFirstData = False
                        continue
                    if col in minKey:
                        if tempData < limitData:
                            limitData = tempData
                    else:
                        if tempData > limitData:
                            limitData = tempData
                except:
                    continue
            resultLst.append(limitData)
        dataDic[key].append(resultLst)

def FmtStrHex(strHex):
    #去掉十六进制前面的多个0
    strNew = strHex.lstrip('0')
    if '' == strNew:
        strNew = '0'
    return strNew

def GetImtResultPath(strImtBmpPath, key):
    pos = strImtBmpPath.rfind('\\')
    strPath = strImtBmpPath[:pos+1] + 'inst%s_IometerResults.csv'%key
    return strPath

def GetNewIoMeterDic(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-1:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewBitDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G)/F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            write = 0
            if smartLst[0] != '':
                write = (int(smartLst[0],16)*32)//1024
            read = 0
            if smartLst[1] != '':
                read = (int(smartLst[1],16)*32)//1024
            newLst.append('%d/%d'%(write,read))
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic

def ReadQaIniDataForNano(curpath, pattern, dataDic, caseKey, keyLst, imageSuffix, dicMax, caseName, diskCnt = 6):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            if caseKey not in dataDic[sec]:
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                        if '' != value:
                            fValue = float(value)
                            # 区分hdtune读写相同字段，避免最值比较混乱
                            maxKey = '%s_%s'%(caseName, key)
                            if maxKey not in dicMax or fValue > dicMax[maxKey]:
                                dicMax[maxKey] = fValue
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst

def WriteDataAndImageForNano(worksheet, wsPic, startLine, dataDic, itemLst, startColLst, keyLst, dicMax, caseLst, picColLst):
    imgWidth = 240
    imgHeight = 190
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        worksheet['F%d'%curLine] = key
        wsPic['F%d'%curLine] = key
        for itemIdx,itLst in enumerate(itemLst):
            for itIdx,itLst in enumerate(itLst):
                itemLine = curLine
                startCol = startColLst[itemIdx][itIdx]
                for item in itLst:
                    curCol = column_index_from_string(startCol)
                    if item not in dataDic[key]:
                        itemLine += 1
                        continue
                    for idx,data in enumerate(dataDic[key][item][:-1]):
                        worksheet['%s%d'%(get_column_letter(curCol), itemLine)] = data
                        maxKey = '%s_%s'%(caseLst[itemIdx], keyLst[itemIdx][idx])
                        if '' != data and maxKey in dicMax:
                            fData = float(data)
                            if fData == dicMax[maxKey]:
                                worksheet['%s%d'%(get_column_letter(curCol), itemLine)].fill = maxFill
                            elif fData <= dicMax[maxKey]*0.8:
                                worksheet['%s%d'%(get_column_letter(curCol), itemLine)].fill = warnFill
                        curCol+=1
                    # 列表最后一项是图片路径
                    picPath = dataDic[key][item][-1]
                    if '' != picPath:
                        img = Image(picPath)
                        img.width = imgWidth
                        img.height = imgHeight
                        wsPic.add_image(img, '%s%d'%(picColLst[itemIdx][itIdx], itemLine))
                    itemLine += 1
        curLine += 6


#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName

def GetValueFromDic(dataDic, key,defaultVulue = ''):
    if key in dataDic:
        return dataDic[key]
    else:
        return defaultVulue


#获取测试时间
def GetTestTimeStrFromDic(dic):
    #测试时间
    endTimeStr = GetValueFromDic(dic,'end_time')
    startTimeStr = GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return ''
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
            return timeStr
        except:
            return ''

#获取测试时间
def GetTestTimeValueFromDic(dic):
    #测试时间
    endTimeStr = GetValueFromDic(dic,'end_time')
    startTimeStr = GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return 0
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            return totalSecond
        except:
            return 0

def GetIndex(element,elementlist):
    elementIdx = -1
    try:
        elementIdx = elementlist.index(element)
    except:
        elementIdx = -1
    return elementIdx

def Safe_float(rawData):
    newData = ''
    try:
        newData = float(rawData)
    except:
        newData = ''
    return newData

def AppendErrDiskInfo(errTypeName,sampleID,errCode,pcNo,logFilePath):
    errDiskInfo = []
    errDiskInfo.append(sampleID)
    errDiskInfo.append(errCode)
    errDiskInfo.append(pcNo)
    errDiskInfo.append(logFilePath)
    if IsInErrDiskSet(errTypeName,sampleID) == False:
        ErrDiskInfo.g_dicErrDisk[errTypeName].append(errDiskInfo)
    else:
        #比较时间，将最新记录的数据传上去
        idx = GetErrDiskInfoIdx(errTypeName,sampleID)
        if idx != -1:
            orgErrInfo = ErrDiskInfo.g_dicErrDisk[errTypeName][idx]
            oldTime = os.path.getmtime(orgErrInfo[-1])
            curTime = os.path.getmtime(logFilePath)
            if curTime > oldTime:
                orgErrInfo[1] = errCode
                orgErrInfo[2] = pcNo
                orgErrInfo[3] = logFilePath

#判定此样本编号是否已经在错误列表中
def IsInErrDiskSet(errTypeName,sampleID):
    for errDisk in ErrDiskInfo.g_dicErrDisk[errTypeName]:
        sampleNo = errDisk[0]
        if sampleNo == sampleID:
            return True
    return False

#判定此样本编号是否已经在错误列表中
def GetErrDiskInfoIdx(errTypeName,sampleID):
    idx = 0
    for errDisk in ErrDiskInfo.g_dicErrDisk[errTypeName]:
        sampleNo = errDisk[0]
        if sampleNo == sampleID:
            return idx
        idx += 1
    return -1

#获取样本编号的值
def GetOrderNoFromSampleNo(_sampleNo = 'AI-JGS-001'):
    nOrderNo = -1 #无效编号
    pattern = '\d+$'
    ret = re.findall(pattern, _sampleNo)
    if ret:
        strOrder = ret[-1]
        nOrderNo = int(strOrder)
    return nOrderNo

def GetTimeKeyName(caseKey):
    timeKeyName = caseKey + '_file_time'
    return timeKeyName

#获取当前日期
def GetDate():
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d', filemt)
    return strTime

def JustOpenCloseExcelFile(filename):
    try:
        xlApp = Dispatch("Excel.Application")
        xlApp.Visible = False
        xlBook = xlApp.Workbooks.Open(filename)
        xlBook.Save()
        xlBook.Close(SaveChanges=True)
        xlApp.Quit()
        return True
    except:
        try:
            xlBook.Close(SaveChanges=True)
            xlApp.Quit()
            return False
        except:
            xlApp.Quit()
            return False

def TryRepeatOpenCloseExcelFile(filename):
    for i in range(1,20):
        if JustOpenCloseExcelFile(filename):
            break
        time.sleep(5)
        


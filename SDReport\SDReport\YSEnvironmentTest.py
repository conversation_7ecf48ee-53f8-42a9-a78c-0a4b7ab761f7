import PublicFuc
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

readDisturbAnyTime = ''

def Run(curpath, workBook, alignment):
    ws = workBook['Environment Test']
    ws.alignment = alignment
    ProReadDisturbance(curpath, ws,16)
    ProRetention(curpath,ws,40)
    ProRetention4647(curpath,ws,40)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'H',2)
    #ProEntry(workBook)

def ProReadDisturbance(curpath,worksheet,recordCnt):
    pattern = '.+\\\\Plan12\\\\T_GE_SD_C51\\\\ReadDisturb\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'ReadDisturb'
    caseDicReadDisturb = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicReadDisturb, caseName, recordCnt)

    global readDisturbAnyTime
    readDisturbAnyTime = GetMaxReadDisturbTime(caseDicReadDisturb)

    keyLst = ['capacity','pc_no','wavg_H2','ravg_H2','test_time','circle','result',
             'RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewReadDisturbReportDic(caseDicReadDisturb)

    colLst = ['B','C','D','E','F','G','H','I',
              'J','K','L','M','N','O','P','Q','R','U']

    startLine = 95
   
    #写内容
    resultColumnList = ['I']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst,1,resultColumnList)

def ProRetention(curpath,worksheet,recordCnt):
    #得到数据
    dicH2Data = {}
    pattern = '.+\\\\Plan36\\\\T_GE_SD_C7\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsH2Key = ['Cap','MMS_PC','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    ReadMarsIniDataLocal(curpath, pattern, dicH2Data, 'AT_H2', 'H2_1', marsH2Key, '',recordCnt)

    pattern = '.+\\\\Plan37\\\\T_GE_SD_C7\\\\Mars_H2_Verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsH2VerifyKey = ['Cap','MMS_PC','AVERAGE_READ_VEL','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    ReadMarsIniDataLocal(curpath, pattern, dicH2Data, 'AT_Verify_H2', 'H2_2', marsH2VerifyKey, '',recordCnt)

    colLst = ['B','C','D','E','F','G',
              'K','L','M','N','O','P','Q','R','S','V']
    startLine = 7
    #写内容
    WriteDataLocal(worksheet, startLine, dicH2Data, 'H2_1', colLst,'G')

    colLst = ['B','C','H','I','J',
              'K','L','M','N','O','P','Q','R','S','V']
    startLine = 7
    #写内容
    WriteDataLocal(worksheet, startLine, dicH2Data, 'H2_2', colLst,'J')

def ProRetention4647(curpath,worksheet,recordCnt):
    #得到数据
    dicH2Data = {}
    pattern = '.+\\\\Plan46\\\\T_GE_SD_C31\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsH2Key = ['Cap','MMS_PC','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    ReadMarsIniDataLocal(curpath, pattern, dicH2Data, 'AT_H2', 'H2_1', marsH2Key, '',recordCnt)

    pattern = '.+\\\\Plan47\\\\T_GE_SD_C31\\\\Mars_H2_Verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsH2VerifyKey = ['Cap','MMS_PC','AVERAGE_READ_VEL','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    ReadMarsIniDataLocal(curpath, pattern, dicH2Data, 'AT_Verify_H2', 'H2_2', marsH2VerifyKey, '',recordCnt)

    colLst = ['B','C','D','E','F','G',
              'K','L','M','N','O','P','Q','R','S','V']
    startLine = 51
    #写内容
    WriteDataLocal(worksheet, startLine, dicH2Data, 'H2_1', colLst,'G')

    colLst = ['B','C','H','I','J',
              'K','L','M','N','O','P','Q','R','S','V']
    startLine = 51
    #写内容
    WriteDataLocal(worksheet, startLine, dicH2Data, 'H2_2', colLst,'J')

#获取ReadDisturb的时间
def GetMaxReadDisturbTime(caseDicReadDisturb):
    strTime = ''
    maxTimeInSeconds = 0
    for sampleNo in caseDicReadDisturb:
        curTimeSeconds = PublicFuc.GetTestTimeValueFromDic(caseDicReadDisturb[sampleNo])
        if curTimeSeconds > maxTimeInSeconds:
            maxTimeInSeconds = curTimeSeconds

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#填写封面
def ProEntry(workBook):
    ws = workBook['Full Test Reports']
    strTime = readDisturbAnyTime
    ws['K37'] = strTime

def GetNewReadDisturbReportDic(caseDicReadDisturb):
    newDic = {}
    for key in caseDicReadDisturb:
        newDic[key] = []
        dic = caseDicReadDisturb[key]   

        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位

        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])

        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

        #测试时间
        strTime = PublicFuc.GetTestTimeStrFromDic(dic)
        newDic[key].append(strTime)

        testCycle = PublicFuc.GetValueFromDic(dic,'teset_circle')
        if '' == testCycle:
            newDic[key].append('')
        else:
            newDic[key].append(int(testCycle))

        #测试结果
        testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
        newDic[key].append(testResult)
        if testResult != '' and testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
            PublicFuc.AppendErrDiskInfo('Read Disturb_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
        
        #smart信息
        if key in caseDicReadDisturb:#如果键值在里面
            dic = caseDicReadDisturb[key]
            if 'RetryCnt'.lower() in dic:
                if ''== dic['RetryCnt'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['RetryCnt'.lower()])
            else:
                newDic[key].append('')
          
            if 'SLCBadBlock_New'.lower() in dic:
                if ''== dic['SLCBadBlock_New'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['SLCBadBlock_New'.lower()])
            else:
                newDic[key].append('')

            if 'TLCBadBlock_New'.lower() in dic:
                if ''== dic['TLCBadBlock_New'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['TLCBadBlock_New'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_MAX'.lower() in dic:
                if ''== dic['WL_SLC_MAX'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['WL_SLC_MAX'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_MIN'.lower() in dic:
                if ''== dic['WL_SLC_MIN'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['WL_SLC_MIN'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_AVG'.lower() in dic:
                if ''== dic['WL_SLC_AVG'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['WL_SLC_AVG'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_MAX'.lower() in dic:
                if ''== dic['WL_TLC_MAX'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['WL_TLC_MAX'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_MIN'.lower() in dic:
                if ''== dic['WL_TLC_MIN'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['WL_TLC_MIN'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_AVG'.lower() in dic:
                if ''== dic['WL_TLC_AVG'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['WL_TLC_AVG'.lower()])
            else:
                newDic[key].append('')

            if 'PowerUpCnt'.lower() in dic:
                if ''== dic['PowerUpCnt'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['PowerUpCnt'.lower()])
            else:
                newDic[key].append('')
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

    return newDic 



def ReadMarsIniDataLocal(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 0):
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue
        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap)*1024)))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            resultStr = PublicFuc.GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
                if resultStr.upper() != '' and resultStr.upper() != 'TRUE' and resultStr.upper() != 'PASS':
                    if caseName == 'AT_H2':
                        PublicFuc.AppendErrDiskInfo('H2_Err',keyName,resultStr,config['HWCONFIG']['MMS_PC'],file)
                    elif caseName == 'AT_Verify_H2':
                        PublicFuc.AppendErrDiskInfo('Data Retention_Err',keyName,resultStr,config['HWCONFIG']['MMS_PC'],file)
                

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst
        else:
            #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
            oldTime = dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap)*1024)))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            resultStr = PublicFuc.GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
                if resultStr.upper() != '' and resultStr.upper() != 'TRUE' and resultStr.upper() != 'PASS':
                    if caseName == 'AT_H2':
                        PublicFuc.AppendErrDiskInfo('H2_Err',keyName,resultStr,config['HWCONFIG']['MMS_PC'],file)
                    elif caseName == 'AT_Verify_H2':
                        PublicFuc.AppendErrDiskInfo('Data Retention_Err',keyName,resultStr,config['HWCONFIG']['MMS_PC'],file)
                

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst


#resultIdx代表数据的结果索引
def WriteDataLocal(worksheet, startLine, dataDic, caseKey, colLst,resultCol = ''):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                worksheet['%s%d'%(col, curLine)] = keyNo
            else:
                worksheet['%s%d'%(col, curLine)] = line[index-1]
                if resultCol != '' and col == resultCol:
                    if line[index-1].upper() != 'TRUE' and line[index-1].upper() != 'PASS':
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
        curLine += 1
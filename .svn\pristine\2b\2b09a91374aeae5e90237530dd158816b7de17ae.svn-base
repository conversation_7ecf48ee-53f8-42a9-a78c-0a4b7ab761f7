Version 1.1.0 
'TEST SETUP ====================================================================
'Test Description
	
'Run Time
'	hours      minutes    seconds
	0          4          0
'Ramp Up Time (s)
	0
'Default Disk Workers to Spawn
	NUMBER_OF_CPUS
'Default Network Workers to Spawn
	0
'Record Results
	ALL
'Worker Cycling
'	start      step       step type
	1          1          LINEAR
'Disk Cycling
'	start      step       step type
	1          1          LINEAR
'Queue Depth Cycling
'	start      end        step       step type
	1          32         2          EXPONENTIAL
'Test Type
	NORMAL
'END test setup
'RESULTS DISPLAY ===============================================================
'Record Last Update Results,Update Frequency,Update Type
	ENABLED,1,LAST_UPDATE
'Bar chart 1 statistic
	Total I/Os per Second
'Bar chart 2 statistic
	Total MBs per Second (Decimal)
'Bar chart 3 statistic
	Average I/O Response Time (ms)
'Bar chart 4 statistic
	Maximum I/O Response Time (ms)
'Bar chart 5 statistic
	% CPU Utilization (total)
'Bar chart 6 statistic
	Total Error Count
'END results display
'ACCESS SPECIFICATIONS =========================================================
'Access specification name,default assignment
	Default,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	2048,100,67,100,0,1,2048,0
'Access specification name,default assignment
	SSD_Random_4KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,0,100,0,1,4096,0
'Access specification name,default assignment
	SSD_Random_4KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,100,100,0,1,4096,0
'Access specification name,default assignment
	SSD_Random_64KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	65536,100,100,100,0,1,65536,0
'Access specification name,default assignment
	SSD_Random_64KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	65536,100,0,100,0,1,65536,0
'Access specification name,default assignment
	SSD_Random_128KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	131072,100,100,100,0,1,131072,0
'Access specification name,default assignment
	SSD_Random_128KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	131072,100,0,100,0,1,131072,0
'Access specification name,default assignment
	SSD_Random_512KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,100,100,100,0,1,524288,0
'Access specification name,default assignment
	SSD_Random_512KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,100,0,100,0,1,524288,0
'Access specification name,default assignment
	SSD_Seq_4KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,100,0,0,1,4096,0
'Access specification name,default assignment
	SSD_Seq_4KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,0,0,0,1,4096,0
'Access specification name,default assignment
	SSD_Seq_64KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	65536,100,100,0,0,1,65536,0
'Access specification name,default assignment
	SSD_Seq_64KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	65536,100,0,0,0,1,65536,0
'Access specification name,default assignment
	SSD_Seq_128KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	131072,100,100,0,0,1,131072,0
'Access specification name,default assignment
	SSD_Seq_128KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	131072,100,0,0,0,1,131072,0
'Access specification name,default assignment
	SSD_Seq_512KB_Read100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,100,100,0,0,1,524288,0
'Access specification name,default assignment
	SSD_Seq_512KB_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	524288,100,0,0,0,1,524288,0
'Access specification name,default assignment
	SSD_Seq_1M_Write100%,DISK
'size,% of size,% reads,% random,delay,burst,align,reply
	1048576,100,0,0,0,1,1048576,0
'END access specifications
'MANAGER LIST ==================================================================
'Manager ID, manager name
	1,JON-PC
'Manager network address
	
'Worker
	Worker 1
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	SSD_Seq_4KB_Write100%
	SSD_Seq_4KB_Read100%
	SSD_Seq_64KB_Write100%
	SSD_Seq_64KB_Read100%
	SSD_Seq_128KB_Write100%
	SSD_Seq_128KB_Read100%
	SSD_Seq_512KB_Write100%
	SSD_Seq_512KB_Read100%
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'End manager
'END manager list
Version 1.1.0 

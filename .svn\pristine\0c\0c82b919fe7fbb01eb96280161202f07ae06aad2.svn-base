#pragma once

enum E_POWER_TYPE
{
	E_POWER_OFF = 0,
	E_POWER_ON,

};

enum E_PRODUCT_TYPE
{
	E_U2 = 0x00,
	E_U3,
	E_SD,
	E_EMMC,
	E_SSD,
	E_<PERSON>IE,
	E_TECH_SATA,
	E_TECH_PCIE,
	E_<PERSON>OP_SATA,
	E_MOP_PCIE,
};

#define     WM_SET_CURINFO				(WM_USER + 1000)
#define     WM_ENABLE_UI				(WM_USER + 1001)
#define     WM_DISABLE_UI				(WM_USER + 1002)

#define		ID_MAIN_TIMER			(WM_USER + 1200)
#define		ID_ITEM_TIMER			(WM_USER + 1201)
#define		ID_WAITTING_TIMER			(WM_USER + 1202)
#define     WM_RECORD_LOG				(WM_USER + 1003)
#define     WM_UPDATE_TEST_NO				(WM_USER + 1004)
#define     WM_UPDATE_MARS_PATH				(WM_USER + 1005)
#define     WM_UPDATE_MARS_PLAN				(WM_USER + 1006)
#define     WM_UPDATE_GROUP				(WM_USER + 1007)
#define     WM_UPDATE_MP_PATH				(WM_USER + 1008)
#define     WM_UPDATE_PRODUCT				(WM_USER + 1009)
#define     WM_UPDATE_TEST_OPERATOR				(WM_USER + 1010)
#define	WM_SET_WAITTING_BEGIN  (WM_USER + 114)
#define	WM_SET_WAITTING_END (WM_USER + 115)
#define	WM_PRODUCT_TYPE_CHANGE (WM_USER + 116)
#define	WM_PLAN_CHANGE (WM_USER + 117)
#define	WM_SET_PLAN (WM_USER + 118)
#define  WM_SET_MATERIAL_NO (WM_USER + 119)
#define	WM_TESTNO_CHANGE (WM_USER + 120)
#define	WM_MOVE_WND_TOP (WM_USER + 121)
#define	WM_LOAD_ATEREX_PLAN (WM_USER + 122)
#define  WM_MESSAGE_DEBUG (WM_USER+1969)
#define     WM_REBOOT_FINISH			(WM_USER + 1968)

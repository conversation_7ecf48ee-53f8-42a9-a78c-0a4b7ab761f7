import PublicFuc
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image

dicPfm = {}

def Run(curpath, workBook, alignment):
    ws = workBook['Performance-Full']
    ws.alignment = alignment
    ProData(curpath, ws, 5)
    ProSumData(curpath, ws)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'H',2)
    
def ProData(curpath, worksheet, maxDiskCnt):
    #mars_h2
    marsH2Key = ['Cap','MMS_PC','FILE_SYSTEM','MAX_WRITE_VEL','MIN_WRITE_VEL']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C13\\\\Mars_H2_1\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    ReadMarsIniDataLocal(curpath, pattern, dicPfm, 'AT_H2', 'MH2_1', marsH2Key, 'Mars.bmp', maxDiskCnt) #测试结果不准确，此统计恰好不用测试结果，因此无影响
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C13\\\\Mars_H2_2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    ReadMarsIniDataLocal(curpath, pattern, dicPfm, 'AT_H2', 'MH2_2', marsH2Key, 'Mars.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C13\\\\Mars_H2_3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    ReadMarsIniDataLocal(curpath, pattern, dicPfm, 'AT_H2', 'MH2_3', marsH2Key, 'Mars.bmp', maxDiskCnt)
    startLine = 37
    mh2Col = ['C','D','A','G','E','F']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'MH2_1', mh2Col, marsH2Key)
    mh2Col = ['C','D','A','J','H','I']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'MH2_2', mh2Col, marsH2Key)
    mh2Col = ['C','D','A','L','M','N']
    WriteDataAndImageLocal(worksheet, startLine, startLine+7, dicPfm, 'MH2_3', mh2Col, marsH2Key)
    #cdm
    cdmKey = ['Cap','pc_no','format','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C15\\\\CDM_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C15\\\\CDM_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_2', cdmKey, 'CDM.bmp', maxDiskCnt)
    startLine = 50
    cdmCol = ['C','D','A','N','E','F','G','H','I','J','K','L']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'CDM_1', cdmCol, cdmKey)
    cdmCol = ['C','D','A','N','O','P','Q','R','S','T','U','V']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'CDM_2', cdmCol, cdmKey)
    #h2testw
    h2Key = ['Cap','pc_no','format','write speed','read speed']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C14\\\\H2testw_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C14\\\\H2testw_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_2', h2Key, 'H2.bmp', maxDiskCnt)
    startLine = 62
    h2Col = ['C','D','A','G','E','F']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'H2_1', h2Col, h2Key)
    h2Col = ['C','D','A','I','J','K']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'H2_2', h2Col, h2Key)
    #iometer
    imtKey = ['Cap','pc_no','format','512K_SEQ_100R_MiBps','512B_SEQ_100W_MiBps','4KALG_Random_100R_MiBps','4KALG_Random_100W_MiBps','512K_SEQ_100R_Iops','512B_SEQ_100W_Iops','4KALG_Random_100R_Iops','4KALG_Random_100W_Iops']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C19\\\\IOmeter_1G_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_1', imtKey, 'IOmeter.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C19\\\\IOmeter_1G_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_2', imtKey, 'IOmeter.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C19\\\\IOmeter_FullCard\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_3', imtKey, 'IOmeter.bmp', maxDiskCnt)
    startLine = 75
    ImtCol = ['C','D','A','E','G','I','K','M','F','H','J','L']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'IMT_1', ImtCol, imtKey, 440, 400, 2, 5)
    ImtCol = ['C','D','A','O','Q','S','U','W','P','R','T','V']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'IMT_2', ImtCol, imtKey, 440, 400, 2, 5)
    ImtCol = ['C','D','A','X','Z','AB','AD','AF','Y','AA','AC','AE']
    WriteDataAndImageLocal(worksheet, startLine, startLine+7, dicPfm, 'IMT_3', ImtCol, imtKey, 440, 400, 2, 5)
    #hdtune
    hdtuneKey =  ['Cap','pc_no','format','min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C16\\\\HDTune_Read_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR1', hdtuneKey, 'HDTune.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C16\\\\HDTune_Write_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW1', hdtuneKey, 'HDTune.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C16\\\\HDTune_Read_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR2', hdtuneKey, 'HDTune.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C16\\\\HDTune_Write_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW2', hdtuneKey, 'HDTune.bmp', maxDiskCnt)
    startLine = 89
    hdtuneCol = ['C','D','A','R','E','F','G','H','I','J']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'HDR1', hdtuneCol, hdtuneKey, 440, 420, 2, 5)
    hdtuneCol = ['C','D','A','R','K','L','M','N','O','P']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'HDW1', hdtuneCol, hdtuneKey, 440, 420, 2, 5)
    hdtuneCol = ['C','D','A','R','S','T','U','V','W','X']
    WriteDataAndImageLocal(worksheet, startLine, startLine+7, dicPfm, 'HDR2', hdtuneCol, hdtuneKey, 440, 420, 2, 5)
    hdtuneCol = ['C','D','A','R','Y','Z','AA','AB','AC','AD']
    WriteDataAndImageLocal(worksheet, startLine, startLine+8, dicPfm, 'HDW2', hdtuneCol, hdtuneKey, 440, 420, 2, 5)
    #ATTO
    attoKey = ['Cap','pc_no','format','64 MB_Write','64 MB_Read']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C17\\\\ATTO_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_1', attoKey, 'ATTO4_0_MBps.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C17\\\\ATTO_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_2', attoKey, 'ATTO4_0_MBps.bmp', maxDiskCnt)
    startLine = 103
    attoCol = ['C','D','A','H','E','F']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'ATTO_1', attoCol, attoKey, 340, 350, 2, 4)
    attoCol = ['C','D','A','H','I','J']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'ATTO_2', attoCol, attoKey, 340, 350, 2, 4)
    #hdbench
    hdbKey = ['Cap','pc_no','format','Read','Write','RRead','RWrite']
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C18\\\\HDBench_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan17\\\\T_GE_SD_C18\\\\HDBench_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_2', hdbKey, 'HDBench.bmp', maxDiskCnt)
    startLine = 115
    hdbCol = ['C','D','A','J','E','F','G','H']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'HDB_1', hdbCol, hdbKey)
    hdbCol = ['C','D','A','J','K','L','M','N']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'HDB_2', hdbCol, hdbKey)

#'MMS_PC','FILE_SYSTEM'
def ReadMarsIniDataLocal(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue

        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    pcNo = ''
                    if 'MMS_PC' in config['HWCONFIG']:
                        pcNo = config['HWCONFIG']['MMS_PC']                   
                    tempLst.append(pcNo)
                    continue
                if 'FILE_SYSTEM' == key:
                    fs = ''
                    if 'FILE_SYSTEM' in config['HWCONFIG']:
                        fs = config['HWCONFIG']['FILE_SYSTEM']
                    tempLst.append(fs)
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  PublicFuc.GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst
        else:
            #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
            oldTime = dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    pcNo = ''
                    if 'MMS_PC' in config['HWCONFIG']:
                        pcNo = config['HWCONFIG']['MMS_PC']                   
                    tempLst.append(pcNo)
                    continue
                if 'FILE_SYSTEM' == key:
                    fs = ''
                    if 'FILE_SYSTEM' in config['HWCONFIG']:
                        fs = config['HWCONFIG']['FILE_SYSTEM']
                    tempLst.append(fs)
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  PublicFuc.GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst

def WriteDataAndImageLocal(worksheet, startLine, imageLine, dataDic, caseKey, colLst, keyLst, imgWidth = 260, imgHeight = 240, startCol = 2, colCnt = 3):
    curLine = startLine
    imageCol = startCol
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                worksheet['%s%d'%(col, curLine)] = keyNo
            else:
                worksheet['%s%d'%(col, curLine)] = line[index-1]
        # 列表最后一项是图片路径
        if '' != line[-1]:
            img = Image(line[-1])
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        curLine += 1
        imageCol += colCnt

def WriteData(worksheet, startLine, dataDic, caseKey, colNum, lineCnt, disk=''):
    if caseKey not in dataDic:
        return
    col = get_column_letter(colNum)
    curLine = startLine
    line = dataDic[caseKey]
    if '' != disk:
        worksheet['%s3'%col] = '%s-%sG'%(disk, line[0])
    for data in line[3:3+lineCnt]:
        try:
            fData = float(data)
            worksheet['%s%d'%(col, curLine)] = float(data)
        except:
            DD ='NOTHING TO DO'
            #单元格不做填充

        curLine += 1

def ProSumData(curpath, ws):
    cleanLst = ['CDM_1', 'H2_1', 'IMT_1', 'HDR1', 'HDW1', 'ATTO_1', 'HDB_1']
    dirtyLst = ['CDM_2', 'H2_2', 'IMT_2', 'HDR2', 'HDW2', 'ATTO_2', 'HDB_2']
    lineLst = [8, 2, 4, 3, 3, 2, 4]
    startCol = 6
    keyLst = sorted(dicPfm.keys(), reverse=False)
    for disk in keyLst:
        startLine = 5
        for idx,caseKey in enumerate(cleanLst):
            WriteData(ws, startLine, dicPfm[disk], caseKey, startCol, lineLst[idx], disk)
            startLine += lineLst[idx]
        startLine = 5
        startCol += 1
        for idx,caseKey in enumerate(dirtyLst):
            WriteData(ws, startLine, dicPfm[disk], caseKey, startCol, lineLst[idx])
            startLine += lineLst[idx]
        startCol += 1
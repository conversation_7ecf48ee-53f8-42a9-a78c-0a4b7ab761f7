﻿<!DOCTYPE html>
<html>
<head>
   <title>Command line options</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Arguments,Command line,Configuration mode,Interactive mode" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "commandline.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold;"> &nbsp;Command line options</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="overview.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="forcetypes.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="autorun.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;">There are three command line options. If no command line option is specified, Rebooter runs in interactive mode.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">-Reboot</span></p>
<p style="margin: 7px 0px 7px 0px;">Perform a reboot according to the parameters in the saved configuration file. The count down to a reboot will start immediately after the program has started. All buttons are disabled (as if the user had hit the Start Cycle button). The type of reboot / restart performed will be whatever was lasted saved in the configuration file. </p>
<p style="margin: 7px 0px 7px 0px;">Note: When Rebooter is started from BurnInTest, the Rebooter setting of &quot;Auto load Rebooter at startup&quot; is not applied. This allows BurnInTest to be setup as the auto restart program, and avoids the conflict of both BurnInTest and Rebooter auto starting after a reboot. This means that only a single reboot will be performed when rebooter is run from BurnInTest. To perform multiple reboots from within BurnInTest a script should be used with multiple REBOOT commands. </p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">-Config</span></p>
<p style="margin: 7px 0px 7px 0px;">Allows the user to change and save the settings but not do a reboot. This is useful when Rebooter is integrated into another external application. The external application can call Rebooter with this option to allow the user to configure various parameters then call Rebooter once again with the –Reboot argument to effect the Reboot.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">-p</span></p>
<p style="margin: 7px 0px 7px 0px;">Forces Rebooter to use the rebooter.exe directory rather than the User's personal directory for configuration and log files etc. This may be useful when running rebooter from a USB drive. Note: If BurnInTest is started with the -p &nbsp;command line parameter then BurnInTest will start rebooter with the -p command line parameter.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-weight: bold;">-c &lt;config file&gt;</span></p>
<p style="margin: 7px 0px 7px 0px;">Load Rebooter using the config file specified.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-weight: bold;">-x</span></p>
<p style="margin: 7px 0px 7px 0px;">Auto exit Rebooter when last cycle has finished</p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Example – Execute a reboot from the command line</span></p>
<p style="margin: 7px 0px 7px 0px;">rebooter &nbsp;-reboot</p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #000000;">&nbsp;</span></p>

</td></tr></table>

</body>
</html>

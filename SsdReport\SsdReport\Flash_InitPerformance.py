import PublicFuc
from openpyxl.drawing.image import Image
import csv
import matplotlib.pyplot as plt
import tempfile,os
from openpyxl.utils import get_column_letter

def Run(curpath, workBook, alignment):
    ws = workBook['05-初始_性能测试']
    ws.alignment = alignment
    ProCdm(curpath, ws)  
    ProAssd(curpath, ws)
    PublicFuc.WriteReportTime(ws,'H',2)
    PublicFuc.WriteReportOperator(ws,'M',2)


def LocalWriteDataAndImage(worksheet, startLine, dataDic, colLst, keyLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    imageCol = 1
    for key in dataDic:       
        #最后一行数据是经过处理获取的最值
        limitLine = dataDic[key][-1]
        for line in dataDic[key][:-1]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    try:
                        floatValue = float(line[index-1])
                        #和最值相差20%需要填充红色警告
                        if keyLst[index-1] not in minKey:
                            if floatValue <= limitLine[index-1]*0.8:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                        else:
                            if floatValue >= limitLine[index-1]*1.2:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    except:
                        continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+len(dataDic[key])-1
        #imageLine += 1

def ProCdm(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'P','G','H','I','J','K','L','M','N']
    cdmDic = {}
    pattern = '.+\\\\Plan503\\\\T-SS-SS-E03\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 12
    imgWidth = 250
    imgHeight = 240
    LocalWriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAssd(curpath, worksheet):   
    assdKey = ['pc_no', 'Cap', 'qa_err_msg','Seq Read','Seq Write','4K Read','4K Write','4K 64Thrd Read','4K 64Thrd Write','Read Acc Time','Write Acc Time','Read Score','Write Score','Total Score']
    assdCol = ['C','B','E','S','G','H','I','J','K','L','M','N','O','P','Q']
    assdDic = {}
    pattern = '.+\\\\Plan503\\\\T-SS-SS-E04\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 18
    imgWidth = 250
    imgHeight = 240
    LocalWriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)

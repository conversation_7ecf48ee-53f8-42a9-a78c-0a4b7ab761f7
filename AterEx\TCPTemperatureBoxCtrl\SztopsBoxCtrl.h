#pragma  once
#include "TCPTempBoxBase.h"
#include "../../Include/public.h"
#include <Windows.h>
#include <string>

class CSztopsBoxCtrl : public TCPTempBoxBase
{
public:
	CSztopsBoxCtrl();

	~CSztopsBoxCtrl();

	BOOL InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs = 5000);
	virtual void CloseConnection() override;
	virtual bool IsReady() override;

	//设置目标温度,单位：0.1度
	virtual void SetTargetTemperature(int _nVal) override;

	//获取目标温度,单位：0.1度
	virtual int GetTargetTemperature() override;

	//获取温度，单位：0.1度
	virtual int GetTemperature() override;

	virtual bool IsRunning() override;//是否正在运行

	virtual bool IsAlarm() override;//是否报警

	virtual bool Run() override;

	virtual void Stop() override;

	//设置升降温时间，单位秒
	virtual void SetTemperatureChangeTime(int _nVal) override;

	//获取升降温时间，单位秒
	virtual int GetTemperatureChangeTime() override;

	//设置通讯组号,0-20
	virtual void SetTS1(int _nVal) override;

	virtual int GetTS1() override;

	//设置运行模式0-定值，1-程式
	virtual void SetMode(int _nVal = 0) override;

	virtual int GetMode() override;

	virtual DWORD GetErrCode() override;

private:
	// 移除了CRC相关方法和串口相关成员变量
};

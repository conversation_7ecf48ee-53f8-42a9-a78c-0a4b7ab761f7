import PublicFuc,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

dicData = {}

def Run(curpath, workBook, alignment):
    ws = workBook['Full Test Reports']
    ws.alignment = alignment
    ProDataMPFormatSummary(ws,workBook)
    ProDataFunctionH2Summary(ws,workBook)
    ProDataFunctionCopyFileSummary(ws,workBook)
    ProDataSporSummary(ws,workBook)
    ProDataPorSummary(ws,workBook)
    ProBurinRTSummary(ws,workBook)
    ProBurinHTSummary(ws,workBook)
    ProBurinLTSummary(ws,workBook)
    ProEmptyChunkSummary(ws,workBook)
    ProEnvironmentDataRentionSummary(ws,workBook)
    ProEnvironmentDataRention4647Summary(ws,workBook)
    ProEnvironmentReadDisturbSummary(ws,workBook)
    ProDataCoverageSummary(ws,workBook)
    ProDataPfmMarsH2Summary(ws,workBook)
    ProDataPfmThirdpartH2Summary(ws,workBook)
    ProDataPfmCDMSummary(ws,workBook)
    ProDataPfmHDTuneSummary(ws,workBook)
    ProDataPfmATTOSummary(ws,workBook)
    ProDataPfmHDBenchSummary(ws,workBook)
    ProDataPfmIometerSummary(ws,workBook)
    ProMPFunctionErrSummary(workBook)

#填写MP_FUNCTION中的错误样片编号信息为统计信息
def ProMPFunctionErrSummary(workBook):
    wsSummary = workBook['Full Test Reports']
    wsMPFunction = workBook['MP_Function']
    wsMPFunction['Z4'] = wsSummary['L16'].value
    wsMPFunction['Z6'] = wsSummary['L17'].value
    wsMPFunction['Z9'] = wsSummary['L18'].value

def ProDataPfmMarsH2Summary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 19
    MIN_LINE = 37
    MAX_LINE = 41
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmThirdpartH2Summary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 19
    MIN_LINE = 62
    MAX_LINE = 66
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    wsTmp_simple = workBook['Performance-Simple']
    MIN_LINE = 33
    MAX_LINE = 37
    totalSampleCnt_simple = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp_simple)

    totalSampleCnt += totalSampleCnt_simple

    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmCDMSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 20
    MIN_LINE = 50
    MAX_LINE = 54
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    wsTmp_simple = workBook['Performance-Simple']
    MIN_LINE = 21
    MAX_LINE = 25
    totalSampleCnt_simple = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp_simple)

    totalSampleCnt += totalSampleCnt_simple
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmHDTuneSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 22
    MIN_LINE = 89
    MAX_LINE = 93
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmATTOSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 23
    MIN_LINE = 103
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmHDBenchSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 24
    MIN_LINE = 115
    MAX_LINE = 119
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmIometerSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 25
    MIN_LINE = 75
    MAX_LINE = 79
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataMPFormatSummary(ws,workBook):
    wsTmp = workBook['MP_Function']
    MIN_LINE = 16
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','C']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    ws['H16'] = totalSampleCnt
    ws['J16'] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['0XFA0']
    totalFailCnt,failInfo = GetFailInfoByRex(dataList,'\d+M')
    ws['I16'] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K16'] = 'Pass'
        return

    ws['K16'] = 'Fail'
    ws['L16'] = failInfo

def ProDataFunctionH2Summary(ws,workBook):
    wsTmp = workBook['MP_Function']
    targetLineNo = 17
    MIN_LINE = 16
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'K',wsTmp)
    colList = ['B','K']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    errTypeDic = {'读失败':['读失败'],'写失败':['写失败'],'校验失败':['verify','KB','MB','GB']}
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList,errTypeDic)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataFunctionCopyFileSummary(ws,workBook):
    wsTmp = workBook['MP_Function']
    targetLineNo = 18
    MIN_LINE = 16
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'L',wsTmp)
    colList = ['B','L']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataSporSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 28
    MIN_LINE = 43
    MAX_LINE = 74
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','Q','U','Y']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0 and failInfo != '':
        ws['L%d'%targetLineNo] = failInfo #测试中
        return
    if totalFailCnt <= 0 and failInfo == '':
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataPorSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 27
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','Q','U','Y']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0 and failInfo != '':
        ws['L%d'%targetLineNo] = failInfo #测试中
        return
    if totalFailCnt <= 0 and failInfo == '':
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#返回失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果
def GetFailInfoOldVersion(dataList,validResultList):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo

#返回失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果
#errRuleDic内容示例,errTypeDic = {'写失败':['写失败'],'读失败':['读失败'],'校验失败':['VERIFY','KB','GB','MB']}
def GetConlusionErrTypeName(errTypeDic,errMsg):
    UpCaseErrMsg = errMsg.upper()
    errTypeName = errMsg
    for errType in errTypeDic:
        for str in errTypeDic[errType]:
            if UpCaseErrMsg.find(str.upper()) != -1:
                errTypeName = errType #找到，代表属于此类错误
                return errTypeName
    return errTypeName

def GetFailInfoString(errDic):
    failInfo = ''
    for errType in errDic:
        failInfo += '[' + errType + ']' + ':' + str(errDic[errType]) + ','
    if failInfo != '':
        failInfo = failInfo[:-1]

    return failInfo

def GetFailInfo(dataList,validResultList,errTypeDic = {}):
    failCnt = 0
    failInfo = ''
    errDic = {} #按照错误信息分类的
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == 'UNFINISHED':
                strResult = '测试中'
                errTypeName = GetConlusionErrTypeName(errTypeDic,strResult)
                if errTypeName in errDic:
                    errDic[errTypeName] = errDic[errTypeName] + 1
                else:
                    errDic[errTypeName] = 1
                break #只记录最先遇到的错误
            elif strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                errTypeName = GetConlusionErrTypeName(errTypeDic,strResult)
                if errTypeName in errDic:
                    errDic[errTypeName] = errDic[errTypeName] + 1
                else:
                    errDic[errTypeName] = 1
                break #只记录最先遇到的错误
    failInfo = GetFailInfoString(errDic)
    return failCnt,failInfo

#返回量产失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果（因为量产结果列要么填的是容量要么填的是错误码，比较特殊。单独写函数处理）
def GetFailInfoByRexOldVersion(dataList,rexExpression):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (not re.match(rexExpression,strResult)):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo

#返回量产失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果（因为量产结果列要么填的是容量要么填的是错误码，比较特殊。单独写函数处理）
def GetFailInfoByRex(dataList,rexExpression,errTypeDic = {}):
    failCnt = 0
    failInfo = ''
    errDic = {}
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (not re.match(rexExpression,strResult)):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                errTypeName = GetConlusionErrTypeName(errTypeDic,strResult)
                if errTypeName in errDic:
                    errDic[errTypeName] = errDic[errTypeName] + 1
                else:
                    errDic[errTypeName] = 1
              
                break #只记录最先遇到的错误
    failInfo = GetFailInfoString(errDic)
    return failCnt,failInfo

def ProDataCoverageSummary(ws,workBook):
    wsTmp = workBook['Data coverage']

    targetLineNo = 41
    MIN_LINE = 5
    MAX_LINE = 12
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentDataRentionSummary(ws,workBook):
    wsTmp = workBook['Environment Test']
    targetLineNo = 36
    MIN_LINE = 7
    MAX_LINE = 46
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','W']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentDataRention4647Summary(ws,workBook):
    wsTmp = workBook['Environment Test']
    #100°的retention结果
    targetLineNo = 37
    MIN_LINE = 51
    MAX_LINE = 90
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','W']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentReadDisturbSummary(ws,workBook):
    wsTmp = workBook['Environment Test']

    targetLineNo = 38
    MIN_LINE = 95
    MAX_LINE = 110
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo


def ProEmptyChunkSummary(ws,workBook):
    wsTmp = workBook['Empty chunk']

    targetLineNo = 34
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H','J','O']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def GetSpecialDataCnt(_startRow,_endRow,_colName,ws):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt



#BIT测试数据汇总包括：RT
def ProBurinRTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取RT BIT汇总数据
    targetLineNo = 29
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','L','P','S','V','AI']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0 and failInfo != '':
        ws['L%d'%targetLineNo] = failInfo #测试中
        return
    if totalFailCnt <= 0 and failInfo == '':
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#BIT测试数据汇总包括：HT
def ProBurinHTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取HT BIT汇总数据
    targetLineNo = 30
    MIN_LINE = 43
    MAX_LINE = 58
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I']#,'AF' 未填写，暂时不适用
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#BIT测试数据汇总包括：LT
def ProBurinLTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取HT BIT汇总数据
    targetLineNo = 31
    MIN_LINE = 63
    MAX_LINE = 78
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I'] #,'AF' 未填写，暂时不适用
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo


#获取指定范围的数据内容，结果数据是列表形式
def GetSpecialDataList(_startRow,_endRow,_colName,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            dic.append(celValue)
    return dic


#获取最长测试时间
def GetMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        timestr = data
        if timestr != '' and timestr != None:
            if timestr.find(':') != -1:
                #00:00:00
                timedata = timestr.split(':')
                totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond
            else:
                #000h 03m 07s
                timedata = timestr.split(' ')
                hour = timedata[0][0:-1]
                minutes = timedata[1][0:-1]
                seconds = timedata[2][0:-1]
                totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow,_endRow,_colNameList,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        oneRow = []
        for _colName in _colNameList:
            celPosSample = '%s%d'%(_colName, rowNo)
            celValue = ws[celPosSample].value
            oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic

#判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False
    
    isValid = False
    for idx in range(1,len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid
  
#获取最长测试时间
def GetCombinedMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        if len(data) == 1:
            #简单时间信息
            timestr = data
            if timestr != '' and timestr != None:
                if timestr.find(':') != -1:
                    #00:00:00
                    timedata = timestr.split(':')
                    totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
                else:
                    #000h 03m 07s
                    timedata = timestr.split(' ')
                    hour = timedata[0][0:-1]
                    minutes = timedata[1][0:-1]
                    seconds = timedata[2][0:-1]
                    totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
        else:
            oneRowTimeList = data
            perRowTotalTime = 0
            for perTime in oneRowTimeList:
                if perTime != '' and perTime != None:
                    timestr = perTime
                    if timestr.find(':') != -1:
                        #00:00:00
                        timedata = timestr.split(':')
                        totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                        perRowTotalTime += totalSecond
                    else:
                        #000h 03m 07s
                        timedata = timestr.split(' ')
                        hour = timedata[0][0:-1]
                        minutes = timedata[1][0:-1]
                        seconds = timedata[2][0:-1]
                        totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                        perRowTotalTime += totalSecond
            
            if perRowTotalTime > maxTimeInSeconds:
                            maxTimeInSeconds = perRowTotalTime


    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#从字符中直接解析出时间的值
def GetTimeValue(dic):
    timeValue = 0
    endTimeStr = dic[2]
    startTimeStr = dic[1]
    if '' == endTimeStr or '' == startTimeStr:
        timeValue = 0
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            timeValue = totalSecond
        except:
            timeValue = 0
    return timeValue
         

#获取开始和结束的时间
def GetTotalTimeStr():
    #测试时间
    strTime = ''
    totalTimeValue = 0
    for key in dicData:
        childDic = dicData[key]
        if 'MPTOOL' not in childDic:
            continue
        dic = childDic['MPTOOL']
        totalTimeValue += GetTimeValue(dic)

    totalSecond = int(totalTimeValue)
    hour = int(totalSecond/3600)
    lefSeconds = totalSecond%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
        
    return strTime

   


def DrawTable(ws):
    totalRowCnt = len(dicData)
    STARTLINE = 13
    #serialNo = 1
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        for ColNo in range(1,1+5):
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
    
    #合并单元格
    if totalRowCnt > 0:
        ws['%s%d'%(get_column_letter(1), STARTLINE)] = '48H高格'
        ws.merge_cells(start_row=STARTLINE, start_column=1, end_row=STARTLINE+totalRowCnt-1, end_column=1)

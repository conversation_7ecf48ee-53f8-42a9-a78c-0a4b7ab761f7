
import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import ErrDiskInfo

dicPfm = {} #存放性能相关数据
dicPcCopyfile ={} #存放文件拷贝相关数据
dicPcCopyfileIdxByPcNo = {} #存放按照电脑索引的数据，由dicPcCopyfile筛选而来。
dicBurnIn ={} #存放burnin相关数据
dicBurnInIdxByPcNo = {} #存放按照电脑索引的数据，由dicBurnIn筛选而来。
g_listMPColumnName = ['Flash编号','LogCap','MP_Result','MP_Time','MPStatus','FlashID','测试PC编号','PCVersion','FWVersion','FlashName','MCUType']
g_invalid_pos = -1
mp_column_name_map = {} #量产工具低格高格测试结果的列映射关系

global g_totalValidCopyFileDataCnt
g_totalValidCopyFileDataCnt = 0

PC_COPY_DATA_START_LINE = 19
PFM_DATA_START_LINE = 6

BURNIN_DATA_START_LINE = 17
global g_totalValidBurInDataCnt
g_totalValidBurInDataCnt = 0

FW_VERSION = '' #固件版本
MP_VERSION = '' #量产工具版本
FLASH_ID = ''  #flash的ID
FLASH_NAME = ''  #flash的型号
FLASH_MCU = '' #主控版本号
TEST_TIME = '' #测试时间

def Run(curpath, workBook, alignment):
    ws = workBook['性能测试']
    ws.alignment = alignment

    InitMPCsvColumnNameMapAutomatic(curpath)
    ProPfm(curpath, ws)

    ws_conclusion = workBook['PC端文件拷贝']
    ws_conclusion.alignment = alignment
    ProPcCopy(curpath, ws_conclusion)

    ws_burnin = workBook['常温老化']
    ws_burnin.alignment = alignment
    ProBurnIn(curpath, ws_burnin)

def ProBurnIn(curpath, worksheet):
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C21\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    KeySet = ['Cap','pc_no','Duration','BitCycle','qa_err_msg']
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C21\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    #plan9
    pattern = '.+\\\\Plan9\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')
    pattern = '.+\\\\Plan19\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    pattern = '.+\\\\Plan9\\\\T_GE_U2_C21\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)
    pattern = '.+\\\\Plan19\\\\T_GE_U2_C21\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    InitDicBurInDataByPcNo()
    startLine = BURNIN_DATA_START_LINE
    WriteBurnInDic(worksheet, startLine, dicBurnInIdxByPcNo)
    worksheet['%s%d'%('B', 9)] = FLASH_MCU
    worksheet['%s%d'%('D', 9)] = MP_VERSION
    worksheet['%s%d'%('D', 11)] = PublicFuc.GetDate()
    PublicFuc.WriteReportTime(worksheet,'N',1)
    PublicFuc.WriteReportOperator(worksheet,'P',1)

#得到按照PCno索引的字典
def InitDicBurInDataByPcNo():
    global g_totalValidBurInDataCnt
    for key in dicBurnIn:
        subDic = {}
        subDic[key] = dicBurnIn[key]
        if 'HighFormat' in dicBurnIn[key]:
            row = subDic[key]['HighFormat']
            pcNo = row[2]
            if pcNo not in dicBurnInIdxByPcNo:
                dicBurnInIdxByPcNo[pcNo] = []          
            dicBurnInIdxByPcNo[pcNo].append(subDic)
            g_totalValidBurInDataCnt += 1


def WriteBurnInDic(worksheet, startLine, dataDic):
    InitBurnInReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    serialNo = 1
    failcnt = 0
    for pcNo in dicBurnInIdxByPcNo:
        listRows = dicBurnInIdxByPcNo[pcNo]
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    worksheet['%s%d'%('A', curLine)] = serialNo #填写flash编号
                    cap = rowFormat[0]
                    mode = rowFormat[1]
                    worksheet['%s%d'%('B', curLine)] = key
                    worksheet['%s%d'%('C', curLine)] = cap
                    worksheet['%s%d'%('D', curLine)] = mode
                    #PCNO已经在构造表格的时候写入，此处不必写入

                    if 'BurnIn' in dicSample[key]:
                        rowBurIn = dicSample[key]['BurnIn']
                        costTime = rowBurIn[2]
                        runcircle = rowBurIn[3]
                        errMsg = rowBurIn[4]
                        worksheet['%s%d'%('G', curLine)] =  costTime
                        worksheet['%s%d'%('H', curLine)] = runcircle
                        bPass = True
                        if errMsg != '':
                            bPass = False

                        if bPass == True:
                            worksheet['%s%d'%('I', curLine)] = 'PASS'
                            worksheet['%s%d'%('K', curLine)] = 'PASS'
                        else:
                            worksheet['%s%d'%('I', curLine)] = errMsg
                            worksheet['%s%d'%('K', curLine)] = 'FAIL'
                            failcnt += 1
                            worksheet['%s%d'%('I', curLine)].fill = PublicFuc.warnFill
                            worksheet['%s%d'%('K', curLine)].fill = PublicFuc.warnFill
                            tmpItemKey = ErrDiskInfo.GetCombinedKeyName('BurnIn',ErrDiskInfo.g_filepathKey)
                            file = dicSample[key][tmpItemKey]
                            PublicFuc.AppendErrDiskInfo('常温老化测试_Err',key,errMsg,pcNo,file)

                    else:
                        worksheet['%s%d'%('I', curLine)] = 'FAIL'
                        worksheet['%s%d'%('K', curLine)] = 'FAIL'
                        worksheet['%s%d'%('I', curLine)].fill = PublicFuc.warnFill
                        worksheet['%s%d'%('K', curLine)].fill = PublicFuc.warnFill
                        failcnt += 1
                    
                    curLine += 1
                    serialNo += 1
                    
    if g_totalValidBurInDataCnt > 0:
        passRatio = float(g_totalValidBurInDataCnt-failcnt)*100/float(g_totalValidBurInDataCnt)
        strDesc = 'BurnIn 24h测试：测试%dpcs,失败%dpcs,良率%.2f%%。'%(g_totalValidBurInDataCnt, failcnt,passRatio)
        worksheet['%s%d'%('A', 13)] = strDesc
    worksheet.merge_cells(start_row=13, start_column=1, end_row=13, end_column=9) 
    
#绘制BurnIn表格。
def InitBurnInReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(g_totalValidBurInDataCnt):
        for col in range(12):
            worksheet['%s%d'%(get_column_letter(col+1), BURNIN_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            bottomLine = 'thin'
            rightLine = 'thin'
            worksheet['%s%d'%(get_column_letter(col+1), BURNIN_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
            worksheet['%s%d'%(get_column_letter(col+1), BURNIN_DATA_START_LINE+rowIdx)].font = cellfont
   
    #合并单元格
    mergeStartLine = BURNIN_DATA_START_LINE
    mergeStartCol = 5
    curLine = mergeStartLine
    for pcNo in dicBurnInIdxByPcNo:
        listRows = dicBurnInIdxByPcNo[pcNo]
        rowCnt = len(listRows)
        bWritePcNo = False
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    if bWritePcNo == False:
                        worksheet['%s%d'%('E', curLine)] = pcNo
                        if 'windows_version' in dicSample[key]:
                            worksheet['%s%d'%('F', curLine)] = dicSample[key]['windows_version']
                        bWritePcNo = True #确保只写一次，方便合并单元格。
                    curLine += 1

        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol)
        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol+1, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol+1)
        mergeStartLine += rowCnt

def ProPcCopy(curpath, worksheet):
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C15\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPcCopyfile,'HighFormat')#copyfile前面的格式化
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C15\\\\copyfile\\\\DUTTest.+.csv$'
    ReadCopyFileCsvData(curpath,pattern,dicPcCopyfile,'copyfile')
    #plan8
    pattern = '.+\\\\Plan8\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPcCopyfile,'HighFormat')#copyfile前面的格式化
    pattern = '.+\\\\Plan8\\\\T_GE_U2_C15\\\\copyfile\\\\DUTTest.+.csv$'
    ReadCopyFileCsvData(curpath,pattern,dicPcCopyfile,'copyfile')
    pattern = '.+\\\\Plan20\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPcCopyfile,'HighFormat')#copyfile前面的格式化
    pattern = '.+\\\\Plan20\\\\T_GE_U2_C15\\\\copyfile\\\\DUTTest.+.csv$'
    ReadCopyFileCsvData(curpath,pattern,dicPcCopyfile,'copyfile')
    InitDicDataByPcNo()
    startLine = PC_COPY_DATA_START_LINE
    WriteCopyFileDic(worksheet, startLine, dicPcCopyfileIdxByPcNo)
    worksheet['%s%d'%('B', 9)] = FLASH_MCU
    worksheet['%s%d'%('B', 10)] = MP_VERSION
    worksheet['%s%d'%('E', 12)] = PublicFuc.GetDate()
    #worksheet['%s%d'%('B', 12)] = len(dicPcCopyfile)
    PublicFuc.WriteReportTime(worksheet,'N',1)
    PublicFuc.WriteReportOperator(worksheet,'P',1)

    
    
#得到按照PCno索引的编号
def InitDicDataByPcNo():
    global g_totalValidCopyFileDataCnt
    for key in dicPcCopyfile:
        subDic = {}
        subDic[key] = dicPcCopyfile[key]
        if 'HighFormat' in dicPcCopyfile[key]:
            row = subDic[key]['HighFormat']
            pcNo = row[2]
            if pcNo not in dicPcCopyfileIdxByPcNo:
                dicPcCopyfileIdxByPcNo[pcNo] = []          
            dicPcCopyfileIdxByPcNo[pcNo].append(subDic)
            g_totalValidCopyFileDataCnt += 1

def ProPfm(curpath, worksheet):
    #高格
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C4\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    #高格-plan7
    pattern = '.+\\\\Plan7\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan18\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan12\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan13\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan14\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    #h2testw
    h2Key = ['write speed','read speed','qa_err_msg']
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C4\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    #h2testw-plan7
    pattern = '.+\\\\Plan7\\\\T_GE_U2_C4\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U2_C4\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan12\\\\T_GE_U2_C26\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
   
    #hdbench
    hdbKey = ['Read','Write','RRead','RWrite']
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C4\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    #hdbench-plan7
    pattern = '.+\\\\Plan7\\\\T_GE_U2_C4\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U2_C4\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan14\\\\T_GE_U2_C28\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    
    #cdm
    cdmKey = ['SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C4\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    #cdm-plan7
    pattern = '.+\\\\Plan7\\\\T_GE_U2_C4\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U2_C4\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan13\\\\T_GE_U2_C27\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)

    #写数据
    startLine = PFM_DATA_START_LINE  
    WritePfmDic(worksheet,startLine,dicPfm)
    PublicFuc.WriteReportTime(worksheet,'V',1)
    PublicFuc.WriteReportOperator(worksheet,'Y',1)

def ReadFormatCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[mp_column_name_map['Flash编号']]
                cap = row[mp_column_name_map['LogCap']]
                mode = row[mp_column_name_map['MPStatus']]
                pcNo = row[mp_column_name_map['测试PC编号']]
                tempRow = [cap,mode,pcNo]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                    fileMdTime = os.path.getmtime(file)
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
                else:
                    #查看是否是最新数据做覆盖处理。
                    oldTime = dataDic[key][PublicFuc.GetTimeKeyName(caseKey)]
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global FLASH_ID
                global FLASH_NAME
                global FLASH_MCU
                if FW_VERSION == '':
                    if 'FWVersion' in mp_column_name_map and mp_column_name_map['FWVersion'] >=0:
                        FW_VERSION = row[mp_column_name_map['FWVersion']]
                if MP_VERSION == '':
                    if 'PCVersion' in mp_column_name_map and mp_column_name_map['PCVersion'] >=0:
                        MP_VERSION = row[mp_column_name_map['PCVersion']]
                if FLASH_ID == '':
                    if 'FlashID' in mp_column_name_map and mp_column_name_map['FlashID'] >=0:
                        FLASH_ID = row[mp_column_name_map['FlashID']]
                if FLASH_NAME == '':
                    if 'FlashName' in mp_column_name_map and mp_column_name_map['FlashName'] >=0:
                        FLASH_NAME = row[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    if 'MCUType' in mp_column_name_map and mp_column_name_map['MCUType'] >=0:
                        FLASH_MCU = row[mp_column_name_map['MCUType']]
                #end


def ReadCopyFileCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[3]
                result = row[10]
                pcNo = row[5]
                tempRow = [result,pcNo]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                else:
                    #判定是否有更新数据进行覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖
                    dataDic[key][caseKey] = tempRow

                if 'windows_version' not in dataDic[key] or dataDic[key]['windows_version'] == '':
                    dataDic[key]['windows_version'] = PublicFuc.GetCommonInfoByFilePath(file,'WINDOWS_VERSION')
                dataDic[key][caseKey+'_file_path'] = file
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                dataDic[key][tmpItemKey] = pcNo
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                dataDic[key][tmpItemKey] = file

#绘制表格。
def InitPfmReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(dicPfm)):
        for col in range(20):
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].font = cellfont

    #填充表格标题
    strTitle = ''
    global TEST_TIME
    TEST_TIME = PublicFuc.GetDate()
    strTitle += FLASH_MCU+'工具设置：'+ MP_VERSION + '\n' + '（测试日期：' + TEST_TIME + '）'
    worksheet['%s%d'%('B', 2)] = strTitle

#绘制表格。
def InitPcCopyFileReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(g_totalValidCopyFileDataCnt):
        for col in range(9):
            worksheet['%s%d'%(get_column_letter(col+1), PC_COPY_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            bottomLine = 'thin'
            rightLine = 'thin'
            worksheet['%s%d'%(get_column_letter(col+1), PC_COPY_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
            worksheet['%s%d'%(get_column_letter(col+1), PC_COPY_DATA_START_LINE+rowIdx)].font = cellfont
   
    #合并单元格
    mergeStartLine = PC_COPY_DATA_START_LINE
    mergeStartCol = 5
    curLine = mergeStartLine
    for pcNo in dicPcCopyfileIdxByPcNo:
        listRows = dicPcCopyfileIdxByPcNo[pcNo]
        rowCnt = len(listRows)
        bWritePcNo = False
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    if bWritePcNo == False:
                        worksheet['%s%d'%('E', curLine)] = pcNo
                        bWritePcNo = True #确保只写一次，方便合并单元格。
                        if 'windows_version' in dicSample[key]:
                            worksheet['%s%d'%('F', curLine)] = dicSample[key]['windows_version']
                    curLine += 1

        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol)
        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol+1, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol+1)
        mergeStartLine += rowCnt




def WritePfmDic(worksheet, startLine, dataDic,imgWidth = 360, imgHeight = 300):
    InitPfmReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    imageStartLine = startLine + len(dataDic)+2
    imageLine = imageStartLine
    imageCol = 1
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for idx,key in enumerate(keySortLst):
        imageCol = 1+(idx%2)*16 
        imageLine = imageStartLine + (idx//2)*18

        worksheet['%s%d'%('A', curLine)] = key #填写flash编号
        if 'windows_version' in dataDic[key]:
            worksheet['%s%d'%('E', curLine)] = dataDic[key]['windows_version']

        caseKey = 'HighFormat'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['B','C','D']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'H2_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['F','G','H']
            for index,col in enumerate(colLst):
                if col == 'H':
                    if line[index] == '':
                        worksheet['%s%d'%(col, curLine)] = 'TRUE'
                    else:
                        worksheet['%s%d'%(col, curLine)] = 'FALSE'
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_1',ErrDiskInfo.g_pcnoKey)
                        pcNo = dataDic[key][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_1',ErrDiskInfo.g_filepathKey)
                        file = dataDic[key][tmpItemKey]
                        errcode = line[index]
                        PublicFuc.AppendErrDiskInfo('性能测试_Err',key,errcode,pcNo,file)
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDB_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['I','J','K','L']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'CDM_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['M','N','O','P','Q','R','S','T']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        #写图片
        worksheet['%s%d'%(get_column_letter(imageCol), imageLine)] = key #填写flash编号
        imageLine += 1
        if 'H2_1' in dataDic[key]:
            line = dataDic[key]['H2_1']
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                if imageCol == 1:
                    img.width = imgWidth-20
                    img.height = imgHeight
                    worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                    imageCol += 4
                else:
                    img.width = imgWidth
                    img.height = imgHeight
                    worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                    imageCol += 5
        
        if 'HDB_1' in dataDic[key]:
            line = dataDic[key]['HDB_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                imageCol += 5

        if 'CDM_1' in dataDic[key]:
            line = dataDic[key]['CDM_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))

        curLine += 1


def WriteCopyFileDic(worksheet, startLine, dataDic):
    InitPcCopyFileReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    serialNo = 1
    failcnt = 0
    for pcNo in dicPcCopyfileIdxByPcNo:
        listRows = dicPcCopyfileIdxByPcNo[pcNo]
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    worksheet['%s%d'%('A', curLine)] = serialNo #填写flash编号
                    cap = rowFormat[0]
                    mode = rowFormat[1]
                    worksheet['%s%d'%('B', curLine)] = key
                    worksheet['%s%d'%('C', curLine)] = cap
                    worksheet['%s%d'%('D', curLine)] = mode
                    #PCNO已经在构造表格的时候写入，此处不必写入

                    if 'copyfile' in dicSample[key]:
                        rowCopyFile = dicSample[key]['copyfile']
                        bPass = rowCopyFile[0].upper()
                        worksheet['%s%d'%('G', curLine)] = bPass
                        worksheet['%s%d'%('H', curLine)] = bPass
                        if bPass != 'PASS':
                            failcnt += 1
                            worksheet['%s%d'%('G', curLine)].fill = PublicFuc.warnFill
                            worksheet['%s%d'%('H', curLine)].fill = PublicFuc.warnFill
                            tmpItemKey = ErrDiskInfo.GetCombinedKeyName('copyfile',ErrDiskInfo.g_filepathKey)
                            file = dicSample[key][tmpItemKey]
                            errcode = bPass
                            PublicFuc.AppendErrDiskInfo('PC端文件拷贝比对测试_Err',key,errcode,pcNo,file)
                    else:
                        worksheet['%s%d'%('G', curLine)] = 'FAIL'
                        worksheet['%s%d'%('H', curLine)] = 'FAIL'
                        worksheet['%s%d'%('G', curLine)].fill = PublicFuc.warnFill
                        worksheet['%s%d'%('H', curLine)].fill = PublicFuc.warnFill
                        failcnt += 1
                    
                    curLine += 1
                    serialNo += 1
                    
    if g_totalValidCopyFileDataCnt > 0:
        passRatio = float(g_totalValidCopyFileDataCnt-failcnt)*100/float(g_totalValidCopyFileDataCnt)
        strDesc = '文件拷贝比对测试：测试%dpcs,失败%dpcs,良率%.2f%%。'%(g_totalValidCopyFileDataCnt, failcnt,passRatio)
        worksheet['%s%d'%('A', 14)] = strDesc
    worksheet.merge_cells(start_row=14, start_column=1, end_row=14, end_column=8)                   


def ParserMPColumnName(line):
    PublicFuc.RemoveSpace(line)
    for i in g_listMPColumnName:
        pos = g_invalid_pos
        try:
            pos = line.index(i)
        except:
            pos = g_invalid_pos
        if pos == g_invalid_pos:
            return False #只要有一个名称没有找到则直接返回false
        mp_column_name_map[i] = pos
    return True

#读取文件去文件中寻找位置信息
def FindColumnPosInfo(curpath,pattern):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题

            #解析量产工具各列位置
            if ParserMPColumnName(birth_header):
                return True

    return False


def InitMPCsvColumnNameMapAutomatic(curpath):
    #自动化获取量产工具的列信息
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C4\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:  #解析到列信息就直接返回，没解析到就继续用默认内容
        return
    pattern = '.+\\\\Plan7\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan18\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C15\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan8\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan20\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan2\\\\T_GE_U2_C21\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan9\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan19\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan10\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan11\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan12\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan13\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan14\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return
    pattern = '.+\\\\Plan17\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    if FindColumnPosInfo(curpath,pattern)== True:
        return

    if len(mp_column_name_map) == 0:
        InitMPCsvColumnNameMap(mp_column_name_map)

def InitMPCsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 23
    columnNameMap['LogCap'] = 4  #开卡容量
    columnNameMap['MP_Result'] = 5 #通过Pass 否则错误码
    columnNameMap['MP_Time'] = 7 #量产时间
    columnNameMap['MPStatus'] = 13 #模式P-NS,P-HS等
    columnNameMap['FlashID'] = 16
    columnNameMap['测试PC编号'] = 20
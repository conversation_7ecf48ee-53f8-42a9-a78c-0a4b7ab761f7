import PublicFuc
from openpyxl.utils import get_column_letter
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta

dicPlan23 = {}

h2_column_name_map = {} #DUT的h2测试结果列映射关系

def Run(curpath, workBook, alignment):
    ws = workBook['BurnIn']
    ws.alignment = alignment
    ProData(curpath, ws)
    FillData(ws)
    PublicFuc.WriteReportTime(ws,'D',2)
    PublicFuc.WriteReportOperator(ws,'H',2)

def ProData(curpath, worksheet):
    
    pattern = '.+\\\\Plan23\\\\T_GE_SD_C7\\\\H2_1\\\\.+.csv$'
    rawH2List_1 = [] #第1次H2原始数据
    ReadRawCsvData(curpath,pattern,rawH2List_1)
    InitH2CsvColumnNameMap(h2_column_name_map)
    InitH2RawDataMap(dicPlan23,rawH2List_1,'H2_1')

    pattern = '.+\\\\Plan23\\\\T_GE_SD_C7\\\\H2_2\\\\.+.csv$'
    rawH2List_2 = [] #第2次H2原始数据
    ReadRawCsvData(curpath,pattern,rawH2List_2)
    InitH2RawDataMap(dicPlan23,rawH2List_2,'H2_2')

    pattern = '.+\\\\Plan23\\\\T_GE_SD_C7\\\\H2_3\\\\.+.csv$'
    rawH2List_3 = [] #第3次H2原始数据
    ReadRawCsvData(curpath,pattern,rawH2List_3)
    InitH2RawDataMap(dicPlan23,rawH2List_3,'H2_3')

    pattern = '.+\\\\Plan23\\\\T_GE_SD_C7\\\\H2_4\\\\.+.csv$'
    rawH2List_4 = [] #第4次H2原始数据
    ReadRawCsvData(curpath,pattern,rawH2List_4)
    InitH2RawDataMap(dicPlan23,rawH2List_4,'H2_4')

    #读取COPYFILE测试结果
    pattern = '.+\\\\Plan23\\\\T_GE_SD_C8\\\\Mars_文件拷贝\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPlan22, 'AT_H2', 'MH2_1', marsH2Key, 'Mars.bmp',0)
    marsCopyKey = ['Cap','MMS_PC','START_TIME','END_TIME','TEST_RESULT']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan23, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)

    #读取bit测试结果
    pattern = '.+\\\\Plan23\\\\T_GE_SD_C22\\\\BIT\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPlan22, 'AT_H2', 'MH2_1', marsH2Key, 'Mars.bmp',0)
    marsBitKey = ['Cap','MMS_PC','START_TIME','END_TIME','TESET_CIRCLE','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan23, 'BurnIn', 'MARS_BURNIN', marsBitKey, '',0)


def FillData(worksheet):
    #DrawTable(worksheet)
    startLine = 7
    h2colLst1 = ['B','C','D','E','F','G']
    WriteDataDUTH2(worksheet, startLine,dicPlan23,'H2_1', h2colLst1)

    h2colLst2 = ['B','C','D','H','I','J']
    WriteDataDUTH2(worksheet, startLine,dicPlan23,'H2_2', h2colLst2)

    h2colLst3 = ['B','C','D','Q','R','S']
    WriteDataDUTH2(worksheet, startLine,dicPlan23,'H2_3', h2colLst3)

    h2colLst4 = ['B','C','D','T','U','V']
    WriteDataDUTH2(worksheet, startLine,dicPlan23,'H2_4', h2colLst4)

    copyfilecolLst = ['B','C','D','K','L']
    WriteDataMarsCopyFile(worksheet, startLine,dicPlan23,'MARS_COPYFILE', copyfilecolLst)
    
    copyfilecolLst = ['B','C','D','M','N','O','P','W','X','Y','Z','AA','AB','AC','AD','AE','AH']
    WriteDataMarsBIT(worksheet, startLine,dicPlan23,'MARS_BURNIN', copyfilecolLst)

    WriteDataConclusion(worksheet)

def WriteDataMarsBIT(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    unitLst = ['M']
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            else:
                if index == 1:                
                    if worksheet['%s%d'%(col, curLine)].value != None and worksheet['%s%d'%(col, curLine)].value != '':
                        continue #已经被写过容量信息则跳过

                    #去除单位
                    value = line[index-1].upper()
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    worksheet['%s%d'%(col, curLine)] = int(float(value)*1024) #转换为M
                elif index == 2:
                    if worksheet['%s%d'%(col, curLine)].value != None and worksheet['%s%d'%(col, curLine)].value != '':
                        continue #已经被写过PC NO信息则跳过
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                elif index == 3:
                    worksheet['%s%d'%(col, curLine)] = '25℃' #固定文字
                elif index == 6:
                    #记录测试结果描述
                    resultStr = GetMarsBITResultStr(line)
                    worksheet['%s%d'%(col, curLine)]= resultStr
                    if resultStr.upper() == 'UNFINISHED':
                        worksheet['%s%d'%(col, curLine)]= 'UNFINISHED'
                    if resultStr != 'PASS':
                        if resultStr.upper() == 'UNFINISHED':
                            worksheet['%s%d'%(col, curLine)].fill = PublicFuc.unfinishedFill
                        else:
                            worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
                elif index == 4:
                    #记录的起始时间，需要将时间进行转换
                    startTimeStr = line[index-2]
                    endtimeStr = line[index-1]
                    if '' == startTimeStr or '' == endtimeStr:
                        worksheet['%s%d'%(col, curLine)] = ''
                    else:
                        try:
                            endtime = datetime.strptime(endtimeStr, '%Y-%m-%d %H:%M:%S')
                            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                            totalSecond = timedelta.total_seconds(endtime-starttime)
                            totalSecond = int(totalSecond)
                            hour = int(totalSecond/3600)
                            lefSeconds = totalSecond%3600
                            minutes = int(lefSeconds/60)
                            seconds = lefSeconds%60
                            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                            worksheet['%s%d'%(col, curLine)] = timeStr
                        except:
                            worksheet['%s%d'%(col, curLine)] = ''
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

def WriteDataConclusion(worksheet):
    STARTLINE = 7
    totalRowCnt = len(dicPlan23)
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        h2Result1 = worksheet['%s%d'%('G', rowNo)].value
        h2Result2 = worksheet['%s%d'%('J', rowNo)].value
        h2Result3 = worksheet['%s%d'%('S', rowNo)].value
        h2Result4 = worksheet['%s%d'%('V', rowNo)].value
        BitResult = worksheet['%s%d'%('P', rowNo)].value
        copyfileResult = worksheet['%s%d'%('L', rowNo)].value

        if copyfileResult != None and BitResult != None and (copyfileResult.upper() == 'UNFINISHED' or BitResult.upper() == 'UNFINISHED'):
            worksheet['%s%d'%('AI', rowNo)] = 'UNFINISHED'
            worksheet['%s%d'%('AI', rowNo)].fill = PublicFuc.unfinishedFill
        else:
            if h2Result1 == None or h2Result2 == None or h2Result3 == None or h2Result4 == None or copyfileResult == None or BitResult == None:
                worksheet['%s%d'%('AI', rowNo)] = 'FAIL'
                worksheet['%s%d'%('AI', rowNo)].fill = PublicFuc.warnFill
            else:
                if h2Result1.upper() == 'PASS' and h2Result2.upper() == 'PASS' and h2Result3.upper() == 'PASS' and h2Result4.upper() == 'PASS' and copyfileResult.upper() == 'PASS' and BitResult.upper() == 'PASS':        
                    worksheet['%s%d'%('AI', rowNo)] = 'PASS'
                else:
                    worksheet['%s%d'%('AI', rowNo)] = 'FAIL'
                    worksheet['%s%d'%('AI', rowNo)].fill = PublicFuc.warnFill

def GetDUTH2ResultStr(line):
    str = 'PASS'
    if len(line) < 5:
        str = 'FAIL'
        return str

    #写速度
    wspeed = float(line[2])
    rspeed = float(line[3])
    rawResult = line[4].upper()
    rawResult = rawResult.strip()
    if rawResult == 'PASS' or rawResult == 'TRUE':
        #进一步判定是否有速度超标
        #if wspeed < 10 or rspeed < 50:
            #str = "speed err"
        #else:
        str = 'PASS'
        return str

    else:
        #原样打印测试工具报告中的错误描述
        str = line[4]

    return str


def GetMarsH2ResultStr(line):
    str = 'PASS'
    if len(line) < 7:
        str = 'FAIL'
        return str

    #写速度
    wspeed = float(line[2])
    rspeed = float(line[3])
    rawResult = line[6].upper()
    rawResult = rawResult.strip()
    if rawResult == 'PASS' or rawResult == 'TRUE':
        #进一步判定是否有速度超标
        if wspeed < 10 and rspeed < 50:
            str = "speed err"
        elif wspeed < 10 and rspeed >= 50:
            str = "write speed err"
        elif wspeed >= 10 and rspeed < 50:
            str = "read speed err"
        else:
            str = 'PASS'
        return str

    else:
        #原样打印测试工具报告中的错误描述
        str = line[6]

    return str


def GetMarsBITResultStr(line):
    str = 'PASS'
    if len(line) < 12:
        str = 'FAIL'
        return str

    #写速度
    rawResult = line[5].upper()
    rawResult = rawResult.strip()
    if rawResult == 'PASS' or rawResult == 'TRUE':
        str = 'PASS'
        return str

    else:
        #原样打印测试工具报告中的错误描述
        str = line[5]

    return str
    



#写一个dic
def WriteDataDUTH2(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    unitLst = ['M']
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            else:
                if index == 1:
                    if worksheet['%s%d'%(col, curLine)].value != None and worksheet['%s%d'%(col, curLine)].value != '':
                        continue #已经被写过容量信息则跳过

                    #去除单位
                    value = line[index-1].upper()
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    worksheet['%s%d'%(col, curLine)] = value
                elif index == 2:
                    if worksheet['%s%d'%(col, curLine)].value != None and worksheet['%s%d'%(col, curLine)].value != '':
                        continue #已经被写过PC NO信息则跳过
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                elif index == 5:
                    #记录测试结果描述
                    resultStr = GetDUTH2ResultStr(line)
                    worksheet['%s%d'%(col, curLine)]= resultStr
                    if resultStr != 'PASS':
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
                    
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

#写一个dic
def WriteDataMarsH2(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    unitLst = ['M']
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            else:
                if index == 1:
                    #去除单位
                    value = line[index-1].upper()
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    worksheet['%s%d'%(col, curLine)] = int(float(value)*1024) #转换为M
                elif index == 6:
                    #记录测试结果描述
                    resultStr = GetMarsH2ResultStr(line)
                    worksheet['%s%d'%(col, curLine)]= resultStr
                    if resultStr != 'PASS':
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
                elif index == 5:
                    #记录的起始时间，需要将时间进行转换
                    startTimeStr = line[index-1]
                    endtimeStr = line[index]
                    if '' == startTimeStr or '' == endtimeStr:
                        worksheet['%s%d'%(col, curLine)] = ''
                    else:
                        try:
                            endtime = datetime.strptime(endtimeStr, '%Y-%m-%d %H:%M:%S')
                            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                            totalSecond = timedelta.total_seconds(endtime-starttime)
                            totalSecond = int(totalSecond)
                            hour = int(totalSecond/3600)
                            lefSeconds = totalSecond%3600
                            minutes = int(lefSeconds/60)
                            seconds = lefSeconds%60
                            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                            worksheet['%s%d'%(col, curLine)] = timeStr
                        except:
                            worksheet['%s%d'%(col, curLine)] = ''
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

#写一个dic
def WriteDataMarsCopyFile(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            elif 1 == index:
                if worksheet['%s%d'%(col, curLine)].value == None or worksheet['%s%d'%(col, curLine)].value == '':
                    worksheet['%s%d'%(col, curLine)] = int(float(line[index-1])*1024)
            elif 2 == index:
                if worksheet['%s%d'%(col, curLine)].value == None or worksheet['%s%d'%(col, curLine)].value == '':
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            elif 3== index:
                #起始时间
                startTimeStr = line[index-1]
                endtimeStr = line[index]
                if '' == startTimeStr or '' == endtimeStr:
                    worksheet['%s%d'%(col, curLine)] = ''
                else:
                    try:
                        endtime = datetime.strptime(endtimeStr, '%Y-%m-%d %H:%M:%S')
                        starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                        totalSecond = timedelta.total_seconds(endtime-starttime)
                        totalSecond = int(totalSecond)
                        hour = int(totalSecond/3600)
                        lefSeconds = totalSecond%3600
                        minutes = int(lefSeconds/60)
                        seconds = lefSeconds%60
                        timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                        #hours = timedelta.total_seconds(endtime-starttime)//(60*60)
                        worksheet['%s%d'%(col, curLine)] = timeStr
                    except:
                        worksheet['%s%d'%(col, curLine)] = ''

            elif 4==index:
                 #汇总结果
                result = line[index]
                result = result.strip().upper()
                if result == 'PASS' or result == 'TRUE':
                    result = 'PASS'
                else:
                    result = line[index]
                worksheet['%s%d'%(col, curLine)] = result
                if result != 'PASS':
                    if result == 'UNFINISHED':
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.unfinishedFill
                    else:
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
            else:
                worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

def DrawTable(ws):
    totalRowCnt = len(dicPlan23)
    STARTLINE = 12
    serialNo = 1
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        for ColNo in range(1,1+36):
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
        ws['%s%d'%(get_column_letter(1), rowNo)] = serialNo
        serialNo += 1

def ReadMarsIniDataLocal(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 0):
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue
        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']

            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            resultStr = PublicFuc.GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
                if caseName == 'AT_CopyFile':
                    PublicFuc.AppendErrDiskInfo('Copy File_Err',keyName,resultStr,dataDic[keyName]['MMS_PC'],file)
                elif caseName == 'BurnIn':
                    PublicFuc.AppendErrDiskInfo('RT BIT_Err',keyName,resultStr,dataDic[keyName]['MMS_PC'],file)

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst
        else:
            #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
            oldTime = dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            dataDic[keyName][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']

            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            resultStr = PublicFuc.GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
                if caseName == 'AT_CopyFile':
                    PublicFuc.AppendErrDiskInfo('Copy File_Err',keyName,resultStr,dataDic[keyName]['MMS_PC'],file)
                elif caseName == 'BurnIn':
                    PublicFuc.AppendErrDiskInfo('RT BIT_Err',keyName,resultStr,dataDic[keyName]['MMS_PC'],file)

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst

def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 3
    columnNameMap['(H2)写速度'] = 14
    columnNameMap['(H2)读速度'] = 15
    columnNameMap['(H2)错误'] = 17 #PASS,or 错误码
    #columnNameMap['test_time'] = 29
    columnNameMap['cap'] = 10
    columnNameMap['pc_no'] = 5

#获取H2数据的字典
def InitH2RawDataMap(h2Dic,rawDataList,caseName):
    for row in rawDataList:
        tmpRow = ['']*(len(h2_column_name_map)-1) #减1是不再记录sampleNo，因为key已经记录
        tmpRow[0] = row[h2_column_name_map['cap']]
        tmpRow[1] = row[h2_column_name_map['pc_no']]
        tmpRow[2] = row[h2_column_name_map['(H2)写速度']]
        tmpRow[3] = row[h2_column_name_map['(H2)读速度']]     
        tmpRow[4] = row[h2_column_name_map['(H2)错误']]
        if row[h2_column_name_map['Flash编号']] not in h2Dic:
            h2Dic[row[h2_column_name_map['Flash编号']]] = {}
        h2Dic[row[h2_column_name_map['Flash编号']]][caseName] = tmpRow

        errCode = tmpRow[4]
        if errCode.upper() != 'PASS' and errCode.upper() != 'TRUE':
            sampleID = row[h2_column_name_map['Flash编号']]
            pcNo = tmpRow[1]
            fileName = row[-1]
            PublicFuc.AppendErrDiskInfo('H2_Err',sampleID,errCode,pcNo,fileName)

def ReadRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                row.append(file)
                dataDic.append(row)
[TOOL]
; 工具类型：1 AS SSD, 2 CDM, 3 h2test, 4 hd tune, 5 ATTO DISK, 6 BIT
TYPE = 12
PATH = TOOLS\ATTODiskBenchmark4.00汉化版\ATTO 磁盘基准测试.exe

[PARAM]
; 最小的测试大小，要求使用符合工具列表指定字符串，否则工具无法修改该值,缺省值：512 B
TRANSFER_SIZE_MIN = 512 B
; 最大的测试大小，要求使用符合工具列表指定字符串，否则工具无法修改该值，缺省值：64 MB
TRANSFER_SIZE_MAX = 64 MB
; 测试总长度，要求使用符合工具列表指定字符串，否则工具无法修改该值，缺省值：256 MB
TOTAL_LENGTH = 256 MB
; 是否选中[绕过写入缓存]， 1 是， 0 否，缺省值：1
FORCE_WRITE_ACCESS = 1
; 是否选中[直接传输]，1 是， 0 否，缺省值：1
DIRECT_IO = 1
; 是否选中[校验数据]，缺省值：0
CHECK_DATA = 0

; 数据pattern，仅当MODE = 0 时有效。
; 0:00000000, 1:FFFFFFFF, 2:00FF00FF, 3:FF00FF00, 4:AA55AA55, 
; 5:55AA55AA, 6:33CC33CC, 7:CC33CC33, 8:66996699, 9:99669966, 
; 10:Increment, 11:Decrement, 12:Random
TEST_PATTERN=0
;持续运行,仅当MODE=0时有效
RUN_CONTINUOUSLY = 0
; 仅当RUN_CONTINUOUSLY=1时有效，单位分钟，缺省值：30
RUN_TIME = 30

; 队列深度，仅当MODE=1时有效
QUEUE_DEEP = 4
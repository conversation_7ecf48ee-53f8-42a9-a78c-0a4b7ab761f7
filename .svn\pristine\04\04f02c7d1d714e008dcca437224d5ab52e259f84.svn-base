#include "StdAfx.h"
#include "DeviceMgr.h"
#include <algorithm>



CDeviceMgr::CDeviceMgr(void) 
	: m_pMainWnd(NULL)
	, m_bPreType(0xFF)
{
}

CDeviceMgr::~CDeviceMgr(void)
{
}

void CDeviceMgr::Init(CWnd* _pMainWnd)
{
	m_pMainWnd = _pMainWnd;	
	vector<string> vstrHub;
	UDISK_MAP_INS->Init(m_pMainWnd->GetSafeHwnd());
	UDISK_MAP_INS->UpdateHub();
}

void CDeviceMgr::RefreshAllDevice(BYTE _bType)
{
	return UDISK_MAP_INS->UpdateUDisk(_bType);
}
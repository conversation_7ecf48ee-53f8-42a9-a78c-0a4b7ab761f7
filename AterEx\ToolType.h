﻿#ifndef TOOL_TYPE_H
#define TOOL_TYPE_H
#include <vector>

#include <assert.h>

	typedef enum _TOOL_TYPE
	{
		TOOL_INVALID = 0,

		TOOL_AS_SSD = 1,
		TOOL_CrystalDiskMark = 2,
		TOOL_H2Test = 3,
		TOOL_HD_TUNE = 4,
		TOOL_ATTO_DISK = 5,
		TOOL_BurnInTest = 6,
		TOOL_Format = 7,
		TOOL_DeletePartition = 8,
		TOOL_Idle = 9,
		TOOL_IOMeter = 10,
		TOOL_MARS = 11,
		TOOL_ATTO_DISK4_0 = 12,
		TOOL_HDBENCH = 13,
		TOOL_CrystalDiskInfo = 14,
		TOOL_BurnInTest9_1 = 15,
		TOOL_SleepWakeUp = 16,
		TOOL_Reboot = 17,
		TOOL_MPTool_6285 = 18,
		TOOL_PCMark = 19,
		TOOL_TxBENCH = 20,
		TOOL_PCMarkVantage = 21,
		TOOL_AIDA = 22,
		TOOL_AnvilsStorageUtilities = 23,
		TOOL_CDM8Latter = 24,
		TOOL_ATTO_DISK4_01_Latter = 25,
		TOOL_LuDaShiDiskCheck = 26,
	}TOOL_TYPE;
	
	/**
	* @brief AS SSD工具的执行参数
	*	该结构体中，包含输入和输出参数
	* @struct STC_AS_SSD
	*/
	typedef struct _STC_AS_SSD 
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		char szTestMode[10];			///< [in] 测试模式, eg: 1 GB, 3GB……

		bool bSeqTest;					///< [in][out] 是否测试顺序项, true 测试，false 不测试 
		bool b4KTest;					///< [in][out] 是否测试4K项, true 测试，false 不测试 
		bool b4K64ThrdTest;				///< [in][out] 是否测试4K 64线程项, true 测试，false 不测试 
		bool bAccTime;					///< [in][out] 是否测试acc time项, true 测试，false 不测试 

		// 测试结果
		char szSeqRead[20];				///< [out] 顺序读速度
		char szSeqWrite[20];			///< [out] 顺序写速度
		char sz4KRead[20];				///< [out] 4K写速度
		char sz4KWrite[20];				///< [out] 4K读速度
		char sz4K64ThrdRead[20];		///< [out] 4K64线程读速度
		char sz4K64ThrdWrite[20];		///< [out] 4K64线程写速度
		char szRdAccTime[20];			///< [out] read acc time
		char szWrAccTime[20];			///< [out] write acc time

		char szScoreRd[10];				///< [out] read score
		char szScoreWr[10];				///< [out] write score
		char szScoreToltal[10];			///< [out] total score
	}STC_AS_SSD;

	typedef struct _STC_CrystalDiskMark
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int circle;						///< [in][out] 测试圈数：1-9
		int testCap;					///< [in][out] 测试容量：0 50M, 1 100M, 2 500M, 3 1G, 4 2G, 5 4G, 6 8G, 7 16G, 8 32G
		int mode;						///< [in] 0 全部模式 1-4: 从上至下的四个单项模式

		// 测试结果
		char szTestItemName[4][20];		///< [out] 四列测试项的名称
		char szReadSpeed[4][10];		///< [out] 四列测试项的读速度
		char szWriteSpeed[4][10];		///< [out] 四列测试项的写速度
	}STC_CrystalDiskMark;

	typedef struct _STC_H2_TEST
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:\ 
		char szSnapPath[260];			///< [in] 图片截图的完全路径名
		
		bool bEnLang;					///< [in][out] 语种是否为英文
		bool bFixedVolume;				///< [in][out] 是否指定容量测试, true 指定测试大小， false 测试可用容量
		double percent;					///< [in] 测试比例
		char szTestCap[20];				///< [in][out] 测试容量, 仅当bFixedVolume=true且percent=0时有效
		bool bEndlessVerify;			///< [in][out] test endless verify, true 无限校验(可根据时间停止), false 仅校验一次
		int nTestMinute;				///< [in] 测试时间,单位:分钟, 仅当bEndlessVerify = true时有效, nTestMin = 0时，无限校验
		bool bTestMode;					///< [in] 测试模式 true write&verify, false verify
		int nDelFilePercent;			///< [in] 删除已创建文件的文件百分比，缺省值：0
		
		char szWrData[50];				///< [out] 写数据	
		char szWrTime[20];				///< [out] 写时间
		char szWrSpeed[50];				///< [out] 写速度
		char szRdData[50];				///< [out] 写数据	
		char szRdTime[20];				///< [out] 写时间
		char szRdSpeed[50];				///< [out] 测试读速度
		char szLog[2000];				///< [out] 测试log
	}STC_H2_TEST;

	// 目前HDTune仅支持基准测试和文件基准测试，要求启动前点击基准测试界面
	typedef struct _STC_HD_Tune
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int nPhyDiskNo;					///< [in] 物理磁盘号
		bool bWrite;						///< [in] 测试模式, 0 读取, 1 写入
		bool bFastTrip;					///< [in] 是否选择快捷行程, true 是， false 否
		char szFastTripGB[20];				///< [in] 快捷行程的测试量（GB），仅当bFastTrip = true时有效
		
		bool bTransRate;				///< [in][out] 是否测试传输速率, true 是， false 否
		bool bAcessTime;				///< [in][out] 是否测试存取时间
		bool bSuddenTransRate;			///< [in][out] 是否测试突发传输速率
		bool bFullTest;                 ///< [in][out] 是否完整测试

		// 测试结果
		char szMinSpeed[20];			///< [out] 最小速度, 仅当bTransRate=true时有效
		char szMaxSpeed[20];			///< [out] 最大速度, 仅当bTransRate=true时有效
		char szAvgSpeed[20];			///< [out] 平均速度, 仅当bTransRate=true时有效

		char szAcessTime[20];			///< [out] 访问时间，仅当bAcessTime时有效
		char szSuddenTransRate[20];		///< [out] 突发传输速率，仅当bSuddenTransRate=true时有效
		char szCPUUsage[20];			///< [out] CPU占用率

		int nTestItem;						///< [in] 测试项目, 0 基准测试, 1 文件基准测试
		bool bTestFileBaseSpeed;			///< [in] 是否测试传输速度
		int  nSpeedTestFileLen;				///< [in] 速度测试文件大小，单位为M，值默认为0，代表按照磁盘实际大小去自动选择文件大小
		int nTestFileLenAutoCalculateMode;       ///< [in] 测试文件长度自动计算模式，0-代表满盘时确定的模式，1-代表定容时，按实际剩余容量减去固定值得到容量，其它数字留存
		char szSpeedTestDataMode[20];			///< [in] 速度测试数据模式：零，随机，混合
		int nSpeedTestQueueDeep;		///< [in] 速度测试队列深度，1-64，默认为32
		bool bTestFileBaseBlock;			///< [in] 是否按照块大小进行测试
		char szBlockTestFileLen[20];				///< [in] 块大小测试使用的文件大小，只能选择固定几种值
		int nBlockTestDelay;					///< [in] 块大小测试时的延迟时间，默认0
	}STC_HD_Tune;

	typedef struct _STC_ATTO
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char disk_letter[8];			///< [in][out] 磁盘盘符,eg:[-c-]
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		char szTransferSize_min[20];	///< [in][out] 传输大小，最小值
		char szTransferSize_max[20];	///< [in][out] 传输大小，最大值
		char szTotalLength[20];			///< [in][out] 总测试长度
		bool bForceWriteAccess;			///< [in][out] 是否选中[Force Write Access], true 是; false 否
		bool bDirectIO;					///< [in][out] 是否选中[Direct I/O], true 是, false 否
		int mode;						///< [in][out] 命令模式， 0 I/O Comparison, 1 Overlapped I/O, 2 Neither
		char szTestPattern[20];			///< [in][out] Test Pattern, 仅mode=0时有效
		bool bRunContinuously;			///< [in][out] 是否选中[Run Continuously], true 是, false 否, 仅mode=0时有效

		char szRunTime[10];				///< [in][out] 持续执行的时间，仅[Run Continuously]选中时有效
		char szQueueDeep[3];			///< [in][out] 队列深度，仅mode=1时有效		
		
		char result[30][3][20];			///< [out] 测试结果，0,写速度，1, 读速度, 2, 命令长度
	}STC_ATTO;

	typedef struct _STC_ATTO4_0
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char disk_letter[8];			///< [in][out] 磁盘盘符,eg:[-c-]
		char szSnapPathMBps[260];		///< [in] 图片截图的完全路径名
		char szSnapPathIOps[260];		///< [in] IO/S界面图片截图的完全路径名

		char szTransferSize_min[20];	///< [in][out] 传输大小，最小值
		char szTransferSize_max[20];	///< [in][out] 传输大小，最大值
		char szTotalLength[20];			///< [in][out] 总测试长度
		bool bForceWriteAccess;			///< [in][out] 是否选中[绕过写入缓存], true 是; false 否 是否绕过写入缓存，这里针对的是写操作
		bool bDirectIO;					///< [in][out] 是否选中[直接传输], true 是, false 否 是否选择直接传输，这里针对的是读。
		bool bCheckData;				///< [in][out] 是否选中[校验数据], true 是, false 否

		char szTestPattern[20];			///< [in][out] 数据模式，Test Pattern, 仅bCheckData=1时有效
		bool bRunContinuously;			///< [in][out] 是否选中[Run Continuously], true 是, false 否, 仅mode=0时有效

		char szRunTime[10];				///< [in][out] 持续执行的时间，仅[Run Continuously]选中时有效
		char szQueueDeep[3];			///< [in][out] 队列深度，仅mode=1时有效		

		char result[30][3][20];			///< [out] 测试结果，0,写速度，1, 读速度, 2, 命令长度
	}STC_ATTO4_0;

	typedef struct _STC_BIT
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:"
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int nCycle;						///< [in] 测试圈数, 0 means run forever
		int nMiniutes;					///< [in] 测试分钟, 0 means run forever, set 100 when it's above 100.
		int nTestLoad;					///< [in] 测试强度, 0-100, set 100 when it's above 100.

		// 		0: Default (Cyclic)
		// 		1:Sequential data pattern (0,1,2...255)
		// 		2:Random data with random seeking
		// 		3:High Low freq data overwrite (10101 then 00001)
		// 		4:Butterfly seeking
		// 		5:Binary data pattern 1 (10101010)
		// 		6:Binary data pattern 2 (01010101)
		// 		7:Zeros data pattern (00000000)
		// 		8:Ones data pattern (11111111)
		// 		9:Random data pattern
		// 		10:User defined test pattern
		// 		11:Quick physical drive test, 非系统物理盘专用
		// 		12:Physical drive read test, 物理盘专用
		int nTestMode;					///< [in] 测试模式, 0-10 非系统盘的所有磁盘; 11 仅支持非系统盘的物理盘 12 支持所有物理盘

		char szFileSzie[10];			///< [in] 文件大小，eg:1.00
		char szBlockSize[10];			///< [in] 块大小，单位：KB
		char szSeekCount[20];			///< [in] seek count
		char szPattern[100];			///< [in] pattern
		char szSlowDriveThreshold[20];	///< [in] slow drive threshold
		char szDutyCycleOverride[20];	///< [in] duty cycle override

		bool bRunSelfTest;				///< [in] 是否开启自检
		bool bBadSectorIncrease;		///< [in] 仅当bRunSelfTest=true时有效
		char szBadSectorThreshold[20];	///< [in] 仅当bBadSectorIncrease=true时有效

		char szResult[1024*1024];		///< [in] 测试结果，最大1M
		char szEventLog[1024*1024];		///< [in] 事件日志，最大1M
	}STC_BIT;//此结构体是控制8.0版本的

	typedef struct _STC_BIT9_1
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8][4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:",专门放逻辑盘符，用于多分区测试，最多支持8个分区。
		int nTestPartitionCnt;//表明分区要测试的盘符数量,如果数量为0，代表测试物理盘，如果数量为1，代表测试第一个分区，数量为2，代表测试前2个分区。
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int nCycle;						///< [in] 测试圈数, 0 means run forever
		int nMiniutes;					///< [in] 测试分钟, 0 means run forever, set 100 when it's above 100.
		int nTestLoad;					///< [in] 测试强度, 0-100, set 100 when it's above 100.
		bool bQuitAtOnce;               /// 是否马上退出留bit继续跑

		// 		0: Default (Cyclic)
		// 		1:Sequential data pattern (0,1,2...255)
		// 		2:Random data with random seeking
		// 		3:High Low freq data overwrite (10101 then 00001)
		// 		4:Butterfly seeking
		// 		5:Binary data pattern 1 (10101010)
		// 		6:Binary data pattern 2 (01010101)
		// 		7:Zeros data pattern (00000000)
		// 		8:Ones data pattern (11111111)
		// 		9:Random data pattern
		// 		10:User defined test pattern
		// 		11:Quick physical drive test, 非系统物理盘专用
		// 		12:Physical drive read test, 物理盘专用
		int nTestMode;					///< [in] 测试模式, 0-10 非系统盘的所有磁盘; 11 仅支持非系统盘的物理盘 12 支持所有物理盘

		char szFileSzie[10];			///< [in] 文件大小，eg:1.00
		char szBlockSize[10];			///< [in] 块大小，单位：KB
		char szSeekCount[20];			///< [in] seek count
		char szPattern[100];			///< [in] pattern
		char szSlowDriveThreshold[20];	///< [in] slow drive threshold
		char szDutyCycleOverride[20];	///< [in] duty cycle override

		bool bRunSelfTest;				///< [in] 是否开启自检
		bool bBadSectorIncrease;		///< [in] 仅当bRunSelfTest=true时有效
		char szBadSectorThreshold[20];	///< [in] 仅当bBadSectorIncrease=true时有效

		char szResult[1024*1024];		///< [in] 测试结果，最大1M
		char szEventLog[1024*1024];		///< [in] 事件日志，最大1M
		int nActionOnErr; ///< [in] err时BIT的行为：0-Auto stop test 1-Continue，默认为0
	}STC_BIT9_1;

	typedef struct _STC_FORMAT
	{
		char cDiskLetter;				///< [in][required] 格式化后的逻辑盘符
		char szWaterMark[100];			///< [in] 设备水印
		char szFormat[20];				///< [in] 文件系统, 根据系统选择，目前可以支持exFAT,FAT32,NTFS
		char cLogPath[260];             /// log目录，输出diskpart结果
		unsigned __int64 nDiskSize;                 /// 磁盘容量,sec
		int nPerCent;					///< [in] 分区百分比，该值为0, 则用nSize
		int nSize;						///< [in][op] 分区大小，该值为0, 则格式化所有分区
		int nPhyDisk;					///< [in][required] 需要格式化的的物理磁盘
	}STC_FORMAT;

	typedef struct _STC_DEL_PART
	{
		char szWaterMark[100];			///< [in] 设备水印
		int nPhyDisk;					///< [in][required] 物理盘符
	}STC_DEL_PART;

	typedef struct _STC_IDLE
	{
		int nMin;						///< [in][required] 空闲的时间，单位：分钟
		int nSec;						///< [in][required] 空闲的时间，单位：秒
	}STC_IDLE;

	// 目前Iometer的测试参数
	typedef struct _STC_IOMeter
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int phyNo;
		bool bOpenByEx;
		char icfPath[260];
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:\ 
		bool bTestSeq512Write;			///< [in][out] 是否测试顺序512K写项, true 测试，false 不测试 
		bool bTestSeq512Read;			///< [in][out] 是否测试顺序512K读项, true 测试，false 不测试 
		bool bTestRand4KWrite;			///< [in][out] 是否测试4K随机写项, true 测试，false 不测试 
		bool bTestRand4KRead;			///< [in][out] 是否测试4K随机读项, true 测试，false 不测试 
		int nRunTime;					///< [in] 执行时间，单位分钟
		int RampUpTime;					///< [in] 延迟时间，单位秒
		char maximumDiskSize[20];		///< [in] 磁盘大小，单位扇区sector,对于INI文件任何数字都可以按字符串配置。为了支持更大容量，采用字符串

		// 测试结果
		char szTotalIOPerSecond[20];	///< [out] szTotalIOPerSecond
		char szTotalMBsPerSecond[20];	///< [out] szTotalMBsPerSecond
		char szAvgIOResponseTime[20];	///< [out] szAvgIOResponseTime
		char szMaxIOResponseTime[20];	///< [out] szMaxIOResponseTime

		char szSeqWrite512KSpeed[20];	///< [out] 
		char szSeqRead512KSpeed[20];	///< [out] 
		char szRandWrite4KSpeed[20];	///< [out] 
		char szRandRead4KSpeed[20];	    ///< [out] 
	}STC_IOMeter;

	typedef struct _STC_MARS
	{
		char FlowName[256];  ///< [in]
	}STC_MARS;

	typedef struct _STC_HDBENCH
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char disk_letter[8];			///< [in] 要测试的盘符
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int nPhyDiskNo;					///< [in] 物理磁盘号
		char szTotalSize[20];			///< [in][out] 总测试长度

		// 测试结果
		char szReadSpeed[20];			///< [out] 读速度
		char szWriteSpeed[20];			///< [out] 写速度
		char szRandomRead[20];			///< [out] 随机读速度
		char szRandomWrite[20];			///< [out] 随机读速度
	}STC_HDBENCH;

	typedef struct _STC_CrystalDiskInfo
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char disk_letter[8];			///< [in] 要测试的盘符,作为标志在CDI工具上面找到对应的盘
		char szSnapPath[260];			///< [in] 图片截图的完全路径名
		int nPhyDiskNo;					///< [in] 物理磁盘号
	}STC_CrystalDiskInfo;

	typedef struct _INI_REPORT_INFO
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		std::vector<std::string> vStrKey;
		std::vector<std::string> vStrValue;

	}INI_REPORT_INFO,*PINI_REPORT_INFO;

	typedef struct _STC_SleepWakeUp
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		int nSleepTime;					///< [in] Sleep时间，单位秒
		int nSleepTimeInterval;		    ///< [in] Sleep间隔时间，单位秒
		int nCycle;						///< [in] SleepWakeUp的圈数，默认为1
	}STC_SleepWakeUp;

	typedef struct _STC_MPTool_6285
	{
		int nRepeatMode;                    ///< [in]测试模式，按时间测试还是按照次数测试。0-按照时间，默认，1-按照次数测试
		int nMPTime;						///< [in] 测试多少分钟（高格测试指定时间）
		int nMPCount;                      ///< [in] 测试多少次（低格测试指定次数）
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szSnapPath[260];			///< [in] 图片截图的完全路径名
	}STC_MPTool_6285;

	typedef struct _STC_PC_MARK
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:"
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

	}STC_PC_MARK;

		/**
	* @brief TxBENCH工具的执行参数
	*	该结构体中，包含输入和输出参数
	* @struct STC_TxBENCH
	*/
	typedef struct _STC_TxBENCH
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int nUpdateFrequency; ///< [in] 
		int nMaxLogLine; ///< [in] 
		bool bHideSerialNumber; ///< [in] 
		bool bCheckReleaseInfo; ///< [in] 
		int nKBSize; ///< [in] 
		int nStartPosition; ///< [in] 单位MB
		char szMeasureSize[20]; ///< [in]
		char szAlignSize[20]; ///< [in]
		char szWritedataMode[20]; ///< [in] 填充的数据模式
		bool bPrewrite;///< [in]

		//------各个task配置
		bool bCheckTask1; ///< [in]
		char szSeekMethodTask1[20];///< [in]
		char szTransSizeTask1[20];///< [in]
		char szQDTask1[20];///< [in]
		char sztThreadCnt1[20];///< [in]
		char szActionTask1[20];///< [in] wirte+read、read、write

		bool bCheckTask2; ///< [in]
		char szSeekMethodTask2[20];///< [in]
		char szTransSizeTask2[20];///< [in]
		char szQDTask2[20];///< [in]
		char sztThreadCnt2[20];///< [in]
		char szActionTask2[20];///< [in] wirte+read、read、write

		bool bCheckTask3; ///< [in]
		char szSeekMethodTask3[20];///< [in]
		char szTransSizeTask3[20];///< [in]
		char szQDTask3[20];///< [in]
		char sztThreadCnt3[20];///< [in]
		char szActionTask3[20];///< [in] wirte+read、read、write

		bool bCheckTask4; ///< [in]
		char szSeekMethodTask4[20];///< [in]
		char szTransSizeTask4[20];///< [in]
		char szQDTask4[20];///< [in]
		char sztThreadCnt4[20];///< [in]
		char szActionTask4[20];///< [in] wirte+read、read、write

		//------------------------以下是原来就有的------------------------------

		char szTestMode[10];			///< [in] 测试模式, eg: 1 GB, 3GB……

		bool bSeqTest;					///< [in][out] 是否测试顺序项, true 测试，false 不测试 
		bool b4KTest;					///< [in][out] 是否测试4K项, true 测试，false 不测试 
		bool b4K64ThrdTest;				///< [in][out] 是否测试4K 64线程项, true 测试，false 不测试 
		bool bAccTime;					///< [in][out] 是否测试acc time项, true 测试，false 不测试 

		// 测试结果
		char szSeqRead[20];				///< [out] 顺序读速度
		char szSeqWrite[20];			///< [out] 顺序写速度
		char sz4KRead[20];				///< [out] 4K写速度
		char sz4KWrite[20];				///< [out] 4K读速度
		char sz4K64ThrdRead[20];		///< [out] 4K64线程读速度
		char sz4K64ThrdWrite[20];		///< [out] 4K64线程写速度
		char szRdAccTime[20];			///< [out] read acc time
		char szWrAccTime[20];			///< [out] write acc time

		char szScoreRd[10];				///< [out] read score
		char szScoreWr[10];				///< [out] write score
		char szScoreToltal[10];			///< [out] total score
	}STC_TxBENCH;

	typedef struct _STC_PC_MARK_VANTAGE
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:"
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

	}STC_PC_MARK_VANTAGE;

	typedef struct _STC_AIDA
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:"
		char szSnapPath[260];			///< [in] 图片截图的完全路径名
		int nPhyDiskNo;					///< [in] 物理磁盘号 用于选盘

		int nBlockSize; ///< [in] 
		bool bLoop; ///< [in] 
		bool bDisplayInKB; ///< [in] 
		bool bContainWriteTests; ///< [in] 
		char szTestItem[50]; ///< [in]
	}STC_AIDA;

	typedef struct _STC_AnvilsStorage
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:"
		char szSnapPath[260];			///< [in] 图片截图的完全路径名
		int nPhyDiskNo;					///< [in] 物理磁盘号 用于选盘

		char szCfgPath[260];///< [in] 配置文件路径，工具AnvilsStorage的配置是保存在一个配置文件中，此为配置文件路径。
		char szTestSize[20]; ///< [in] 字符串1GB,2GB的描述
	}STC_AnvilsStorage;

	typedef struct _STC_CDM8Latter
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[8];			///< [in][out] 磁盘盘符,eg:Z:
		char szSnapPath[260];			///< [in] 图片截图的完全路径名

		int circle;						///< [in][out] 测试圈数：1-9
		int mode;						///< [in] 0 全部模式 1-4: 从上至下的四个单项模式
		int cfgMode;       ///< [in] 0 Default配置 1  NVMe SSD, 2 custom定制模式 -目前流程并不需要此流程配置，不需要做这么复杂。如果为2则采用和0一样的default模式。

		char szTestCap[20];		///< [in] 显示结果的单位，包含16MiB-64GiB,默认1GiB
		char szDisplayUnit[20];		///< [in] 显示结果的单位，包含MB/s、GB/s、IOPS、us

		// 测试结果
		char szTestItemName[4][20];		///< [out] 四列测试项的名称
		char szReadSpeed[4][10];		///< [out] 四列测试项的读速度
		char szWriteSpeed[4][10];		///< [out] 四列测试项的写速度
	}STC_CDM8Latter;

	typedef struct _STC_ATTO4_01_Latter
	{
		int nToolLanguage;	///< [in] 工具的语言 0-english 1- chinese，2-日语 其它暂时不支持
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char disk_letter[8];			///< [in][out] 磁盘盘符,eg:[-c-]
		char szSnapPathMBps[260];		///< [in] 图片截图的完全路径名
		char szSnapPathIOps[260];		///< [in] IO/S界面图片截图的完全路径名

		char szTransferSize_min[20];	///< [in][out] 传输大小，最小值
		char szTransferSize_max[20];	///< [in][out] 传输大小，最大值
		char szTotalLength[20];			///< [in][out] 总测试长度
		bool bForceWriteAccess;			///< [in][out] 是否选中[绕过写入缓存], true 是; false 否 是否绕过写入缓存，这里针对的是写操作
		bool bDirectIO;					///< [in][out] 是否选中[直接传输], true 是, false 否 是否选择直接传输，这里针对的是读。
		bool bCheckData;				///< [in][out] 是否选中[校验数据], true 是, false 否

		char szTestPattern[20];			///< [in][out] 数据模式，Test Pattern, 仅bCheckData=1时有效
		bool bRunContinuously;			///< [in][out] 是否选中[Run Continuously], true 是, false 否, 仅mode=0时有效

		char szRunTime[10];				///< [in][out] 持续执行的时间，仅[Run Continuously]选中时有效
		char szQueueDeep[3];			///< [in][out] 队列深度，仅mode=1时有效		

		char result[30][3][20];			///< [out] 测试结果，0,写速度，1, 读速度, 2, 命令长度
	}STC_ATTO4_01_Latter;

	typedef struct _STC_LuDaShiDiskCheck
	{
		char szWatermark[100];			///< [in] 允许不超过30个字符的水印
		char szDiskLetter[4];			///< [in] 逻辑磁盘 "C:", 物理磁盘"00:", "01:"
		char szSnapPath[260];			///< [in] 图片截图的完全路径名
	}STC_LuDaShiDiskCheck;

#ifndef SAFE_DELETE 
#define SAFE_DELETE(p) {if(p){delete(p);(p)=NULL;}}
#endif

#ifndef SAFE_DELETE_ARRAY
#define SAFE_DELETE_ARRAY(p) { if(p){delete[] (p); (p)=NULL;} }
#endif
#ifndef SAFE_DELETE
#define SAFE_DELETE(p) { if(p) delete (p);(p)=NULL;}
#endif



#if _DEBUG
#define CHECK_RESULT(ret) if (!(ret)){ assert(false); return false; }
#else
#define CHECK_RESULT(ret) if (!(ret)){ return false; }
#endif

#if _DEBUG
#define CHECK_RESULT_LOG(ret, eno) if (!(ret)){ assert(false); Trace(ErrCode2String(eno)); m_errorCode = eno; return false; }
#else
#define CHECK_RESULT_LOG(ret, eno) if (!(ret)){ Trace(ErrCode2String(eno)); m_errorCode = eno; return false; }
#endif

#define TRACE_ERROR(ERR) {Trace(ERROR_INFO[ERR]); m_errorCode = ERR;}

#define CHECK_BREAK(v) {if(!(v)) break;}

// 将整数转换为bool
#define INT2BOOL(v) (v>0?true:false)

#endif

#define  WM_SLEEP_INFO (WM_USER + 103) //sleep当前圈数信息
#define  WM_SEND_REPORT (WM_USER+9998)
#define  WM_COMMON_LOG  (WM_USER+9999)

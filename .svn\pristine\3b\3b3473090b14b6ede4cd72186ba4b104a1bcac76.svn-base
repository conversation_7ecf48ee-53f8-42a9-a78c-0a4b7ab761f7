import PublicFuc

dicMax = {}
dicHWCardReader = {}
dicCardReader = {}

def Run(curpath, workBook, alignment):
    ws = workBook['PC端性能']
    ws.alignment = alignment
    wsPic = workBook['PC性能截图']
    wsPic.alignment = alignment
    ProNano(curpath, ws, wsPic)
   
    
def ProNano(curpath, worksheet, wsPic):
    #数据读取
    keyLst = [['SeqQ32T1_Read','SeqQ32T1_Write','4KiBQ8T8_Read','4KiBQ8T8_Write','4KiBQ32T1_Read','4KiBQ32T1_Write','4KiBQ1T1_Read','4KiBQ1T1_Write'],
              ['64 MB_Write','64 MB_Read'],
              ['min spped','max spped','avg spped'],
              ['min spped','max spped','avg spped'],
              ['write speed','read speed']]
    caseLst = ['T_EM_SD_C12','T_EM_SD_C71','T_EM_SD_C69','T_EM_SD_C70','T_EM_SD_C66']
    picLst =  ['CDM.bmp','ATTO4_0_MBps.bmp','HDTune.bmp','HDTune.bmp','H2.bmp']
    itemLst = [[['CDM_1','CDM_2','CDM_3','CDM_NTFS_1','CDM_NTFS_2','CDM_NTFS_3'],['CDM_4','CDM_5','CDM_6','CDM_NTFS_4','CDM_NTFS_5','CDM_NTFS_6']],
               [['ATTO_1','ATTO_2','ATTO_3','ATTO_NTFS_1','ATTO_NTFS_2','ATTO_NTFS_3'],['ATTO_4','ATTO_5','ATTO_6','ATTO_NTFS_4','ATTO_NTFS_5','ATTO_NTFS_6']],
               [['HDTune_Read_1','HDTune_Read_2','HDTune_Read_3','HDTune_Read_NTFS_1','HDTune_Read_NTFS_2','HDTune_Read_NTFS_3'],['HDTune_Read_4','HDTune_Read_5','HDTune_Read_6','HDTune_Read_NTFS_4','HDTune_Read_NTFS_5','HDTune_Read_NTFS_6']],
               [['HDTune_Write_1','HDTune_Write_2','HDTune_Write_3','HDTune_Write_NTFS_1','HDTune_Write_NTFS_2','HDTune_Write_NTFS_3'],['HDTune_Write_4','HDTune_Write_5','HDTune_Write_6','HDTune_Write_NTFS_4','HDTune_Write_NTFS_5','HDTune_Write_NTFS_6']],
               [['H2testw_1','H2testw_2','H2testw_3','H2testw_NTFS_1','H2testw_NTFS_2','H2testw_NTFS_3']]]
    startColLst = [['I','AB'],['Q','AJ'],['S','AL'],['V','AO'], ['Y']]
    picColLst = [['I','Y'],['L','AB'],['O','AE'],['R','AH'],['U']]
    for idx,case in  enumerate(caseLst):
        for itLst in itemLst[idx]:
            for item in itLst:
                pattern = '.+\\\\Plan19\\\\%s\\\\%s\\\\\d{14}\\\\report.ini$'%(case, item)
                PublicFuc.ReadQaIniDataForNano(curpath, pattern, dicHWCardReader, item, keyLst[idx], picLst[idx], dicMax, case, 4)

    for idx,case in  enumerate(caseLst):
        for itLst in itemLst[idx]:
            for item in itLst:
                pattern = '.+\\\\Plan20\\\\%s\\\\%s\\\\\d{14}\\\\report.ini$'%(case, item)
                PublicFuc.ReadQaIniDataForNano(curpath, pattern, dicCardReader, item, keyLst[idx], picLst[idx], dicMax, case, 4)
    startLine = 5
    PublicFuc.WriteDataAndImageForNano(worksheet, wsPic, startLine, dicHWCardReader, itemLst, startColLst, keyLst, dicMax, caseLst, picColLst)
    startLine = 29
    PublicFuc.WriteDataAndImageForNano(worksheet, wsPic, startLine, dicCardReader, itemLst, startColLst, keyLst, dicMax, caseLst, picColLst)
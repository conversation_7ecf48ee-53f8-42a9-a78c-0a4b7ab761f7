﻿#pragma once

#include "..\baseDeviceIO\DeviceIo.h"

class CNVMeDeviceIo : public SGDeviceIo::CDeviceIo
{
	typedef struct
	{
		BYTE bOpcode;
		DWORD dwNSID;
		DWORD dw10;
		DWORD dw11;
		DWORD dw12;
		DWORD dw13;
		DWORD dw14;
		DWORD dw15;
		BYTE bVendorControl;
		DWORD dwDataLength;
		BYTE bProtocolField;
		ULONG ulTimeout;
	} COMMAND_SET;

public:
	CNVMeDeviceIo(void);
	virtual ~CNVMeDeviceIo(void);

public:
	virtual Bool DeviceIO(void *_pCMD, U32 _nCMDSize, U8 *_pBuffer, U32 _bufferSize);

	virtual void CloseDevice();

	virtual Bool OpenDevice(U8 _phyDiskNo, Bool bShare/* = True*/);

private:
	std::string CreatePhysicDevPath(U8 _phyDiskNo);
	Bool SendNVMeInbox(HANDLE hDeviceIOCTL, void *_pSrtCommandSet, BYTE *bIOBuf);
	BOOL GetFWMode(HANDLE hDeviceIOCTL);

	BOOL DeviceGetFirmwareInfo(HANDLE hDeviceIOCTL, DWORD BufferLength, BYTE *bIOBuf);

	BOOL DeviceFirmwareUpgrade(HANDLE hDeviceIOCTL, void *_pSrtCommandSet, BYTE *bIOBuf);

	BOOL DeviceFirmwareCommit(HANDLE hDeviceIOCTL, void *_pSrtCommandSet, BYTE *bIOBuf);
};



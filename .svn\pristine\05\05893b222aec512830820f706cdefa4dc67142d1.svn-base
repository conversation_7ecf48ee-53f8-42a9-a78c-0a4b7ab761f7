import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta


def Run(curpath, workBook, alignment):
    ws = workBook['Data coverage']
    ws.alignment = alignment
    ProHCTest(curpath, ws,8)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'H',2)

def ProHCTest(curpath,worksheet,recordCnt):
    pattern = '.+\\\\Plan13\\\\T_GE_SD_C30\\\\HCTEST\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'HCTest'
    caseDicHCTest = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicHCTest, caseName, recordCnt)

    keyLst = ['capacity','pc_no','format_time','test_time','circle','result','conclusion']
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewHCTestReportDic(caseDicHCTest)

    colLst = ['B','C','D','E','F','G','H','I']

    startLine = 5
   
    #写内容
    resultColumnList = ['H']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst,1,resultColumnList)


def GetNewHCTestReportDic(caseDicHCTest):
    newDic = {}
    for key in caseDicHCTest:
        newDic[key] = []
        dic = caseDicHCTest[key]   

        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位

        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])

        #FORMATTIME
        if 'format_time' in dic:
            if '' == dic['format_time']:
                newDic[key].append('')
            else:
                newDic[key].append(dic['format_time'])
        else:
            newDic[key].append('')

        #测试时间
        endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
        startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')
        if '' == endTimeStr or '' == startTimeStr:
            newDic[key].append('')
        else:
            try:
                endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                totalSecond = timedelta.total_seconds(endtime-starttime)
                hour = int(totalSecond/3600)
                lefSeconds = totalSecond%3600
                minutes = int(lefSeconds/60)
                seconds = lefSeconds%60
                timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                newDic[key].append(timeStr)
            except:
                newDic[key].append('')

        testCycle = PublicFuc.GetValueFromDic(dic,'teset_circle')
        if '' == testCycle:
            newDic[key].append('')
        else:
            newDic[key].append(int(testCycle))

        #测试结果
        testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
        newDic[key].append(testResult)
        
        if testResult == 'TRUE':
            newDic[key].append('PASS')
        else:
            newDic[key].append('FAIL')

    return newDic 
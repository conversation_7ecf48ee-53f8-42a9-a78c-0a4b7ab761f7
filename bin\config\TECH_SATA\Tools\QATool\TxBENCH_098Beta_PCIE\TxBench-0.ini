[TOOL]
; 工具类型：1 AS SSD, 2 CDM, 3 h2test, 4 hd tune, 5 ATTO DISK, 6 BIT
TYPE = 20
PATH = TOOLS\TxBENCH\TxBENCH.exe

[PARAM]
; UpdateFrequency,单位秒
UPDATE_FREQUENCY = 1
; 日志最大记录长度
MAX_LOG_LINE = 2000
; Hide Serial Number 0-否，1-是
IS_HIDE_SERIALNUMBER = 0
; 
CHECK_RELEASE_INFO = 0
; 1kb对于的字节数
KB_BYTES = 1024
; START_POSITION 单位MB
START_POSITION = 0
; Measurement size:
MEASUREMENT_SIZE = 512 MB
; Alignment size:
ALIGNMENT_SIZE = 4 KB
; Write data:
WRITE_DATA = Random number
; Prewrite
IS_PREWRITE = 0
; TASK1,是否测试任务1
IS_CHECK_TASK1 = 1
SEEK_METHOD_TASK1 = Sequential
TRANSMISSION_SIZE_TASK1 = Max(2048 KB)
QD_TASK1 = 32
THREAD_CNT_TASK1 = 1
ACTION_TASK1 = Read + Write
; TASK2,是否测试任务2
IS_CHECK_TASK2 = 1
SEEK_METHOD_TASK2 = Random
TRANSMISSION_SIZE_TASK2 = Max(2048 KB)
QD_TASK2 = 1
THREAD_CNT_TASK2 = 1
ACTION_TASK2 = Read + Write
; TASK3,是否测试任务3
IS_CHECK_TASK3 = 1
SEEK_METHOD_TASK3 = Random
TRANSMISSION_SIZE_TASK3 = 4 KB
QD_TASK3 = 1
THREAD_CNT_TASK3 = 1
ACTION_TASK3 = Read + Write
; TASK4,是否测试任务4
IS_CHECK_TASK4 = 1
SEEK_METHOD_TASK4 = Random
TRANSMISSION_SIZE_TASK4 = 4 KB
QD_TASK4 = 32
THREAD_CNT_TASK4 = 1
ACTION_TASK4 = Read + Write
// PortDlg.h : main header file for the PortDlg DLL
//

#pragma once

#ifndef __AFXWIN_H__
	#error "include 'stdafx.h' before including this file for PCH"
#endif

#include "resource.h"		// main symbols


// CPortDlgApp
// See PortDlg.cpp for the implementation of this class
//

class CPortDlgApp : public CWinApp
{
public:
	CPortDlgApp();

// Overrides
public:
	virtual BOOL InitInstance();

	DECLARE_MESSAGE_MAP()
};

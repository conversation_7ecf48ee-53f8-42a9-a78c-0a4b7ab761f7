#include "stdafx.h"
#include "SztopsBoxCtrl.h"
#include <atlstr.h>
#include "..\AutoLock.h"

enum SZTOPS_COMMAND : U8
{
	SZTOPS_COMMAND_01 = 0x01,	// ������01��������Ȧ, Coils�ɶ��Ŀ�������
	SZTOPS_COMMAND_02,			// DiscretesInputֻ���Ŀ�������(������02��
	SZTOPS_COMMAND_03,			// HoldingRegisters�ɶ������ͱ���, �������Ĵ���
	SZTOPS_COMMAND_04,			// InputRegistersֻ�������ͱ���(������04), ������Ĵ���
	SZTOPS_COMMAND_05,			// ������05д������Ȧ, Coils��д�Ŀ�������
	SZTOPS_COMMAND_06,			// HoldingRegisters��д�����ͱ���, д�����Ĵ���
};

static const U8 SZTOPS_STATION = 0x01;		// վ��
// ��������롾01������05д��
static const U16 SZTOPS_CTRL_RUN = 0x1F40;		// 8000, ���а�ť, ��1�豸���У��Զ���λ
static const U16 SZTOPS_CTRL_STOP = 0x1F41;		// 8001, ֹͣ��ť, ��1�豸ֹͣ���Զ���λ
static const U16 SZTOPS_CTRL_PAUSE = 0x1F5D;	// 8029, ��ͣ��ť, ��1���ϸ�λ���Զ���λ
static const U16 SZTOPS_CTRL_CONTINUE = 0x1FA7; // 8103, �������а�ť, ��1���ϸ�λ���Զ���λ
// ��������롾03������06д��
static const U16 SZTOPS_GET_STATUS = 0x1F36;			// 7990, ��ȡ����״̬, ����ֵ��Χ��0-1��, 0��ֹͣ 1������
static const U16 SZTOPS_GET_TEMPERATURE = 0x1F37;		// 7991, ��ȡ��ǰ�¶�, ����ֵ��Χ��-200.0~300.0�桿����ǰ�¶Ȳ���ֵ(һλС��)
static const U16 SZTOPS_GET_TEMPERATURE_SET = 0x1F58;	// 8024, ��ȡ�¶��趨, ����ֵ��Χ��-200.0~300.0�桿���趨�¶ȣ�ֻ����(һλС��)
static const U16 SZTOPS_SET_TEMPERATURE = 0x1FA4;		// 8100, ��ֵ�趨�¶�, ��ʼֵ25, �趨��Χ��-100.0~300.0�桿����ֵ����(һλС��)
static const U16 SZTOPS_SET_MODE = 0x1FAC;				// 8108, ����ģʽ, ��ʼֵ0~1, �趨��Χ��0~1����0:��ֵ���� 1:��ʽ����

CSztopsBoxCtrl::CSztopsBoxCtrl()
	: TCPTempBoxBase()
{
}

CSztopsBoxCtrl::~CSztopsBoxCtrl()
{
	CloseConnection();
}

BOOL CSztopsBoxCtrl::InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs)
{
	return TCPTempBoxBase::InitConnection(_pServerIP, _nPort, _nTimeoutMs);
}

void CSztopsBoxCtrl::CloseConnection()
{
	TCPTempBoxBase::CloseConnection();
}

bool CSztopsBoxCtrl::IsReady()
{
	return TCPTempBoxBase::IsReady();
}

void CSztopsBoxCtrl::SetTargetTemperature(int _nVal)
{
	m_errCode = 0;
	if (!IsReady())
	{
		return;
	}
	CAutoLock autoLock(&m_critiLock);
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_06;

	U16 uVal = SZTOPS_SET_TEMPERATURE;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	S16 sVal = PublicW32::WORDReverse(_nVal);
	memcpy_s((void*)&wBuff[4], 2, &sVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(1000);
}

int CSztopsBoxCtrl::GetTemperature()
{
	m_errCode = 0;
	if (!IsReady())
	{
		return INVALID_TEMPERATURE;
	}
	CAutoLock autoLock(&m_critiLock);
	int nTemperature = INVALID_TEMPERATURE;
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_03;

	U16 uVal = SZTOPS_GET_TEMPERATURE;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = (U16)1;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	int nRet = m_tcpComm.SendData(wBuff, 6);
	Sleep(500);
	if (nRet != 0)
	{
		m_errCode = nRet;
		return INVALID_TEMPERATURE;
	}

	char rBuff[30];
	memset(rBuff, 0, 30);
	int nReadBytesCnt = m_tcpComm.ReceiveData(rBuff, 30);
	Sleep(1000);

	if (nReadBytesCnt < 5 || rBuff[0] != SZTOPS_STATION || rBuff[1] != SZTOPS_COMMAND_03)
	{
		return INVALID_TEMPERATURE;
	}

	int nTargetDataBytes = rBuff[2];
	if (nTargetDataBytes == 2)
	{
		U16 uData = *((U16*)&rBuff[3]);
		uData = PublicW32::WORDReverse(uData);
		S16 sData = (S16)uData;
		nTemperature = sData;
	}
	else if (nTargetDataBytes == 4)
	{
		U32 uData = *((U32*)&rBuff[3]);
		uData = PublicW32::ReverseDWORD(uData);
		nTemperature = (int)uData;
	}
	else
	{
		return INVALID_TEMPERATURE;
	}

	return nTemperature;
}

bool CSztopsBoxCtrl::IsRunning()
{
	m_errCode = 0;
	if (!IsReady())
	{
		return false;
	}

	CAutoLock autoLock(&m_critiLock);
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_04;

	U16 uVal = SZTOPS_GET_STATUS;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = (U16)1;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(500);
	if (m_errCode != 0)
	{
		return false;
	}

	char rBuff[30];
	memset(rBuff, 0, 30);
	int nReadBytesCnt = m_tcpComm.ReceiveData(rBuff, 30);
	Sleep(1000);

	if (nReadBytesCnt < 5 || rBuff[0] != SZTOPS_STATION || rBuff[1] != SZTOPS_COMMAND_04)
	{
		return false;
	}

	int nTargetDataBytes = rBuff[2];
	if (nTargetDataBytes == 2)
	{
		U16 uData = *((U16*)&rBuff[3]);
		uData = PublicW32::WORDReverse(uData);
		if (uData == 1)
		{
			return true;
		}
	}
	else if (nTargetDataBytes == 4)
	{
		U32 uData = *((U32*)&rBuff[3]);
		uData = PublicW32::ReverseDWORD(uData);
		if (uData == 1)
		{
			return true;
		}
	}

	return false;
}

bool CSztopsBoxCtrl::IsAlarm()
{
	return false;
}

bool CSztopsBoxCtrl::Run()
{
	m_errCode = 0;
	if (!IsReady())
	{
		return false;
	}
	CAutoLock autoLock(&m_critiLock);
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_05;

	U16 uVal = SZTOPS_CTRL_RUN;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = 0xFF00;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(2000);

	char rBuff[30];
	memset(rBuff, 0, 30);
	int nReadBytesCnt = m_tcpComm.ReceiveData(rBuff, 30);
	Sleep(1000);

	if (nReadBytesCnt < 3 || rBuff[0] != SZTOPS_STATION || rBuff[1] != SZTOPS_COMMAND_05)
	{
		return false;
	}

	return true;
}

void CSztopsBoxCtrl::Stop()
{
	m_errCode = 0;
	if (!IsReady())
	{
		return;
	}
	CAutoLock autoLock(&m_critiLock);
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_05;

	U16 uVal = SZTOPS_CTRL_STOP;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = 0xFF00;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(1000);
}

int CSztopsBoxCtrl::GetTargetTemperature()
{
	m_errCode = 0;
	if (!IsReady())
	{
		return INVALID_TEMPERATURE;
	}
	CAutoLock autoLock(&m_critiLock);
	int nTemperature = INVALID_TEMPERATURE;
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_04;

	U16 uVal = SZTOPS_GET_TEMPERATURE_SET;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = (U16)1;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(500);
	if (m_errCode != 0)
	{
		return INVALID_TEMPERATURE;
	}

	char rBuff[30];
	memset(rBuff, 0, 30);
	int nReadBytesCnt = m_tcpComm.ReceiveData(rBuff, 30);
	Sleep(1000);

	if (nReadBytesCnt < 5 || rBuff[0] != SZTOPS_STATION || rBuff[1] != SZTOPS_COMMAND_04)
	{
		return INVALID_TEMPERATURE;
	}

	int nTargetDataBytes = rBuff[2];
	if (nTargetDataBytes == 2)
	{
		U16 uData = *((U16*)&rBuff[3]);
		uData = PublicW32::WORDReverse(uData);
		S16 sData = (S16)uData;
		nTemperature = sData;
	}
	else if (nTargetDataBytes == 4)
	{
		U32 uData = *((U32*)&rBuff[3]);
		uData = PublicW32::ReverseDWORD(uData);
		nTemperature = (int)uData;
	}
	else
	{
		return INVALID_TEMPERATURE;
	}

	return nTemperature;
}

void CSztopsBoxCtrl::SetTemperatureChangeTime(int _nVal)
{
	return;
}

int CSztopsBoxCtrl::GetTemperatureChangeTime()
{
	return 0;
}

void CSztopsBoxCtrl::SetTS1(int _nVal)
{
	return;
}

int CSztopsBoxCtrl::GetTS1()
{
	return 0;
}

void CSztopsBoxCtrl::SetMode(int _nVal /*= 0*/)
{
	m_errCode = 0;
	if (!IsReady())
	{
		return;
	}
	CAutoLock autoLock(&m_critiLock);
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_06;

	U16 uVal = SZTOPS_SET_MODE;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = (U16)_nVal;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(1000);
}

int CSztopsBoxCtrl::GetMode()
{
	m_errCode = 0;
	if (!IsReady())
	{
		return INVALID_BOX_MODE;
	}
	CAutoLock autoLock(&m_critiLock);
	int nMode = INVALID_BOX_MODE;
	
	char wBuff[6] = { 0 };
	wBuff[0] = SZTOPS_STATION;
	wBuff[1] = SZTOPS_COMMAND_03;

	U16 uVal = SZTOPS_SET_MODE;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[2], 2, &uVal, 2);

	uVal = (U16)1;
	uVal = PublicW32::WORDReverse(uVal);
	memcpy_s((void*)&wBuff[4], 2, &uVal, 2);

	m_errCode = m_tcpComm.SendData(wBuff, 6);
	Sleep(500);
	if (m_errCode != 0)
	{
		return INVALID_BOX_MODE;
	}

	char rBuff[30];
	memset(rBuff, 0, 30);
	int nReadBytesCnt = m_tcpComm.ReceiveData(rBuff, 30);
	Sleep(1000);

	if (nReadBytesCnt < 5 || rBuff[0] != SZTOPS_STATION || rBuff[1] != SZTOPS_COMMAND_03)
	{
		return INVALID_BOX_MODE;
	}

	int nTargetDataBytes = rBuff[2];
	if (nTargetDataBytes == 2)
	{
		U16 uData = *((U16*)&rBuff[3]);
		uData = PublicW32::WORDReverse(uData);
		S16 sData = (S16)uData;
		nMode = sData;
	}
	else if (nTargetDataBytes == 4)
	{
		U32 uData = *((U32*)&rBuff[3]);
		uData = PublicW32::ReverseDWORD(uData);
		nMode = (int)uData;
	}
	else
	{
		return INVALID_BOX_MODE;
	}

	return nMode;
}

DWORD CSztopsBoxCtrl::GetErrCode()
{
	return m_errCode;
}

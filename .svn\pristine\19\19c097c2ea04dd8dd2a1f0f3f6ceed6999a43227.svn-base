#ifndef PUBLIC_H_
#define PUBLIC_H_

#include "Typedef.h"

#define	SECTOR_SIZE				(512)

#ifdef __cplusplus

#ifndef SAFE_DELETE 
#define SAFE_DELETE(p) { if(p) { delete (p); (p)=NULL; } } 
#endif 
#ifndef SAFE_DELETE_ARRAY 
#define SAFE_DELETE_ARRAY(p) { if(p) { delete[] (p); (p)=NULL; } } 
#endif 
#ifndef SAFE_RELEASE 
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p)=NULL; } } 
#endif 

#endif


#if ! defined(lint)

#ifndef UNREFERENCED_PARAMETER
#define UNREFERENCED_PARAMETER(P)          (P)
#endif

#ifndef DBG_UNREFERENCED_PARAMETER
#define DBG_UNREFERENCED_PARAMETER(P)      (P)
#endif

#ifndef DBG_UNREFERENCED_LOCAL_VARIABLE
#define DBG_UNREFERENCED_LOCAL_VARIABLE(V) (V)
#endif

#else // lint

// Note: lint -e530 says don't complain about uninitialized variables for
// this varible.  Error 527 has to do with unreachable code.
// -restore restores checking to the -save state

#ifndef UNREFERENCED_PARAMETER
#define UNREFERENCED_PARAMETER(P)          \
    /*lint -save -e527 -e530 */ \
    { \
    (P) = (P); \
    } \
    /*lint -restore */
#endif

#ifndef DBG_UNREFERENCED_PARAMETER
#define DBG_UNREFERENCED_PARAMETER(P)      \
    /*lint -save -e527 -e530 */ \
    { \
    (P) = (P); \
    } \
    /*lint -restore */
#endif

#ifndef DBG_UNREFERENCED_LOCAL_VARIABLE
#define DBG_UNREFERENCED_LOCAL_VARIABLE(V) \
    /*lint -save -e527 -e530 */ \
    { \
    (V) = (V); \
    } \
    /*lint -restore */
#endif

#endif // lint

//const U8 ALWAYS_TRUE_FLAG = 1;
//const U8 ALWAYS_FALSE_FLAG = 0;

#endif
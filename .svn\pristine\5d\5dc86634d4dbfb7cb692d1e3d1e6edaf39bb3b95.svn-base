import PublicFuc
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

dicPfm = {}

def Run(curpath, workBook, alignment):
    ws = workBook['BurnIn']
    ws.alignment = alignment
    ProData(curpath, ws)
    FillHTBitTable(ws)
    FillLTBitTable(ws)
    PublicFuc.WriteReportTime(ws,'D',2)
    PublicFuc.WriteReportOperator(ws,'H',2)
    
def ProData(curpath, worksheet):

    #前置条件
    #mars_h2
    #marsH2Key = ['Cap','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    #pattern = '.+\\\\Plan38\\\\T_GE_SD_C7\\\\Mars_H2_1\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_1', marsH2Key, '',0)
    #pattern = '.+\\\\Plan38\\\\T_GE_SD_C7\\\\Mars_H2_2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_2', marsH2Key, '',0)

    #mars_CopyFile
    #marsCopyFileKey = ['Cap','START_TIME','END_TIME','TEST_RESULT','RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','PowerUpCnt']
    #pattern = '.+\\\\Plan38\\\\T_GE_SD_C8\\\\Mars文件拷贝\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_CopyFile', 'MarsCopyFile', marsCopyFileKey, '',0)

    #mars_delete_删除一半文件
    #marsDeleteKey = ['Cap','TEST_RESULT','ReplaceBadBlock','PowerUpCnt','AbnormalPowerOffCnt','RemainFlashWriteCap','RemainLifePercent','AvgWearLevel']
    #pattern = '.+\\\\Plan24\\\\T_GE_SD_C53\\\\删除一半文件\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_Copy_Delete', 'MarsDelete', marsDeleteKey, '',0)

    #高低温测试结果
    #高低温BIT
    BitKey = ['Cap','pc_no','Duration','BitCycle','qa_err_msg']
    pattern = '.+\\\\Plan8\\\\T_GE_SD_C23\\\\BurnIn测试\\\\\d{14}\\\\report.ini$' #72h
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HT_BIT', BitKey, '',0)
    pattern = '.+\\\\Plan31\\\\T_GE_SD_C23\\\\BurnIn测试\\\\\d{14}\\\\report.ini$' #48h
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HT_BIT', BitKey, '',0)
    pattern = '.+\\\\Plan33\\\\T_GE_SD_C23\\\\BurnIn测试\\\\\d{14}\\\\report.ini$' #24h
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HT_BIT', BitKey, '',0)

    #低温BIT
    pattern = '.+\\\\Plan9\\\\T_GE_SD_C24\\\\BurnIn测试\\\\\d{14}\\\\report.ini$' #72h
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'LT_BIT', BitKey, '',0)
    pattern = '.+\\\\Plan32\\\\T_GE_SD_C24\\\\BurnIn测试\\\\\d{14}\\\\report.ini$' #48h
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'LT_BIT', BitKey, '',0)
    pattern = '.+\\\\Plan34\\\\T_GE_SD_C24\\\\BurnIn测试\\\\\d{14}\\\\report.ini$' #24h
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'LT_BIT', BitKey, '',0)

    #mars_校验剩下的文件
    #marsVerifyKey = ['Cap','AVERAGE_READ_VEL','TEST_RESULT','ReplaceBadBlock','PowerUpCnt','AbnormalPowerOffCnt','RemainFlashWriteCap','RemainLifePercent','AvgWearLevel']
    #pattern = '.+\\\\Plan25\\\\T_GE_SD_C54\\\\MARS文件拷贝校验\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_Verify_CF', 'MarsVerify', marsVerifyKey, '',0)

    ProTime()

#获取原先的容量
def GetDefaultCapMB(sampleDic):
    defaultCap = ''
    for caseKey in sampleDic:
        tempList = sampleDic[caseKey]
        if not isinstance(tempList,list):
            continue
        if tempList[0] != '':
            rawCap = float(tempList[0])
            if rawCap < 10000:
                defaultCap = int(rawCap*1024)
            else:
                defaultCap = int(rawCap)
            break
    return defaultCap

#合并时间项目
def ProTime():
    for sampleKey in dicPfm:
        sampleDic = dicPfm[sampleKey]
        if 'MarsCopyFile' in sampleDic:
            dic = sampleDic['MarsCopyFile']
            endtime = datetime.strptime(dic[2], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic[1], '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
            dic.pop(1)
            dic.pop(1)
            dic.insert(1,timeStr)

        for caseKey in sampleDic:
            tempList = sampleDic[caseKey]
            if not isinstance(tempList,list):
                continue
            if tempList[0] != '':
                tempList[0] = int(float(tempList[0])*1024)
            else:
                tempList[0] = GetDefaultCapMB(sampleDic)

            
            result = tempList[-1]
            if caseKey != 'HT_BIT' and caseKey != 'LT_BIT':
                result = tempList[-7]
            if result == '' or result.upper() == 'TRUE':
                if caseKey != 'HT_BIT' and caseKey != 'LT_BIT':
                    tempList[-7] = 'PASS'
                else:
                    tempList[-1] = 'PASS'
    

def FillHTBitTable(worksheet):
    keyList = ['']#无实际意义，只是为了满足下面的参数符合
    startLine = 43

    dicHTData = dicPfm.copy()
    for sampleNo in dicPfm:
        if 'HT_BIT' not in dicPfm[sampleNo]:
            del dicHTData[sampleNo]

    #marH2_1_Col = ['B','C','D','E','F','G','R','S','T','U','V','W','X','Y','Z','AC']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicHTData, 'MH2_1', marH2_1_Col, keyList)

    #marH2_2_Col = ['B','C','D','H','I','J','R','S','T','U','V','W','X','Y','Z','AC']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicHTData, 'MH2_2', marH2_2_Col, keyList)

    #marCopyfileCol = ['B','C','D','K','L','R','S','T','U','V','W','X','Y','Z','AC']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicHTData, 'MarsCopyFile', marCopyfileCol, keyList)

    #marDeletefileCol = ['B','C','O','Y','Z','AA','AB','AC','AD']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicPfm, 'MarsDelete', marDeletefileCol, keyList)

    marBurinCol = ['B','C','E','G','H','I','J']
    WriteQADataAndImageLocal(worksheet, startLine, startLine+10, dicHTData, 'HT_BIT', marBurinCol, keyList)

    #marVerifyCol = ['B','C','U','W','X','Y','Z','AA','AB','AC','AD']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicPfm, 'MarsVerify', marVerifyCol, keyList)


def FillLTBitTable(worksheet):
    keyList = ['']#无实际意义
    startLine = 63

    dicLTData = dicPfm.copy()
    for sampleNo in dicPfm:
        if 'LT_BIT' not in dicPfm[sampleNo]:
            del dicLTData[sampleNo]

    #marH2_1_Col = ['B','C','D','E','F','G','R','S','T','U','V','W','X','Y','Z','AC']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicLTData, 'MH2_1', marH2_1_Col, keyList)

    #marH2_2_Col = ['B','C','D','H','I','J','R','S','T','U','V','W','X','Y','Z','AC']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicLTData, 'MH2_2', marH2_2_Col, keyList)

    #marCopyfileCol = ['B','C','D','K','L','R','S','T','U','V','W','X','Y','Z','AC']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicLTData, 'MarsCopyFile', marCopyfileCol, keyList)

    #marDeletefileCol = ['B','C','O','Y','Z','AA','AB','AC','AD']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicPfm, 'MarsDelete', marDeletefileCol, keyList)

    marBurinCol = ['B','C','E','G','H','I','J']
    WriteQADataAndImageLocal(worksheet, startLine, startLine+10, dicLTData, 'LT_BIT', marBurinCol, keyList)

    #marVerifyCol = ['B','C','U','W','X','Y','Z','AA','AB','AC','AD']
    #WriteDataAndImageLocal(worksheet, startLine, startLine+10, dicPfm, 'MarsVerify', marVerifyCol, keyList)
    

def WriteDataAndImageLocal(worksheet, startLine, imageLine, dataDic, caseKey, colLst, keyLst, imgWidth = 260, imgHeight = 240, startCol = 2, colCnt = 3):
    curLine = startLine
    imageCol = startCol
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                if  'Cap' == keyLst[0]:
                    keyNo = '%s-%sG'%(key, line[0])
                worksheet['%s%d'%(col, curLine)] = keyNo
            elif 2 == index and caseKey != 'MarsDelete':
                if 'MMS_PC' in dataDic[key]:
                    worksheet['%s%d'%(col, curLine)] = dataDic[key]['MMS_PC']
            else:
                if index > 2:
                    worksheet['%s%d'%(col, curLine)] = line[index-2]
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1




def WriteQADataAndImageLocal(worksheet, startLine, imageLine, dataDic, caseKey, colLst, keyLst, imgWidth = 260, imgHeight = 240, startCol = 2, colCnt = 3):
    curLine = startLine
    imageCol = startCol
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                if  'Cap' == keyLst[0]:
                    keyNo = '%s-%sG'%(key, line[0])
                worksheet['%s%d'%(col, curLine)] = keyNo
            else:
                if col == colLst[-1]:
                    worksheet['%s%d'%(col, curLine)] = line[index-2]
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1
            
       
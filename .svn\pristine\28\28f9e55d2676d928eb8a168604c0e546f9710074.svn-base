// PortDlg.cpp : Defines the initialization routines for the DLL.
//

#include "stdafx.h"
#include "Port.h"
#include "PortDlg.h"
#include <MaterialNoMgrInter.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

//
//TODO: If this DLL is dynamically linked against the MFC DLLs,
//		any functions exported from this DLL which call into
//		MFC must have the AFX_MANAGE_STATE macro added at the
//		very beginning of the function.
//
//		For example:
//
//		extern "C" BOOL PASCAL EXPORT ExportedFunction()
//		{
//			AFX_MANAGE_STATE(AfxGetStaticModuleState());
//			// normal function body here
//		}
//
//		It is very important that this macro appear in each
//		function, prior to any calls into MFC.  This means that
//		it must appear as the first statement within the 
//		function, even before any object variable declarations
//		as their constructors may generate calls into the MFC
//		DLL.
//
//		Please see MFC Technical Notes 33 and 58 for additional
//		details.
//

// CPortDlgApp

BEGIN_MESSAGE_MAP(CPortDlgApp, CWinApp)
END_MESSAGE_MAP()


// CPortDlgApp construction

CPortDlgApp::CPortDlgApp()
{
	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
}


// The one and only CPortDlgApp object

CPortDlgApp theApp;


// CPortDlgApp initialization

BOOL CPortDlgApp::InitInstance()
{
	CWinApp::InitInstance();

	return TRUE;
}

#ifdef PORTDLG_EXPORTS
#define PORTDLG_API __declspec(dllexport)
#else
#define PORTDLG_API __declspec(dllimport)
#endif

extern "C" PORTDLG_API INT_PTR ShowPortDlg(YSMaterialNo::MaterialNoMgr* _pMaterialMgr)
{
	AFX_MANAGE_STATE(AfxGetStaticModuleState());
	CPortDlg dlg(_pMaterialMgr);
	return dlg.DoModal();
}
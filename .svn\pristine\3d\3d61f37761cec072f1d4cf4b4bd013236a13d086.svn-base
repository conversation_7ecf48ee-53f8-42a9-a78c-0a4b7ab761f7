﻿<!DOCTYPE html>
<html>
<head>
   <title>Auto-run applications</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Registry,Auto-run,Starting applications automatically" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "autorun.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold;"> &nbsp;Auto-run applications</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="overview.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="commandline.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="autologin.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;">This page gives a brief description of how an application can be setup to run automatically when Windows starts. This is an important part of creating a stress test to cyclically reboot a PC. There are various methods that can be used to set up an application to run automatically.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Method1 – Windows Startup folder</span></p>
<p style="margin: 7px 0px 7px 0px;">There is a “Startup” folder in the Start menu of Windows. Shortcuts to files can be placed in this directory. All the shortcuts in this folder will be executed automatically when Windows starts. One easy way to edit the contents of the startup folder is to use the Advanced view in the “Start / Settings / Task bar &amp; Start menu”, window. Short cuts can be created by dragging executable files into this Startup folder with the ALT key held down.</p>
<p style="margin: 7px 0px 7px 0px;">Right clicking on the short cut and selecting properties then allows the shortcut settings to be modified. This is the best and easiest method to auto-run applications.</p>
<p style="margin: 7px 0px 7px 0px;">See the Windows documentation for more information about short cuts.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Method2 - win.ini</span></p>
<p style="margin: 7px 0px 7px 0px;">In the windows section of this file, new lines can be added to start applications automatically. For example,</p>
<p style="margin: 7px 0px 0px 0px;">[windows]</p>
<p style="margin: 7px 0px 0px 0px;">run=&quot;C:\PROGRA~1\PERFOR~1\pt.exe&quot;</p>
<p style="margin: 7px 0px 7px 0px;">This file is found is in the Windows installation directory. Note the use of older style 8.3 files names as spaces are not allowed in the file name. This method is not recommended, as it was designed for Windows3.1 and Windows95 and is no longer used in newer versions of Windows. (It still works in Windows98 however).</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Method3 – Edit the registry</span></p>
<p style="margin: 7px 0px 7px 0px;">By placing entries into following registry key, applications can be executed automatically at startup</p>
<p style="margin: 7px 0px 7px 0px;">HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run</p>
<p style="margin: 7px 0px 7px 0px;">Don’t edit the registry unless you know what you are doing.</p>

</td></tr></table>

</body>
</html>

import PublicFuc
from copy import copy

def Run(curpath, workBook, alignment):
    ws = workBook['Platform performance']
    ws.alignment = alignment
    ProPlan4(curpath, ws)
    PublicFuc.WriteReportTime(ws,'D',1)
    PublicFuc.WriteReportOperator(ws,'H',1)

def CopyCell(source_cell, target_cell):
    target_cell.fill = copy(source_cell.fill)
    if source_cell.has_style:
        target_cell._style = copy(source_cell._style)
        target_cell.font = copy(source_cell.font)
        target_cell.border = copy(source_cell.border)
        target_cell.fill = copy(source_cell.fill)
        target_cell.number_format = copy(source_cell.number_format)
        target_cell.protection = copy(source_cell.protection)
        target_cell.alignment = copy(source_cell.alignment)
    if source_cell.value:
        target_cell.value = copy(source_cell.value)

def GetNewRmsData(oldDic):
    newDic = {}
    for key,item in sorted(oldDic.items(),key=lambda x:x[0]):
        if key not in newDic:
            newDic[key] = {}
        for line in item:
            lineData = ['','','','','']
            data1 = ['','','','']
            data2 = ['','','','']
            data3 = ['','','','']
            for cycle in item[line]:
                if 0 == cycle:
                    tempData = data1
                elif 2 == cycle:
                    tempData = data2
                else:
                    tempData = data3
                dataDic = item[line][cycle]
                lineData[0] = dataDic['cputype']
                lineData[1] = dataDic['plattype']
                lineData[2] = dataDic['platno']
                lineData[3] = dataDic['diskno']
                lineData[4] = dataDic['capacity']
                tempData[0] = dataDic['sequential read'].replace('MB/s','')
                tempData[1] = dataDic['sequential write'].replace('MB/s','')
                tempData[2] = dataDic['random read'].replace('MB/s,','\n').replace('IOPS (4KB)','')
                tempData[3] = dataDic['random write'].replace('MB/s,','\n').replace('IOPS (4KB)','')
            lineData.extend(data1)
            lineData.extend(data2)
            lineData.extend(data3)
            newDic[key][line] = lineData
    return newDic

def ProPlan4(curpath, ws):
    resultDic = {}
    cyclst = [0,2,5]
    colLst = ['B','C','E','G','M', 'O','P','Q','R','U','V','W','X','AA','AB','AC','AD']
    for cycle in cyclst:
        for count in range(1,6):
            pattern = '.+\\\\plan4\\\\T_EM_NA_C12\\\\com\.andromeda\.androbench.*_%d_%d_5\\\\\d{14}\\\\TestResult\.ini$'%(cycle, count)
            PublicFuc.ReadRMSIniData(curpath, pattern, resultDic, cycle, count)
    diskCnt = len(resultDic)
    newDic = GetNewRmsData(resultDic)
    #先动态扩展表格，生成所需格式的行数
    curRow = 11
    rowHeight = ws.row_dimensions[7].height
    mergeColLst = list(range(2,14))+[19,25]
    for disk in range(0,diskCnt-1):
        for i in range(1,6):
            curRow+=1
            ws.row_dimensions[curRow].height = rowHeight
            for j in range(1,31):
                srcCell = ws.cell(row=6+i,column=j)
                desCell = ws.cell(row=curRow,column=j)
                CopyCell(srcCell, desCell)
                if 1 == i and j in mergeColLst:
                    ws.merge_cells(start_row=curRow, start_column=j, end_row=curRow+4, end_column=j)
    #填充数据
    curRow = 6
    mergeColLst = ['B','C','E','G','M']
    for item in newDic:
        for line in newDic[item]:
            lineData = newDic[item][line]
            for index,col in enumerate(colLst):
                #合并单元格只能写第一行
                if col in mergeColLst:
                    ws['%s%d'%(col, curRow+1)] = lineData[index]
                else:
                    ws['%s%d'%(col, curRow+line)] = lineData[index]
               
        curRow += 5


   
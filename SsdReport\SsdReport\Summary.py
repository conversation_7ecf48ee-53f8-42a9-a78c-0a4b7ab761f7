import PublicFuc,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  Pat<PERSON><PERSON>ill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

dicData = {}
CheckSmartAttributeList = ['05','C3','C4','C5','C6','C7','AF','B2']

def Run(curpath, workBook, alignment):
    ws = workBook['测试报告汇总']
    ws.alignment = alignment
    ProDataReboot(ws,workBook)
    ProBitAfterReboot(ws,workBook)
    ProDataSleep(ws,workBook)
    ProBitAfterSleep(ws,workBook)
    ProReliabilityReadDisturb85C(ws,workBook)
    ProReliabilityRetention120C3Hours(ws,workBook)
    ProReliabilityRetention70C(ws,workBook)
    ProReliabilityRetention0C(ws,workBook)
    ProReliabilityMarsBurnIn85CLogicDisk(ws,workBook)
    ProReliabilityMarsBurnIn85CPhysicDisk(ws,workBook)
    ProReliabilityMarsBurnInN25CLogicDisk(ws,workBook)
    ProReliabilityMarsBurnInN25CPhysicDisk(ws,workBook)
    ProReliabilityMarsAllModePhysicDisk(ws,workBook)
    ProReliabilityJBBit91LogicDisk(ws,workBook)
    ProReliabilityAbnormalReboot(ws,workBook)
    ProReliabilityAbnormalSleep(ws,workBook)
    ProReliabilityAbnormalBit01(ws,workBook)
    ProReliabilityAbnormalBit02(ws,workBook)
    ProReliabilityAbnormalBit03(ws,workBook)
    ProReliabilityAbnormalBit04(ws,workBook)
    ProStabilityIometerTable4(ws,workBook)
    ProStabilityIometerTable5(ws,workBook)
    ProStabilityIometerTable6(ws,workBook)
    ProReliablityMarsWritePhysicDisk(ws,workBook)
    ProStablityRandLengthPhysicDisk(ws,workBook)
    ProStablityPreRead1(ws,workBook)
    ProStablityPreRead2(ws,workBook)
    ProStablityMarsBasicPor(ws,workBook)
    ProStablityMarsSpor(ws,workBook)
    ProStablityMarsPorSeqTest(ws,workBook)
    ProStablityMarsPorRandTest(ws,workBook)
    ProStablityMarsTrimSpor(ws,workBook)
    ProStablityRecycleInterupt(ws,workBook)
    ProStablityMarsTrimPhy(ws,workBook)
    ProStablityDiffOpRatio_POR_SPOR(ws,workBook)
    ProStablityFixSectionSPOR(ws,workBook)
    ProStablityIC_MaxSPOR(ws,workBook)
    ProStablityStaticSPOR(ws,workBook)
    ProStablitySporLostdateRecover(ws,workBook)
    ProStablityEnduranceSPOR(ws,workBook)
    ProReliabilityDWALogicDisk(ws,workBook)
    ProReliabilityWrite33Bit(ws,workBook)
    ProReliabilityWrite85Bit(ws,workBook)
    ProReliabilityWrite50Bit(ws,workBook)
    ProReliabilityWrite98Bit(ws,workBook)
    ProReliabilitySLCFullDiskBurnin(ws,workBook)
    ProReliabilityMonitor(ws,workBook)
    ProReliabilityIdleWriteRead(ws,workBook)
    ProReliabilitySuperVideoPlay(ws,workBook)
    #ProDataFunctionCopyFileSummary(ws,workBook)
    #ProDataSporSummary(ws,workBook)
    #ProDataPorSummary(ws,workBook)
    #ProBurinRTSummary(ws,workBook)
    #ProBurinHTSummary(ws,workBook)
    #ProBurinLTSummary(ws,workBook)
    #ProEmptyChunkSummary(ws,workBook)
    #ProEnvironmentDataRentionSummary(ws,workBook)
    #ProEnvironmentDataRention4647Summary(ws,workBook)
    #ProEnvironmentReadDisturbSummary(ws,workBook)
    #ProDataCoverageSummary(ws,workBook)
    #ProDataPfmMarsH2Summary(ws,workBook)
    #ProDataPfmThirdpartH2Summary(ws,workBook)
    #ProDataPfmCDMSummary(ws,workBook)
    #ProDataPfmHDTuneSummary(ws,workBook)
    #ProDataPfmATTOSummary(ws,workBook)
    #ProDataPfmHDBenchSummary(ws,workBook)
    #ProDataPfmIometerSummary(ws,workBook)
    #ProMPFunctionErrSummary(workBook)

#填写MP_FUNCTION中的错误样片编号信息为统计信息
def ProMPFunctionErrSummary(workBook):
    wsSummary = workBook['Full Test Reports']
    wsMPFunction = workBook['MP_Function']
    wsMPFunction['V4'] = wsSummary['L16'].value
    wsMPFunction['V6'] = wsSummary['L17'].value
    wsMPFunction['V9'] = wsSummary['L18'].value

def ProDataPfmMarsH2Summary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 19
    MIN_LINE = 37
    MAX_LINE = 41
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmThirdpartH2Summary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 19
    MIN_LINE = 62
    MAX_LINE = 66
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    wsTmp_simple = workBook['Performance-Simple']
    MIN_LINE = 33
    MAX_LINE = 37
    totalSampleCnt_simple = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp_simple)

    totalSampleCnt += totalSampleCnt_simple

    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmCDMSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 20
    MIN_LINE = 50
    MAX_LINE = 54
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    wsTmp_simple = workBook['Performance-Simple']
    MIN_LINE = 21
    MAX_LINE = 25
    totalSampleCnt_simple = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp_simple)

    totalSampleCnt += totalSampleCnt_simple
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmHDTuneSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 22
    MIN_LINE = 89
    MAX_LINE = 93
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmATTOSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 23
    MIN_LINE = 103
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmHDBenchSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 24
    MIN_LINE = 115
    MAX_LINE = 119
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmIometerSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 25
    MIN_LINE = 75
    MAX_LINE = 79
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

#获取安全的字符串
def GetSafeValidString(rawStr):
    if rawStr == None:
        return ''
    else:
        return str(rawStr)

#获取安全的整数
def GetSafeValidInteger(rawInteger):
    retVal = -1
    if rawInteger == None:
        retVal = -1
    else:
        try:
            retVal = int(rawInteger)
        except:
            retVal = -1
    return retVal

#获取测试失败的mark信息
def GetRebootSleepMarkInfoSSD(dataList,validResultList):
    failCnt = 0
    markInfo = ''
    singleSampleMarkInfo = ''
    errDic = {} #按照错误信息分类的
    for line in dataList:
        bFail = False
        for idx in range(5,len(line)):
            strResult = line[idx]
            if strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                bFail = True
                if strResult == None:
                    strResult = ''
                #记录错误样片信息,
                singleSampleMarkInfo = '['+line[0]+']:'
                singleSampleMarkInfo += 'FAIL'+':'+strResult+', '
                testTime = GetSafeValidString(line[1])
                singleSampleMarkInfo += testTime
                testCircle = GetSafeValidInteger(line[2])
                if testCircle > 0:
                    singleSampleMarkInfo += ',' + str(testCircle) + '圈'   
                markInfo += singleSampleMarkInfo+'\r\n'
                break #只记录最先遇到的错误
        if bFail:
            continue

        #如果没有错误，则继续查看样片是否测试中
        strResult = line[-1]
        if strResult == None or strResult == 'UNFINISH':
            #记录测试中样片信息,
            singleSampleMarkInfo = '['+line[0]+']:'
            testTime = GetSafeValidString(line[1])
            singleSampleMarkInfo += testTime
            testCircle = GetSafeValidInteger(line[2])
            if testCircle > 0:
                singleSampleMarkInfo += ',' + str(testCircle) + '圈'
            singleSampleMarkInfo += ',测试中...'
            markInfo += singleSampleMarkInfo+'\r\n'
    return failCnt,markInfo



def IsInCheck(smartVal):
    for checkAttribute in CheckSmartAttributeList:
        checkAttribute += '=' #加上等号比较，避免值刚好和Smart Attribute一样。
        if checkAttribute in smartVal:
            return True
    return False

def GetCheckedSmartInfo(rawSmart):
    if rawSmart == '':
        return ''
    smartList = rawSmart.split(',')
    checkedSmartInfo = ''
    for smartVal in smartList:
        smartVal = smartVal.upper()
        if IsInCheck(smartVal):
            checkedSmartInfo += smartVal
            checkedSmartInfo += ','
    if checkedSmartInfo != '':
        checkedSmartInfo = checkedSmartInfo[:-1]
    return checkedSmartInfo

#获取测试失败的mark信息
def GetMarkInfoSSD(dataList,validResultList):
    failCnt = 0
    markInfo = ''
    singleSampleMarkInfo = ''
    errDic = {} #按照错误信息分类的
    for line in dataList:
        bFail = False
        for idx in range(5,len(line)):
            strResult = line[idx]
            if strResult == None:
                continue
            if (strResult.upper() not in validResultList):
                failCnt += 1
                bFail = True
                if strResult == None:
                    strResult = ''
                #记录错误样片信息,
                singleSampleMarkInfo = '['+line[0]+']:'
                singleSampleMarkInfo += 'FAIL' + ':'+ strResult + ', '
                testTime = GetSafeValidString(line[1])
                singleSampleMarkInfo += testTime
                testCircle = GetSafeValidInteger(line[2])
                if testCircle > 0:
                    singleSampleMarkInfo += ',' + str(testCircle) + '圈'
                smartInfo = GetSafeValidString(line[3])
                if smartInfo != '':
                    singleSampleMarkInfo += ',' + GetCheckedSmartInfo(smartInfo)
                A5A6 = GetSafeValidInteger(line[4])
                if int(A5A6) > 300:
                    singleSampleMarkInfo += ',' + 'A5-A6 > 300'
                markInfo += singleSampleMarkInfo+'\r\n'
                break #只记录最先遇到的错误
        if bFail:
            continue

        #如果没有错误，则继续查看样片是否有Smart信息不达标或者A5-A6不达标的
        smartInfo = GetSafeValidString(line[3])
        smartInfo = GetCheckedSmartInfo(smartInfo)
        A5A6 = GetSafeValidInteger(line[4])
        if smartInfo != '' or A5A6 > 300:
            #记录测试中样片信息,
            singleSampleMarkInfo = '['+line[0]+']:'
            singleSampleMarkInfo += 'PASS'+ ', '
            testTime = GetSafeValidString(line[1])
            singleSampleMarkInfo += testTime
            testCircle = GetSafeValidInteger(line[2])
            if testCircle > 0:
                singleSampleMarkInfo += ',' + str(testCircle) + '圈'
            #smartInfo = GetSafeValidString(line[3])
            if smartInfo != '':
                singleSampleMarkInfo += ',' + smartInfo
            A5A6 = GetSafeValidInteger(line[4])
            if int(A5A6) > 300:
                singleSampleMarkInfo += ',' + 'A5-A6 > 300'
            markInfo += singleSampleMarkInfo+'\r\n'
        else:
            singleSampleMarkInfo = ''
            if line[0] == None:
                continue

            singleSampleMarkInfo = '['+line[0]+']:'
            singleSampleMarkInfo += 'PASS'+ ', '
            testTime = GetSafeValidString(line[1])
            singleSampleMarkInfo += testTime
            testCircle = GetSafeValidInteger(line[2])
            if testCircle > 0:
                singleSampleMarkInfo += ',' + str(testCircle) + '圈'
            markInfo += singleSampleMarkInfo+'\r\n'
    return failCnt,markInfo

#SSD的所有结果固定按照，样片编号、测试时间，测试圈数，smart信息，A6-A5，其它结果列，的方式进行填充
def ProDataReboot(ws,workBook):
    wsTmp = workBook['稳定性测试']
    MIN_LINE = 28
    MAX_LINE = 43
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','J','L','O','P'] #['C','K','J','M','P']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return

    ws['G20'] = totalSampleCnt

    #nTargetCnt = ws['F20'].value
    #nTargetCnt = int(nTargetCnt)
    nTargetCnt = 8

    strTestStatus = ''
    #通过样片数量来简单判定是否测试完毕
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #通过测试结果来细致判定是否测试完毕
        strTestStatus = '完成'
        for row in dataList:
            strTestTime = row[-1]
            if strTestTime == 'UNFINISH':
                strTestStatus = '进行'
                break        
    ws['L20'] = strTestStatus

    #统计错误样片的数量
    totalFailCnt = 0
    markInfo = ''
    validResultList = ['PASS','UNFINISH']
    totalFailCnt,markInfo = GetRebootSleepMarkInfoSSD(dataList,validResultList)
    ws['H20'] = totalFailCnt
    ws['N20'] = markInfo
    if totalFailCnt <= 0:
        ws['M20'] = 'PASS'
        return

    ws['M20'] = 'FAIL'
    

def ProBitAfterReboot(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 22
    MIN_LINE = 48
    MAX_LINE = 63
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','N','M']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProDataSleep(ws,workBook):
    wsTmp = workBook['稳定性测试']
    MIN_LINE = 68
    MAX_LINE = 83
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','J','L','O','P']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)

    if totalSampleCnt < 1:
        return
    targetLineNo = 23
    ws['G%d'%targetLineNo] = totalSampleCnt

    #nTargetCnt = ws['F%d'%targetLineNo].value
    #nTargetCnt = int(nTargetCnt)
    nTargetCnt = 8

    strTestStatus = ''
    #通过样片数量来简单判定是否测试完毕
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #通过测试结果来细致判定是否测试完毕
        strTestStatus = '完成'
        for row in dataList:
            strTestTime = row[-1]
            if strTestTime == 'UNFINISH':
                strTestStatus = '进行'
                break        
    ws['L%d'%targetLineNo] = strTestStatus

    #统计错误样片的数量
    totalFailCnt = 0
    markInfo = ''
    validResultList = ['PASS','UNFINISH']
    totalFailCnt,markInfo = GetRebootSleepMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'
    

def ProBitAfterSleep(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 25
    MIN_LINE = 88
    MAX_LINE = 103
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','N','M']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityReadDisturb85C(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 35
    MIN_LINE = 159
    MAX_LINE = 166
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','I','S','U','V']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityRetention120C3Hours(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 38
    MIN_LINE = 170
    MAX_LINE = 177
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','','','O','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityRetention70C(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 39
    MIN_LINE = 238
    MAX_LINE = 241
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','','','O','S','T']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityRetention0C(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 41
    MIN_LINE = 246
    MAX_LINE = 249
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','','','O','S','T']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityMarsBurnIn85CLogicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 39
    MIN_LINE = 35
    MAX_LINE = 42
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityMarsBurnIn85CPhysicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 40
    MIN_LINE = 43
    MAX_LINE = 50
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityMarsBurnInN25CLogicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 41
    MIN_LINE = 75
    MAX_LINE = 82
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityMarsBurnInN25CPhysicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 42
    MIN_LINE = 83
    MAX_LINE = 90
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityMarsAllModePhysicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 45
    MIN_LINE = 151
    MAX_LINE = 154
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','S','U','V']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityJBBit91LogicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 48
    MIN_LINE = 115
    MAX_LINE = 122
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityAbnormalReboot(ws,workBook):
    wsTmp = workBook['异常测试']
    targetLineNo = 49
    MIN_LINE = 17
    MAX_LINE = 20
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','J','N','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityAbnormalSleep(ws,workBook):
    wsTmp = workBook['异常测试']
    targetLineNo = 50
    MIN_LINE = 33
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','J','N','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityAbnormalBit01(ws,workBook):
    wsTmp = workBook['异常测试']
    targetLineNo = 51
    MIN_LINE = 53
    MAX_LINE = 56
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityAbnormalBit02(ws,workBook):
    wsTmp = workBook['异常测试']
    targetLineNo = 52
    MIN_LINE = 61
    MAX_LINE = 64
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityAbnormalBit03(ws,workBook):
    wsTmp = workBook['异常测试']
    targetLineNo = 53
    MIN_LINE = 69
    MAX_LINE = 72
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityAbnormalBit04(ws,workBook):
    wsTmp = workBook['异常测试']
    targetLineNo = 54
    MIN_LINE = 77
    MAX_LINE = 80
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStabilityIometerTable4(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 79
    MIN_LINE = 124
    MAX_LINE = 131
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp,2)
    colList = ['C','','','O','P','Q']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp,2)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStabilityIometerTable5(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 80
    MIN_LINE = 136
    MAX_LINE = 139
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','','','O','P','Q']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStabilityIometerTable6(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 81
    MIN_LINE = 144
    MAX_LINE = 163
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp,5)
    colList = ['C','','','O','P','Q']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp,5)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliablityMarsWritePhysicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 82
    MIN_LINE = 143
    MAX_LINE = 146
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityRandLengthPhysicDisk(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 85
    MIN_LINE = 240
    MAX_LINE = 243
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','','J','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityPreRead1(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 86
    MIN_LINE = 176
    MAX_LINE = 179
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','','J','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityPreRead2(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 87
    MIN_LINE = 184
    MAX_LINE = 187
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','','J','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityMarsBasicPor(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 88
    MIN_LINE = 192
    MAX_LINE = 195
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityMarsSpor(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 89
    MIN_LINE = 200
    MAX_LINE = 203
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityMarsPorSeqTest(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 90
    MIN_LINE = 208
    MAX_LINE = 211
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityMarsPorRandTest(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 91
    MIN_LINE = 216
    MAX_LINE = 219
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityMarsTrimSpor(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 92
    MIN_LINE = 224
    MAX_LINE = 227
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityRecycleInterupt(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 102
    MIN_LINE = 232
    MAX_LINE = 235
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityDiffOpRatio_POR_SPOR(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 93
    MIN_LINE = 248
    MAX_LINE = 251
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityFixSectionSPOR(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 94
    MIN_LINE = 256
    MAX_LINE = 259
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityIC_MaxSPOR(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 95
    MIN_LINE = 264
    MAX_LINE = 267
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityStaticSPOR(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 96
    MIN_LINE = 272
    MAX_LINE = 275
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablitySporLostdateRecover(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 97
    MIN_LINE = 296
    MAX_LINE = 299
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','BC','BD','BE','','BF']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProStablityEnduranceSPOR(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 98
    MIN_LINE = 280
    MAX_LINE = 283
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','K','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'


def ProStablityMarsTrimPhy(ws,workBook):
    wsTmp = workBook['稳定性测试']
    targetLineNo = 103
    MIN_LINE = 168
    MAX_LINE = 171
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','','J','M','N']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityDWALogicDisk(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 104
    MIN_LINE = 182
    MAX_LINE = 185
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','L','I','N','M','Q']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityWrite33Bit(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 105
    MIN_LINE = 198
    MAX_LINE = 201
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','H','M','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityWrite85Bit(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 106
    MIN_LINE = 206
    MAX_LINE = 209
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','H','M','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityWrite50Bit(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 107
    MIN_LINE = 214
    MAX_LINE = 217
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','H','M','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityWrite98Bit(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 108
    MIN_LINE = 222
    MAX_LINE = 225
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','K','H','M','Q','R']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilitySLCFullDiskBurnin(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 109
    MIN_LINE = 230
    MAX_LINE = 233
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','N','K','O','S','T']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityMonitor(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 110
    MIN_LINE = 262
    MAX_LINE = 265
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','','','P','S','T']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilityIdleWriteRead(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 111
    MIN_LINE = 270
    MAX_LINE = 273
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','H','K','T','U']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProReliabilitySuperVideoPlay(ws,workBook):
    wsTmp = workBook['可靠性测试']
    targetLineNo = 112
    MIN_LINE = 254
    MAX_LINE = 257
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
    colList = ['C','J','','L','P','Q']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['G%d'%targetLineNo] = totalSampleCnt

    #通过样片数量来简单判定是否测试完毕
    nTargetCnt = ws['F%d'%targetLineNo].value
    nTargetCnt = int(nTargetCnt)
    if totalSampleCnt < nTargetCnt:
        strTestStatus = '进行'
    else:
        #没有更多信息判定其未完成
        strTestStatus = '完成'
        
    ws['L%d'%targetLineNo] = strTestStatus

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','']
    totalFailCnt,markInfo = GetMarkInfoSSD(dataList,validResultList)
    ws['H%d'%targetLineNo] = totalFailCnt
    ws['N%d'%targetLineNo] = markInfo
    if totalFailCnt <= 0:
        ws['M%d'%targetLineNo] = 'PASS'
        return

    ws['M%d'%targetLineNo] = 'FAIL'

def ProDataFunctionCopyFileSummary(ws,workBook):
    wsTmp = workBook['MP_Function']
    targetLineNo = 18
    MIN_LINE = 16
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'L',wsTmp)
    colList = ['B','L']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataSporSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 28
    MIN_LINE = 43
    MAX_LINE = 74
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','Q','U','Y']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0 and failInfo != '':
        ws['L%d'%targetLineNo] = failInfo #测试中
        return
    if totalFailCnt <= 0 and failInfo == '':
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataPorSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 27
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','Q','U','Y']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0 and failInfo != '':
        ws['L%d'%targetLineNo] = failInfo #测试中
        return
    if totalFailCnt <= 0 and failInfo == '':
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#返回失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果
def GetFailInfoOldVersion(dataList,validResultList):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo

#返回失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果
#errRuleDic内容示例,errTypeDic = {'写失败':['写失败'],'读失败':['读失败'],'校验失败':['VERIFY','KB','GB','MB']}
def GetConlusionErrTypeName(errTypeDic,errMsg):
    UpCaseErrMsg = errMsg.upper()
    errTypeName = errMsg
    for errType in errTypeDic:
        for str in errTypeDic[errType]:
            if UpCaseErrMsg.find(str.upper()) != -1:
                errTypeName = errType #找到，代表属于此类错误
                return errTypeName
    return errTypeName

def GetFailInfoString(errDic):
    failInfo = ''
    for errType in errDic:
        failInfo += '[' + errType + ']' + ':' + str(errDic[errType]) + ','
    if failInfo != '':
        failInfo = failInfo[:-1]

    return failInfo

def GetFailInfo(dataList,validResultList,errTypeDic = {}):
    failCnt = 0
    failInfo = ''
    errDic = {} #按照错误信息分类的
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == 'UNFINISHED':
                strResult = '测试中'
                errTypeName = GetConlusionErrTypeName(errTypeDic,strResult)
                if errTypeName in errDic:
                    errDic[errTypeName] = errDic[errTypeName] + 1
                else:
                    errDic[errTypeName] = 1
                break #只记录最先遇到的错误
            elif strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                errTypeName = GetConlusionErrTypeName(errTypeDic,strResult)
                if errTypeName in errDic:
                    errDic[errTypeName] = errDic[errTypeName] + 1
                else:
                    errDic[errTypeName] = 1
                break #只记录最先遇到的错误
    failInfo = GetFailInfoString(errDic)
    return failCnt,failInfo

#返回量产失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果（因为量产结果列要么填的是容量要么填的是错误码，比较特殊。单独写函数处理）
def GetFailInfoByRexOldVersion(dataList,rexExpression):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (not re.match(rexExpression,strResult)):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo

#返回量产失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果（因为量产结果列要么填的是容量要么填的是错误码，比较特殊。单独写函数处理）
def GetFailInfoByRex(dataList,rexExpression,errTypeDic = {}):
    failCnt = 0
    failInfo = ''
    errDic = {}
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (not re.match(rexExpression,strResult)):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                errTypeName = GetConlusionErrTypeName(errTypeDic,strResult)
                if errTypeName in errDic:
                    errDic[errTypeName] = errDic[errTypeName] + 1
                else:
                    errDic[errTypeName] = 1
              
                break #只记录最先遇到的错误
    failInfo = GetFailInfoString(errDic)
    return failCnt,failInfo

def ProDataCoverageSummary(ws,workBook):
    wsTmp = workBook['Data coverage']

    targetLineNo = 41
    MIN_LINE = 5
    MAX_LINE = 12
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentDataRentionSummary(ws,workBook):
    wsTmp = workBook['Environment Test']
    targetLineNo = 36
    MIN_LINE = 7
    MAX_LINE = 46
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','W']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentDataRention4647Summary(ws,workBook):
    wsTmp = workBook['Environment Test']
    #100°的retention结果
    targetLineNo = 37
    MIN_LINE = 51
    MAX_LINE = 90
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','W']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentReadDisturbSummary(ws,workBook):
    wsTmp = workBook['Environment Test']

    targetLineNo = 38
    MIN_LINE = 51
    MAX_LINE = 66
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo


def ProEmptyChunkSummary(ws,workBook):
    wsTmp = workBook['Empty chunk']

    targetLineNo = 34
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H','J','O']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def GetSpecialDataCnt(_startRow,_endRow,_colName,ws,step = 1):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow,_endRow+1,step):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt



#BIT测试数据汇总包括：RT
def ProBurinRTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取RT BIT汇总数据
    targetLineNo = 29
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','L','P','S','V','AI']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0 and failInfo != '':
        ws['L%d'%targetLineNo] = failInfo #测试中
        return
    if totalFailCnt <= 0 and failInfo == '':
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#BIT测试数据汇总包括：HT
def ProBurinHTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取HT BIT汇总数据
    targetLineNo = 30
    MIN_LINE = 43
    MAX_LINE = 58
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I']#,'AF' 未填写，暂时不适用
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#BIT测试数据汇总包括：LT
def ProBurinLTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取HT BIT汇总数据
    targetLineNo = 31
    MIN_LINE = 63
    MAX_LINE = 78
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I'] #,'AF' 未填写，暂时不适用
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo


#获取指定范围的数据内容，结果数据是列表形式
def GetSpecialDataList(_startRow,_endRow,_colName,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            dic.append(celValue)
    return dic


#获取最长测试时间
def GetMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        timestr = data
        if timestr != '' and timestr != None:
            if timestr.find(':') != -1:
                #00:00:00
                timedata = timestr.split(':')
                totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond
            else:
                #000h 03m 07s
                timedata = timestr.split(' ')
                hour = timedata[0][0:-1]
                minutes = timedata[1][0:-1]
                seconds = timedata[2][0:-1]
                totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow,_endRow,_colNameList,ws,step = 1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1,step):
        oneRow = []
        for _colName in _colNameList:
            if _colName == '':
                oneRow.append('')
            else:
                celPosSample = '%s%d'%(_colName, rowNo)
                celValue = ws[celPosSample].value
                oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic

#判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False
    
    isValid = False
    for idx in range(1,len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid
  
#获取最长测试时间
def GetCombinedMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        if len(data) == 1:
            #简单时间信息
            timestr = data
            if timestr != '' and timestr != None:
                if timestr.find(':') != -1:
                    #00:00:00
                    timedata = timestr.split(':')
                    totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
                else:
                    #000h 03m 07s
                    timedata = timestr.split(' ')
                    hour = timedata[0][0:-1]
                    minutes = timedata[1][0:-1]
                    seconds = timedata[2][0:-1]
                    totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
        else:
            oneRowTimeList = data
            perRowTotalTime = 0
            for perTime in oneRowTimeList:
                if perTime != '' and perTime != None:
                    timestr = perTime
                    if timestr.find(':') != -1:
                        #00:00:00
                        timedata = timestr.split(':')
                        totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                        perRowTotalTime += totalSecond
                    else:
                        #000h 03m 07s
                        timedata = timestr.split(' ')
                        hour = timedata[0][0:-1]
                        minutes = timedata[1][0:-1]
                        seconds = timedata[2][0:-1]
                        totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                        perRowTotalTime += totalSecond
            
            if perRowTotalTime > maxTimeInSeconds:
                            maxTimeInSeconds = perRowTotalTime


    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#从字符中直接解析出时间的值
def GetTimeValue(dic):
    timeValue = 0
    endTimeStr = dic[2]
    startTimeStr = dic[1]
    if '' == endTimeStr or '' == startTimeStr:
        timeValue = 0
    else:
        endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
        starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
        totalSecond = timedelta.total_seconds(endtime-starttime)
        timeValue = totalSecond
    return timeValue
         

#获取开始和结束的时间
def GetTotalTimeStr():
    #测试时间
    strTime = ''
    totalTimeValue = 0
    for key in dicData:
        childDic = dicData[key]
        if 'MPTOOL' not in childDic:
            continue
        dic = childDic['MPTOOL']
        totalTimeValue += GetTimeValue(dic)

    totalSecond = int(totalTimeValue)
    hour = int(totalSecond/3600)
    lefSeconds = totalSecond%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
        
    return strTime

   


def DrawTable(ws):
    totalRowCnt = len(dicData)
    STARTLINE = 13
    #serialNo = 1
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        for ColNo in range(1,1+5):
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
    
    #合并单元格
    if totalRowCnt > 0:
        ws['%s%d'%(get_column_letter(1), STARTLINE)] = '48H高格'
        ws.merge_cells(start_row=STARTLINE, start_column=1, end_row=STARTLINE+totalRowCnt-1, end_column=1)

import PublicFuc
from openpyxl.utils import get_column_letter
import configparser
import csv,time
import os,re
import openpyxl
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta

dicPlan10 = {}

def Run(curpath, workBook, wbCompetition,alignment):
    ws = workBook['Card reader']
    ws.alignment = alignment
    ProData(curpath, ws,wbCompetition)
    WriteData(ws)
    PublicFuc.WriteReportTime(ws,'G',2)
    PublicFuc.WriteReportOperator(ws,'J',2)

def ProData(curpath, worksheet,wbCompetition):
    #step1&step2
    #第一次格式化，获取报表第1和第2行需要的数据
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\格式化_1\\\\.+.csv$'
    rawFormatList_1 = [] #第1次格式化原始数据
    ReadRawCsvData(curpath,pattern,rawFormatList_1)
    InitFormat1RawDataMap(dicPlan10,rawFormatList_1,'FORMAT_1')

    #step3
    #H2血2000M数据。
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\H2\\\\.+.csv$'
    rawH2List = [] #H2原始数据
    ReadRawCsvData(curpath,pattern,rawH2List)
    InitH2RawDataMap(dicPlan10,rawH2List,'H2')

    #step4
    #第二次格式化，并识别TF卡信息是否正确。实际上就简单以格式化是否成功作为卡信息是否正确的标准
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\格式化_2\\\\.+.csv$'
    rawFormatList_2 = [] #第2次格式化原始数据
    ReadRawCsvData(curpath,pattern,rawFormatList_2)
    InitFormatRawDataMap(dicPlan10,rawFormatList_2,'FORMAT_2','ANY') #ANY代表格式化成任何文件系统，只要存在文件系统就认为OK。

    #sep5
    #文件拷贝
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\拷贝文件\\\\DUTTest.+.csv$'
    rawCopyfile = [] #文件拷贝
    ReadRawCsvData(curpath,pattern,rawCopyfile)
    InitCopyFileRawDataMap(dicPlan10,rawCopyfile,'COPYFILE')

    #step6 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_1\\\\.+.csv$'
    CapCheckList_1 = [] #第1次容量检测，即检测卡的状态
    ReadRawCsvData(curpath,pattern,CapCheckList_1)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_1,'CHECK_1')

    #step7 校验步骤5拷贝的文件
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\校验文件\\\\.+.csv$'
    verifyList = [] 
    ReadRawCsvData(curpath,pattern,verifyList)
    InitCopyFileRawDataMap(dicPlan10,verifyList,'VERIFY')

    #step8 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_2\\\\.+.csv$'
    CapCheckList_2 = [] 
    ReadRawCsvData(curpath,pattern,CapCheckList_2)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_2,'CHECK_2')

    #step9 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_3\\\\.+.csv$'
    CapCheckList_3 = [] 
    ReadRawCsvData(curpath,pattern,CapCheckList_3)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_3,'CHECK_3')

    #step10 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_4\\\\.+.csv$'
    CapCheckList_4 = [] 
    ReadRawCsvData(curpath,pattern,CapCheckList_4)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_4,'CHECK_4')

    #step11 格式化成FAT32
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\格式化FAT32\\\\.+.csv$'
    formatFAT32List = [] 
    ReadRawCsvData(curpath,pattern,formatFAT32List)
    InitFormatRawDataMap(dicPlan10,formatFAT32List,'FORMAT_FAT32','FATSYS')

    #step12 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_5\\\\.+.csv$'
    CapCheckList_5 = [] 
    ReadRawCsvData(curpath,pattern,CapCheckList_5)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_5,'CHECK_5')

    #step13 格式化NTFS
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\格式化NTFS\\\\.+.csv$'
    formatNTFSList = [] 
    ReadRawCsvData(curpath,pattern,formatNTFSList)
    InitFormatRawDataMap(dicPlan10,formatNTFSList,'FORMAT_NTFS','NTFS')

    #step14 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_6\\\\.+.csv$'
    CapCheckList_6 = [] 
    ReadRawCsvData(curpath,pattern,CapCheckList_6)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_6,'CHECK_6')

    #step15 格式化EXFAT
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\格式化EXFAT\\\\.+.csv$'
    formatEXFATSList = [] 
    ReadRawCsvData(curpath,pattern,formatEXFATSList)
    InitFormatRawDataMap(dicPlan10,formatEXFATSList,'FORMAT_EXFAT','exFAT')

    #step16 热插拔读卡器识别TF卡信息
    pattern = '.+\\\\Plan10\\\\T_GE_SD_C35\\\\识别TF卡信息_7\\\\.+.csv$'
    CapCheckList_7 = [] 
    ReadRawCsvData(curpath,pattern,CapCheckList_7)
    InitCapCheckRawDataMap(dicPlan10,CapCheckList_7,'CHECK_7')

    #打开竞品数据源获取竞品数据
    if wbCompetition != None:
        InitCompetitionData(wbCompetition)
        

def InitCompetitionData(wb):
    wsCarder = wb['Card reader(读卡器)']
    for sampleNo in dicPlan10:
        dicCurSmapleNo = dicPlan10[sampleNo]
        if 'COMPETITION_DATA' not in dicCurSmapleNo:
            if 'FORMAT_1' in dicCurSmapleNo:
                cardNo = dicCurSmapleNo['FORMAT_1'][-1]
                compData = findCompetitionData(cardNo,wsCarder)
                dicCurSmapleNo['COMPETITION_DATA'] = compData
        

#寻找对于的竞品数据
def findCompetitionData(sampleNo,ws):
    competitionData = ''
    targetSampleNo = sampleNo.upper()
    targetSampleNo = RemoveSampleNoTailPart(targetSampleNo)
    for columnNo in range(5,ws.max_column):
        curSampleNo = ws['%s%d'%(get_column_letter(columnNo), 16)].value
        if curSampleNo != None:
            curSampleNo = curSampleNo.upper()
            curSampleNo = RemoveSampleNoTailPart(curSampleNo)
        if curSampleNo == targetSampleNo:
            competitionData = ws['%s%d'%(get_column_letter(columnNo), 20)].value #只取H2数据
            if competitionData == None:
                competitionData = ''
            break
    return competitionData

#去掉尾巴的子编号
def RemoveSampleNoTailPart(sampleNo):
    resultNo = sampleNo
    indexPos = sampleNo.rfind('-')
    if indexPos != -1:
        resultNo = resultNo[0:indexPos]
    return resultNo
    

#获取第一次格式化后数据的字典
def InitFormat1RawDataMap(h2Dic,rawDataList,caseName):
    for row in rawDataList:
        tmpRow = ['']*3 #第一个25读的容量，第二个格式化结果成功或者失败,第三个是读卡器物料编号
        tmpRow[0] = row[12]
        tmpRow[1] = row[10]
        tmpRow[2] = GetCardNo(row[9]) #依赖物料
        if row[3] not in h2Dic:
            h2Dic[row[3]] = {}
        h2Dic[row[3]][caseName] = tmpRow

#从依赖物料中解析出读卡器编号
def GetCardNo(rawNo):
    cardNo = ''
    idx = rawNo.find('CardReader')
    if idx == -1:
        return cardNo
    idxStart = idx + 11

    idxEnd = rawNo.find('$',idx)
    if idx == -1:
        return cardNo

    cardNo = rawNo[idxStart:idxEnd]
    return cardNo



#获取格式化数据的字典的通用函数，比第格式化多了一个比对文件类型的参数，‘ANY'则任何文件类型都可
def InitFormatRawDataMap(h2Dic,rawDataList,caseName,filesystem):
    for row in rawDataList:
        tmpRow = ['']*1 #只进行成功与否的判定
        strDiskFileSystem = row[11].upper()
        strResult = row[10].upper()
        finalResult = 'FAIL'
        if filesystem == 'ANY':
            if strDiskFileSystem != '' and strResult == 'PASS':#不匹配文件系统
                finalResult = 'PASS'
        else:
            if strDiskFileSystem == filesystem.upper() and strResult == 'PASS':#匹配文件系统
                finalResult = 'PASS'
        tmpRow[0] = finalResult
        if row[3] not in h2Dic:
            h2Dic[row[3]] = {}
        h2Dic[row[3]][caseName] = tmpRow

#获取copyfile结果数据的字典
def InitCopyFileRawDataMap(h2Dic,rawDataList,caseName):
    for row in rawDataList:
        tmpRow = ['']*1 
        tmpRow[0] = row[10]
        if row[3] not in h2Dic:
            h2Dic[row[3]] = {}
        h2Dic[row[3]][caseName] = tmpRow

#获取容量检测数据的字典
def InitCapCheckRawDataMap(h2Dic,rawDataList,caseName):
    for row in rawDataList:
        tmpRow = ['']*1 
        strResult = row[14].upper()
        strSysTotalCap = row[11]
        finalResult = 'FAIL'
        if strResult == 'PASS' and strSysTotalCap != '' and strSysTotalCap != '0M':
            finalResult = 'PASS'#这些条件才能判定通过
        tmpRow[0] = finalResult
        if row[3] not in h2Dic:
            h2Dic[row[3]] = {}
        h2Dic[row[3]][caseName] = tmpRow


#获取H2数据的字典
def InitH2RawDataMap(h2Dic,rawDataList,caseName):
    for row in rawDataList:
        tmpRow = ['']*(3) #读写速度和测试结果
        tmpRow[0] = row[15] #读速度
        tmpRow[1] = row[14] #写速度
        tmpRow[2] = row[17] #测试结果
        if row[3] not in h2Dic:
            h2Dic[row[3]] = {}
        h2Dic[row[3]][caseName] = tmpRow

def ReadRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                dataDic.append(row)

def WriteData(worksheet):  
    for sampleNo in dicPlan10:
        startLine = 11 #编号行
        sampleDic = dicPlan10[sampleNo]
        if 'FORMAT_1' not in sampleDic:
            continue       
        rawCardNo = sampleDic['FORMAT_1'][-1] #读卡器编号放在最后1位
        columnNo = GetTargetColumnNo(worksheet,rawCardNo)
        if columnNo == -1:
            continue #没有合格的位置，说明是不关心的读卡器测试的结果，不进行统计
        
        #写编号
        curLine = startLine
        worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleNo
        curLine += 1
        #写刚插上卡的容量信息
        worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['FORMAT_1'][0].upper()
        curLine += 1
        worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['FORMAT_1'][1].upper()
        curLine += 1
        if sampleDic['FORMAT_1'][1].upper() != 'PASS':
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        #写H2信息
        if 'H2' in sampleDic:
            speedInfo = sampleDic['H2'][1] +'/'+ sampleDic['H2'][0]
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = speedInfo
            if sampleDic['H2'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1
        
        if 'FORMAT_2' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['FORMAT_2'][-1].upper()
            if sampleDic['FORMAT_2'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'COPYFILE' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['COPYFILE'][-1].upper()
            if sampleDic['COPYFILE'][-1].upper() != 'PASS':
               worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'CHECK_1' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_1'][-1].upper()
            if sampleDic['CHECK_1'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'VERIFY' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['VERIFY'][-1].upper()
            if sampleDic['VERIFY'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 2 #中间空了一行

        if 'CHECK_2' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_2'][-1].upper()
            if sampleDic['CHECK_2'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'CHECK_3' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_3'][-1].upper()
            if sampleDic['CHECK_3'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'CHECK_4' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_4'][-1].upper()
            if sampleDic['CHECK_4'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 2

        if 'FORMAT_FAT32' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['FORMAT_FAT32'][-1].upper()
            if sampleDic['FORMAT_FAT32'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'CHECK_5' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_5'][-1].upper()
            if sampleDic['CHECK_5'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'FORMAT_NTFS' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['FORMAT_NTFS'][-1].upper()
            if sampleDic['FORMAT_NTFS'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'CHECK_6' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_6'][-1].upper()
            if sampleDic['CHECK_6'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'FORMAT_EXFAT' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['FORMAT_EXFAT'][-1].upper()
            if sampleDic['FORMAT_EXFAT'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1

        if 'CHECK_7' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['CHECK_7'][-1].upper()
            if sampleDic['CHECK_7'][-1].upper() != 'PASS':
                worksheet['%s%d'%(get_column_letter(columnNo), curLine)].fill = PublicFuc.warnFill
        curLine += 1
        if 'COMPETITION_DATA' in sampleDic:
            worksheet['%s%d'%(get_column_letter(columnNo), curLine)] = sampleDic['COMPETITION_DATA']

#查找应该写在哪一列
def GetTargetColumnNo(worksheet,cardNo):
    columnNo = -1 #无效列
    for i in range(5,worksheet.max_column):
        orgCardNo = worksheet['%s%d'%(get_column_letter(i), 10)].value
        if orgCardNo == None:
            continue
        orgCardNo = orgCardNo.strip().upper()
        if cardNo.upper() == orgCardNo:
            columnNo = i
            break

    return columnNo
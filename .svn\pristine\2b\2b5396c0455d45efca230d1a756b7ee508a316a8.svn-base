﻿<!DOCTYPE html>
<html>
<head>
   <title>Introduction and Overview</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Introduction to Sleeper,Overview of Sleeper" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "hid_overview.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="page-break-after: avoid; margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold; color: #ffffff;">Sleeper by PassMark™ Software - Overview</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="hid_overview.htm"><img src="nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     
     <a href="hid_sleepstates.htm"><img src="nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Sleeper is a small utility program developed by PassMark Software to help automate the PC hardware testing process. It has been designed to work with PassMark BurnInTest but will also work with other 3rd party applications. Sleeper allows you to,</span></p>
<p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 13px; margin: 7px 0px 7px 0px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#010100;display:inline-block;width:13px;margin-left:-13px">&#8226;</span><span style="color: #010100;">Put a PC into any sleep state supported by the system for a specified period of time.</span></p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 13px; margin: 7px 0px 7px 0px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#010100;display:inline-block;width:13px;margin-left:-13px">&#8226;</span><span style="color: #010100;">Put a PC to sleep from the command line</span></p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 13px; margin: 7px 0px 7px 0px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#010100;display:inline-block;width:13px;margin-left:-13px">&#8226;</span><span style="color: #010100;">Force the PC into the requested sleep state.</span></p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 13px; margin: 7px 0px 7px 0px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#010100;display:inline-block;width:13px;margin-left:-13px">&#8226;</span><span style="color: #010100;">Cycle through all sleep states or just all states supported by the system.</span></p><p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">For more information click on one of the following topics.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_sleepstates.htm" class="topiclink">Types of sleep states</a>.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_ui.htm" class="topiclink">The User Interface</a></span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_config.htm" class="topiclink">Configuration Options</a></span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_commandline.htm" class="topiclink">Command line options</a></span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_copyright.htm" class="topiclink">Copyright and license information</a></span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_systemreq.htm" class="topiclink">System requirements</a></span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;"><a href="hid_contacts.htm" class="topiclink">Contacting PassMark Software</a></span></p>

</td></tr></table>

</body>
</html>

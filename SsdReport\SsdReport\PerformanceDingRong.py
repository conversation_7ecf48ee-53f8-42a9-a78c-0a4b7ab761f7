import PublicFuc
from openpyxl.drawing.image import Image
import csv
import matplotlib.pyplot as plt
import tempfile,os,re
from openpyxl.utils import get_column_letter
from datetime import datetime,timedelta
import configparser
config = configparser.RawConfigParser()

def Run(curpath, workBook, alignment):
    ws = workBook['定容85%_性能测试']
    ws.alignment = alignment
    proMars(curpath, ws)
    ProCdm(curpath, ws)  
    ProAssd(curpath, ws)
    ProAtto(curpath, ws)
    proHdtune(curpath, ws)
    proHdtuneFileBase(curpath, ws)
    PublicFuc.WriteReportTime(ws,'G',2)
    PublicFuc.WriteReportOperator(ws,'L',2)

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result']) 
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        #newDic[key].append('%d/%d'%(write,read))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        #if '' == dic['id_a5'] or '' == dic['id_a6']:
        #    newDic[key].append('')
        #else:
        #    newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def proMarsCommon(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def ReadOverWriteReadCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue

        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName in dataDic:
            continue
       
        dataDic[keyName] = {}
        dataDic[keyName]['30'] = ['','',''] #对应1,2,3次的结果
        dataDic[keyName]['50'] = ['','','']
        dataDic[keyName]['85'] = ['','','']
        dataDic[keyName]['100'] = ['','','']

        pos = file.rfind('\\')
        imagepath = file[:pos]
        pos = imagepath.rfind('\\')
        imagepath = imagepath[:pos]

        #得到对应的csv文件路径
        rptIni = imagepath + "\\writeSpeed.csv"
        if not os.path.exists(rptIni):
            continue

        raw_file_data = []
        with open(rptIni,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                raw_file_data.append(row)

        GetSpecifiedData(dataDic[keyName],raw_file_data,'30')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'50')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'85')
        GetSpecifiedData(dataDic[keyName],raw_file_data,'100')       

#获取单个数据的内容
def GetSpecifiedData(dataDic,raw_file_data,caseName):
    for i in range(len(raw_file_data)):
        try:
            curRow = raw_file_data[i]
            if curRow[0] == 'Cycle':
                continue
        
            percentName = str(curRow[1])
            if percentName != caseName:
                continue
 
            nCircle = int(curRow[0])
            fValue = float(curRow[2])
            dataDic[percentName][nCircle-1] = fValue
        except:
            continue

#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteOverWriteReadData(worksheet, startLine,caseName,dataDic, colLst):
    filledSampleNo = []
    curLine = startLine
    for i in range(2):
        curTxt = worksheet['%s%d'%('C', curLine)].value
        filledSampleNo.append(curTxt)
        curLine += 1

    for key in dataDic:
        if key not in filledSampleNo:
            continue
        if caseName not in dataDic[key]:
            continue
        curLine = startLine + filledSampleNo.index(key) #得到数据要填写的行数
        line = dataDic[key][caseName]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    speedVal = line[index-1]
                    if speedVal != '':
                        speedVal = round(speedVal,2)
                    worksheet['%s%d'%(col, curLine)] = speedVal
                worksheet['%s%d'%(col, curLine)].alignment = PublicFuc.alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue

def ProOverWriteReadData(curpath, worksheet, pattern,startLine):
    dataCsv = {}
    ReadOverWriteReadCsvData(curpath,pattern,dataCsv)
    colLst = ['C','G','H','I']
    WriteOverWriteReadData(worksheet, startLine,'30', dataCsv, colLst)
    colLst = ['C','J','K','L']
    WriteOverWriteReadData(worksheet, startLine,'50', dataCsv, colLst)
    colLst = ['C','M','N','O']
    WriteOverWriteReadData(worksheet, startLine,'85', dataCsv, colLst)
    colLst = ['C','P','Q','R']
    WriteOverWriteReadData(worksheet, startLine,'100', dataCsv, colLst)

def proMars(curpath, worksheet):
    #先写常规数据
    pattern = '.+\\\\Plan88\\\\T-SS-SS-C67\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'OverWriteRead'
    startLine = 105
    keyLst = ['pc_no','cap','result','SmartInfo','runtime']
    colLst = ['C','B','E','U','S','T']
    proMarsCommon(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst)  
    #再写独特的数据
    ProOverWriteReadData(curpath, worksheet, pattern,startLine)

def ProCdm(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'P','G','H','I','J','K','L','M','N']
    cdmDic = {}
    pattern = '.+\\\\Plan79\\\\T-SS-SS-A17\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 16
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAssd(curpath, worksheet):   
    assdKey = ['pc_no', 'Cap', 'qa_err_msg','Seq Read','Seq Write','4K Read','4K Write','4K 64Thrd Read','4K 64Thrd Write','Read Acc Time','Write Acc Time','Read Score','Write Score','Total Score']
    assdCol = ['C','B','E','S','G','H','I','J','K','L','M','N','O','P','Q']
    assdDic = {}
    pattern = '.+\\\\Plan79\\\\T-SS-SS-A18\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 41
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)
    
def ProAtto(curpath, worksheet):
    attoKey = ['pc_no', 'Cap', 'qa_err_msg','64 MB_Write','64 MB_Read']
    attoCol = ['C','B','E','M','G','I']
    attoDic = {}
    pattern = '.+\\\\Plan79\\\\T-SS-SS-A19\\\\ATTO Disk Benchmark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, attoDic, attoKey, 'ATTO.bmp')
    PublicFuc.GetMaxOrMinValueLst(attoKey, attoDic)
    startLine = 66
    imgWidth = 250
    imgHeight = 330
    PublicFuc.WriteDataAndImage(worksheet, startLine, attoDic, attoCol, attoKey, imgWidth, imgHeight)

def WriteDataAndImageOfHdtune(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            curLine += 1
            # hdtune列表最后两项是图片路径(读和写)
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+1
        imageLine += 1

def WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    imageCol = 1
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                 #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 8

def proHdtune(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','Q']
    readDic = {}
    pattern = '.+\\\\Plan79\\\\T-SS-SS-A20\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)

    startLine = 91
    imgWidth = 450
    imgHeight = 350
    WriteDataAndImageOfHdtune(worksheet, startLine, readDic, hdtuneCol, imgWidth, imgHeight)

def proHdtuneFileBase(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','sequential read speed','sequential write speed','4kb read speed','4kb write speed','4kb queue read speed','4kb queue write speed','data mode','file length']
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','P','Q','R']
    dataDic = {}
    pattern = '.+\\\\Plan79\\\\T-SS-SS-C46\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, hdtuneKey, 'HDTune.bmp', 1)

    for keyNo in dataDic:
        keySet = dataDic[keyNo]
        for line in keySet:
            if line[2] == '':
                line[2] = 'PASS'

    imgWidth = 500
    imgHeight = 500
    #WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)
    # hdtune满盘写
    startLine = 98
    WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, hdtuneCol, imgWidth, imgHeight)

def GetImagePath(strCsvFile, key):
    strPath = ''
    dataLst = []
    with open(strCsvFile, 'r', errors='ignore') as f:
        rowLst = list(csv.reader(f))
        for line in rowLst:
            if len(line) >= 14 and 'WORKER' == line[1]:
                dataLst.append(float(line[13]))
    if [] != dataLst:
        plt.figure(figsize=(13,5))
        plt.title('%s  Phy_Seq_1M_2H(MB/s)'%key)
        xLst = [x for x in range(len(dataLst))]
        plt.plot(xLst, dataLst)
        ax = plt.gca()
        ax.xaxis.set_major_locator(plt.MultipleLocator(120))
        plt.xticks(rotation=90)
        ax.yaxis.set_major_locator(plt.MultipleLocator(50))
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_iometer.png'%key)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()  
    return strPath

def proIometer(curpath, worksheet):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E','R', 'G','I','K','L','M','P']
    imtKeyLst = ['pc_no', 'Cap', 'qa_err_msg', 'Seq_1M_2H_Iops', 'Seq_1M_2H_MiBps']
    pattern = '.+\\\\Plan65\\\\T-SS-SS-A23\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'IOmeter.bmp', 1)

    #plan65合并到了plan31中，plan65暂时保留，因此两个地方的结果都需要统计
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A23\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'IOmeter.bmp', 1)

    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey, True)
    newKey = imtKeyLst+smartKeyNew
    startLine = 113
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)
    startLine += 2
    imgWidth = 1000
    imgHeight = 320
    for key in newDic:
        for line in newDic[key]:
            strPath = GetImagePath(line[-1], key)
            if '' != strPath:
                img = Image(strPath)
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, 'G%d'%startLine)
                startLine += 1


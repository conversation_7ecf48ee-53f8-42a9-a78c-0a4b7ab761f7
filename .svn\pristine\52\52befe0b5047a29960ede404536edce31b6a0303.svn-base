﻿<!DOCTYPE html>
<html><head>
   <title>Rebooter by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />

   <!-- This line includes the general project style sheet (not required) -->
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- This block defines the styles of the TOC headings, change them as needed -->
   <style type="text/css">
       body { background:#FFF; }
       .navbar   { font-size: 120%; }

       .heading1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }

       .hilight1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }

       /* TOC LIST CSS */
       #toc    { padding: 0; margin: 0; }
       #toc li { margin-top: 2px; }
       #toc ul { padding-left:0; padding-right:0 }
       /* TOC LIST CSS */
   </style>
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript">
     var parentScope = (parent.hmNavigationFrame);
     if (!parentScope) {
		      var s = document.createElement("script");
		      s.setAttribute("type","text/javascript");
		      s.setAttribute("src", "helpman_navigation.js");
		      document.getElementsByTagName("head")[0].appendChild(s);
	    }
     else {
       if (initialtocstate != "expandall") parent.hmAddCss(document, "#toc li ul { display: none }");
     }
     function loadicons() { var icons = new Array(); for (i=0; i<arguments.length; i++) { icons[i] = new Image(); icons[i].src = arguments[i]; } }
     function loadtoc() { if (parentScope) parent.loadstate(document.getElementById("toc")); else loadstate(document.getElementById("toc")); }
     function savetoc() { if (parentScope) parent.savestate(document.getElementById("toc")); else savestate(document.getElementById("toc")); }
     function clicked(node, event) { deselect(); if (parentScope) parent.hmNodeClicked(node, event); else hmNodeClicked(node, event); }
     function dblclicked(node) { if (parentScope) parent.hmNodeDblclicked(node); else hmNodeDblclicked(node); }
     function deselect() { if (window.getSelection) window.getSelection().removeAllRanges(); else if (document.selection) document.selection.empty(); }
     $(document).ready(function(){
       loadtoc();
       $(window).onunload = savetoc;
     });
   </script>
</head>
<body>
<p class="navbar"><b>Contents</b>
 | <a href="hmkwindex.htm">Index</a>
 | <a href="hmftsearch.htm">Search</a>
</p>
<hr/>


  <!-- Place holder for the TOC - this variable is REQUIRED! -->
  <ul id="toc" style="list-style-type:none;display:block;padding-left:0">
<li class="heading1" id="i1" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a1" href="overview.htm" target="hmcontent"><span class="heading1" id="s1">Introduction and Overview</span></a>
</li>
<li class="heading1" id="i2" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a2" href="reboottypes.htm" target="hmcontent"><span class="heading1" id="s2">Reboot and Restart types</span></a>
</li>
<li class="heading1" id="i3" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a3" href="forcetypes.htm" target="hmcontent"><span class="heading1" id="s3">Forcing a reboot</span></a>
</li>
<li class="heading1" id="i4" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a4" href="commandline.htm" target="hmcontent"><span class="heading1" id="s4">Command line options</span></a>
</li>
<li class="heading1" id="i5" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a5" href="autorun.htm" target="hmcontent"><span class="heading1" id="s5">Auto-run applications</span></a>
</li>
<li class="heading1" id="i6" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a6" href="autologin.htm" target="hmcontent"><span class="heading1" id="s6">Auto-login to Windows</span></a>
</li>
<li class="heading1" id="i7" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a7" href="cycle.htm" target="hmcontent"><span class="heading1" id="s7">Reboot cycling and looping</span></a>
</li>
<li class="heading1" id="i8" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a8" href="whats_new.htm" target="hmcontent"><span class="heading1" id="s8">What's new</span></a>
</li>
<li class="heading1" id="i9" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a9" href="faq.htm" target="hmcontent"><span class="heading1" id="s9">Problems and FAQ</span></a>
</li>
<li class="heading1" id="i10" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a10" href="systemreq.htm" target="hmcontent"><span class="heading1" id="s10">System requirements</span></a>
</li>
<li class="heading1" id="i11" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a11" href="contacts.htm" target="hmcontent"><span class="heading1" id="s11">Contacting PassMark Software</span></a>
</li>
<li class="heading1" id="i12" style="background:url(cicon9.png) no-repeat;padding-left:20px" onclick="return clicked(this,event)"><a class="heading1" id="a12" href="copyright.htm" target="hmcontent"><span class="heading1" id="s12">Copyright and License</span></a>
</li>
</ul>
<script type="text/javascript">loadicons('cicon9.png');</script>


</body>
</html>

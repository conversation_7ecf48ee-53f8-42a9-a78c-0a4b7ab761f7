#pragma once
#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <string>

#pragma comment(lib, "ws2_32.lib")

class TCPCommunity
{
public:
    TCPCommunity();
    ~TCPCommunity();

    // ?????TCP????
    BOOL InitConnection(const char* _pServerIP, UINT _nPort, UINT _nTimeoutMs = 5000);
    
    // ???????
    void CloseConnection();
    
    // ?????????
    bool IsConnected();
    
    // ????????????????6???????
    DWORD SendData(const char* _pData, int _nDataLen);
    
    // ????????????????6???????
    int ReceiveData(char* _pBuffer, int _nBufferSize, UINT _nTimeoutMs = 3000);
    
    // ???????????
    DWORD GetLastErrorCode();

private:
    SOCKET m_socket;
    bool m_bConnected;
    DWORD m_dwLastError;
    std::string m_serverIP;
    UINT m_nPort;
    
    // ??????????
    DWORD SendRawData(const char* _pData, int _nDataLen);
    
    // ??????????
    int ReceiveRawData(char* _pBuffer, int _nBufferSize, UINT _nTimeoutMs);
    
    // ?????Winsock
    BOOL InitWinsock();
    
    // ????Winsock
    void CleanupWinsock();
};
﻿Build started 2025/7/23 17:33:05.
     1>Project "D:\NewWorkSpace\9-AterPlan11\UtilityCompanion\UtilityCompanion.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.unsuccessfulbuild".
       ClCompile:
         All outputs are up-to-date.
         All outputs are up-to-date.
       ResourceCompile:
         All outputs are up-to-date.
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\NewWorkSpace\9-AterPlan11\bin\UtilityCompanion.dll" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\NewWorkSpace\9-AterPlan11\Lib\\" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win32" /DEF:".\UtilityCompanion.def" /MANIFEST /ManifestFile:"D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\UtilityCompanion.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\NewWorkSpace\9-AterPlan11\Lib\UtilityCompanion.lib" /MACHINE:X86 /DLL "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.res"
         "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\commonFunction.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\IUtilityCompanion.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\PrivateCmdCommunication.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\stdafx.obj"
         "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.obj"
            Creating library D:\NewWorkSpace\9-AterPlan11\Lib\UtilityCompanion.lib and object D:\NewWorkSpace\9-AterPlan11\Lib\UtilityCompanion.exp
         Generating code
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\nvmedeviceio.cpp(438): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\nvmedeviceio.cpp(363): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\nvmedeviceio.cpp(121): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\nvmedeviceio.cpp(101): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\basedeviceio\deviceio.cpp(91): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\basedeviceio\deviceio.cpp(23): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\nvmedeviceio.cpp(53): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
     1>d:\newworkspace\9-aterplan11\allnvmedeviceio\nvmedeviceio\nvmedeviceio.cpp(65): warning C4748: /GS can not protect parameters and local variables from local buffer overrun because optimizations are disabled in function
         Finished generating code
         UtilityCompanion.vcxproj -> D:\NewWorkSpace\9-AterPlan11\bin\UtilityCompanion.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"D:\NewWorkSpace\9-AterPlan11\bin\UtilityCompanion.dll;#2" /manifest "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.dll.intermediate.manifest" "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         Deleting file "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.unsuccessfulbuild".
         Touching "D:\NewWorkSpace\9-AterPlan11\obj\UtilityCompanion\ReleaseTW\UtilityCompanion.lastbuildstate".
     1>Done Building Project "D:\NewWorkSpace\9-AterPlan11\UtilityCompanion\UtilityCompanion.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:03.25

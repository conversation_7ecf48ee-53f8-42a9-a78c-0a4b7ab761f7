#pragma once
#include <vector>
#include <PublicWin32/INI/IIniFile.h>
#include <CFGMacro.h>
#include <PublicWin32/FileOp/FileOp.h>
#include <PublicWin32/FileOp/FileOpEx.h>
#include <regex>
using std::vector;
using std::string;

class MainDlg;
class CServerReport
{
public:
	CServerReport(void);
	~CServerReport(void);
	void Init(MainDlg* _pTestMainDlg);
	Bool CopyTestReport(CString _strProduct, CString _strTestNo, CString _strSrcPath);
	Bool CollectTestData(CString _strProduct, CString _strTestNo, CString _strFilePath, CTime& _startTime, CString _strTool, CString _strItemName = "");
	void CopyTestDataFile(CString _strSrcDir, CString _strDesDir, CTime& _startTime, Bool _b1KFile = False);
	string GetReportPath();
private:
	void CopyMpDataDir(CString _strSrcDir, CString _strDesDir, CTime& _startTime, std::regex& _regPattern, int& _nLogDirCnt);	
	
	string GetServerDataPath();
	string FindRegFileFromDir(string _strDir, std::regex _regPattern);

private:
	MainDlg* m_pTestMainDlg;
};

<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>593094d9-d780-4db7-b6e8-4c058642b7ac</ProjectGuid>
    <ProjectHome>
    </ProjectHome>
    <StartupFile>U3Report.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>U3Report</Name>
    <RootNamespace>U2Report</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="DetailAndConclusion.py" />
    <Compile Include="ErrDiskInfo.py" />
    <Compile Include="Plan1.py" />
    <Compile Include="Plan14.py" />
    <Compile Include="Plan2.py" />
    <Compile Include="Plan4.py" />
    <Compile Include="Plan5.py" />
    <Compile Include="Plan6.py" />
    <Compile Include="Plan33.py" />
    <Compile Include="Plan34.py" />
    <Compile Include="Plan35.py" />
    <Compile Include="PublicFuc.py" />
    <Compile Include="Summary.py" />
    <Compile Include="U3Report.py" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="env\">
      <Id>env</Id>
      <Version>3.7</Version>
      <Description>env (Python 3.7 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
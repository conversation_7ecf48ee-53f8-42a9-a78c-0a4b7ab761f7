from cProfile import label
from sqlite3 import Row
import matplotlib.pyplot as plt
import numpy as np
import traceback,os,tempfile
import csv,math,time,uuid
import pandas as pd
from openpyxl.drawing.image import Image
from collections import namedtuple
from openpyxl.utils import get_column_letter

import PublicFuc
from datetime import datetime,timedelta

def Run(curpath, workBook, alignment):
    ws = workBook['IDLE性能测试']
    ws.alignment = alignment
    ProDirtyDiskIdleRecover(curpath, ws)
    ProIdleRecover(curpath, ws)
    ProCoverWriteIdle(curpath, ws)
    #PublicFuc.WriteReportTime(ws,'K',2)
    #PublicFuc.WriteReportOperator(ws,'P',2)

def ProCoverWriteIdle(curpath, worksheet):
    keyLst = ['pc_no','cap']
    colLst = ['C','B','E']
    #全模式随机测试结果处理
    pattern = '.+\\\\Plan90\\\\T-SS-SS-C69\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'CoverWriteIdle'
    startLine = 86

    caseDic = {}
    PublicFuc.ReadMarsIniDataWithIniPath(curpath, pattern, caseDic, caseName, 2)

    #填表格数据
    newDic = PublicFuc.GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
    
    #填绘图数据
    imageLine1 = 88
    for key in newDic:
        root_path = GetDiskLogPath(caseDic[key]['ini_path'])
        csv_path = root_path + '\\' + 'CoverWritePerformance.csv'
        imagePath = DrawCoverWriteIdle_CoverWritePerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'B%d'%imageLine1)
        imageLine1 += 10

def ProIdleRecover(curpath, worksheet):
    keyLst = ['pc_no','cap']
    colLst = ['C','B','E']
    #全模式随机测试结果处理
    pattern = '.+\\\\Plan91\\\\T-SS-SS-C70\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'IdleRecover'
    startLine = 51

    caseDic = {}
    PublicFuc.ReadMarsIniDataWithIniPath(curpath, pattern, caseDic, caseName, 2)

    #填表格数据
    newDic = PublicFuc.GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
    
    #填绘图数据
    imageLine1 = 53
    imageLine2 = 63
    imageLine3 = 73

    for key in newDic:
        root_path = GetDiskLogPath(caseDic[key]['ini_path'])
        root_path = root_path + 'total'
        csv_path = root_path + '\\' + 'totalWriteAfterPerPerformance.csv'
        imagePath = DrawIdleRecover_totalWriteAfterPerPerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 2000
            img.height = 250
            worksheet.add_image(img, 'B%d'%imageLine1)
        imageLine1 += 9

        csv_path = root_path + '\\' + 'totalReadAfterPerPerformance.csv'
        imagePath = DrawIdleRecover_totalReadAfterPerPerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 2000
            img.height = 250
            worksheet.add_image(img, 'B%d'%imageLine2)
        imageLine2 += 9

        csv_path = root_path + '\\' + 'totalPerWritePerformance.csv'
        imagePath = DrawIdleRecover_totalPerWritePerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'B%d'%imageLine3)

        imagePath,trimTime = DrawIdleRecover_totalPerWritePerformance_TrimTime(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'M%d'%imageLine3)
        imageLine3 += 9
        #填写trim最大的时间
        worksheet['%s%d'%('M', startLine)] = trimTime
        startLine += 1      

def GetDiskLogPath(iniPath):
    idx = iniPath.find('report')
    if idx == -1:
        return ''
    rootPath = iniPath[0:idx]
    return rootPath

def ProDirtyDiskIdleRecover(curpath, worksheet):
    keyLst = ['pc_no','cap']
    colLst = ['C','B','E']
    #全模式随机测试结果处理
    pattern = '.+\\\\Plan92\\\\T-SS-SS-C71\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'DirtyDiskIdleRecover'
    startLine = 4

    caseDic = {}
    PublicFuc.ReadMarsIniDataWithIniPath(curpath, pattern, caseDic, caseName, 2)

    #填表格数据
    newDic = PublicFuc.GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
    
    #填绘图数据
    imageLine1 = 6
    imageLine2 = 16
    imageLine3 = 26
    imageLine4 = 36
    picIndex = 0
    for key in newDic:
        root_path = GetDiskLogPath(caseDic[key]['ini_path'])
        root_path = root_path + 'DirtyTotal'
        csv_path = root_path + '\\' + 'DirtyTotalWriteAfterPerPerformance.csv'
        imagePath = DrawDirtyDiskIdleRecover_DirtyTotalWriteAfterPerPerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 2000
            img.height = 250
            worksheet.add_image(img, 'B%d'%imageLine1)
        imageLine1 += 9

        csv_path = root_path + '\\' + 'DirtyTotalReadAfterPerPerformance.csv'
        imagePath = DrawDirtyDiskIdleRecover_DirtyTotalReadAfterPerPerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 2000
            img.height = 250
            worksheet.add_image(img, 'B%d'%imageLine2)
        imageLine2 += 9

        csv_path = root_path + '\\' + 'DirtyTotalDirtyDiskPerformance.csv'
        imagePath = DrawDirtyDiskIdleRecover_DirtyTotalDirtyDiskPerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'B%d'%imageLine3)

        imagePath,trimTime = DrawDirtyDiskIdleRecover_DirtyTotalDirtyDiskPerformance_TrimTime(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'M%d'%imageLine3)
        imageLine3 += 9
        #填写trim最大的时间
        worksheet['%s%d'%('P', startLine)] = trimTime
        startLine += 1      


        csv_path = root_path + '\\' + 'DirtyTotalPerWritePerformance.csv'
        imagePath = DrawDirtyDiskIdleRecover_DirtyTotalPerWritePerformance(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'B%d'%imageLine4)

        imagePath,trimTime = DrawDirtyDiskIdleRecover_DirtyTotalPerWritePerformance_TrimTime(csv_path,key)
        if imagePath != '':
            img = Image(imagePath)
            img.width = 800
            img.height = 400
            worksheet.add_image(img, 'M%d'%imageLine4)
        imageLine4 += 9
        

def DrawDirtyDiskIdleRecover_DirtyTotalPerWritePerformance(filePath,sampleNo):
    try:
        fullData = pd.read_csv(filePath,sep=',',header=0) 

        fig = plt.figure(figsize=(13,5))
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        plt.title('写性能')

        nMaxDataCntPerLine = 0 #最大的折线数据数量
        fMaxDataValuePerLine = 0 #最大的折线数据值

        labels = fullData.iloc[0:1,1:] #获取所有标题行
        labels = np.array(labels).tolist()[0]
        columnCnt = len(labels) - 1 #因为最后多出一列，不作为绘图数据。

        for colIdx in range(0,columnCnt):
            cur_line_label = str(labels[colIdx])   
            data = fullData.iloc[1:,colIdx+1:colIdx+2]
        
            dataCnt = len(data)
            xLst = [x for x in range(dataCnt)]
            plt.plot(xLst,data,label=cur_line_label)

            if dataCnt > nMaxDataCntPerLine:
                nMaxDataCntPerLine = dataCnt
            fMaxDataValue = float(data.max())
            if fMaxDataValue > fMaxDataValuePerLine:
                fMaxDataValuePerLine = fMaxDataValue

        xLim_Min = 0
        xLim_Max = nMaxDataCntPerLine + 10
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/100)*100

        plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 50
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)
        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''

def DrawDirtyDiskIdleRecover_DirtyTotalPerWritePerformance_TrimTime(filePath,sampleNo):
    try:
        trimTime = ''
        fullData = []
        with open(filePath, 'r', errors='ignore') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            fullData = next(csv_reader)

        fig = plt.figure(figsize=(13,5))
        #plt.show()
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        plt.title('trim times(s)')

        data = fullData[1:-1]
        
        dataCnt = len(data)
        xLst = [x for x in range(1,dataCnt+1)]
        data = [float(item) for item in data]

        fMaxDataValuePerLine = max(data)
        fMaxDataValuePerLine = float(fMaxDataValuePerLine)
        trimTime = fMaxDataValuePerLine
        xLim_Min = 0
        xLim_Max = dataCnt+1
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/1)*1

        #plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 0.5
        xLim_Gap = 1
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        plt.xticks(np.arange(0,xLim_Max+xLim_Gap,xLim_Gap))
        plt.plot(xLst,data)
        #ax = plt.gca()
        #ax.xaxis.set_major_locator(plt.MultipleLocator(1))
        #ax.yaxis.set_major_locator(plt.MultipleLocator(0.5))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        #plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        #plt.show()
        plt.close()
        return strPath,trimTime
    except:
        print(traceback.format_exc())
        return '',trimTime


def DrawDirtyDiskIdleRecover_DirtyTotalDirtyDiskPerformance(filePath,sampleNo):
    try:
        fullData = pd.read_csv(filePath,sep=',',header=0) 

        fig = plt.figure(figsize=(13,5))
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        plt.title('10次写满盘')

        nMaxDataCntPerLine = 0 #最大的折线数据数量
        fMaxDataValuePerLine = 0 #最大的折线数据值

        labels = fullData.iloc[0:1,1:] #获取所有标题行
        labels = np.array(labels).tolist()[0]
        columnCnt = len(labels) - 1 #因为最后多出一列，不作为绘图数据。

        for colIdx in range(0,columnCnt):
            cur_line_label = str(labels[colIdx])   
            data = fullData.iloc[1:,colIdx+1:colIdx+2]
        
            dataCnt = len(data)
            xLst = [x for x in range(dataCnt)]
            plt.plot(xLst,data,label=cur_line_label)

            if dataCnt > nMaxDataCntPerLine:
                nMaxDataCntPerLine = dataCnt
            fMaxDataValue = float(data.max())
            if fMaxDataValue > fMaxDataValuePerLine:
                fMaxDataValuePerLine = fMaxDataValue

        xLim_Min = 0
        xLim_Max = nMaxDataCntPerLine + 10
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/100)*100

        plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 50
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''

def DrawDirtyDiskIdleRecover_DirtyTotalDirtyDiskPerformance_TrimTime(filePath,sampleNo):
    try:
        trimTime = ''
        fullData = []
        with open(filePath, 'r', errors='ignore') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            fullData = next(csv_reader)

        fig = plt.figure(figsize=(13,5))
        #plt.show()
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        plt.title('trim times(s)')

        data = fullData[1:-1]
        
        dataCnt = len(data)
        xLst = [x for x in range(1,dataCnt+1)]
        data = [float(item) for item in data]

        fMaxDataValuePerLine = max(data)
        fMaxDataValuePerLine = float(fMaxDataValuePerLine)
        trimTime = fMaxDataValuePerLine
        xLim_Min = 0
        xLim_Max = dataCnt+1
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/1)*1

        #plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 0.5
        xLim_Gap = 1
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        plt.xticks(np.arange(0,xLim_Max+xLim_Gap,xLim_Gap))
        plt.plot(xLst,data)
        #ax = plt.gca()
        #ax.xaxis.set_major_locator(plt.MultipleLocator(1))
        #ax.yaxis.set_major_locator(plt.MultipleLocator(0.5))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        #plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        #plt.show()
        plt.close()
        return strPath,trimTime
    except:
        print(traceback.format_exc())
        return '',trimTime

#绘制多条线在一个图中的公共函数
def DrawMultiLinePicCommon(dataDic,strtitle,plt):
    try:
        plt.title(strtitle,fontsize=30)

        nMaxDataCntPerLine = 0 #最大的折线数据数量
        fMaxDataValuePerLine = 0 #最大的折线数据值

        xLim = dataDic['xLim']
        for colIdx,line in enumerate(dataDic['data_begin_with_label']):
            cur_line_label = line[0]
            data = line[1:]
            data = [float(item) for item in data]

            dataCnt = len(data)
            xLst = [x for x in range(1,dataCnt+1)]
            plt.plot(xLst,data,label=cur_line_label)

            if dataCnt > nMaxDataCntPerLine:
                nMaxDataCntPerLine = dataCnt
            fMaxDataValue = max(data)
            if fMaxDataValue > fMaxDataValuePerLine:
                fMaxDataValuePerLine = fMaxDataValue

        xLim_Min = 0
        xLim_Max = nMaxDataCntPerLine+1
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/100)*100

        plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        ax = plt.gca()
        ax.xaxis.set_major_locator(plt.MultipleLocator(1))
        yLim_Gap = 100
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap),fontsize=25)
        plt.xticks(np.arange(1,xLim_Max,1),xLim,rotation=300,fontsize=25)
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
  
        # Tweak spacing to prevent clipping of tick-labels
        #ax = plt.gca() 
        plt.legend(loc='lower left',fontsize=20)
    except:
        print(traceback.format_exc())
        return ''

def DrawDirtyDiskIdleRecover_DirtyTotalReadAfterPerPerformance(filePath,sampleNo):
    try:
        #第一步要寻找所有要绘制的数据的关键字
        totalDataDic = {}
        with open(filePath, 'r', errors='ignore') as csvfile:
            rowLst = list(csv.reader(csvfile))
            dicKey = ''
            for line in rowLst:
                if len(line) < 2:
                    #非目标行,可能是空行或者其它非法行
                    continue
                if line[0].find('per') != -1 and int(line[1]) != 0:
                    dicKey = line[1]
                    totalDataDic[dicKey] = {}
                    continue
                if dicKey == '':
                    #说明还未开始收集数据
                    continue
                if totalDataDic[dicKey] == {}:
                    #正式开始收集数据,此行为收集标题idle 1M到idle 15M
                    totalDataDic[dicKey]['xLim'] = line[1:-1] #mars生成的结果在尾巴上加了逗号。
                    totalDataDic[dicKey]['data_begin_with_label'] = []
                else:
                    totalDataDic[dicKey]['data_begin_with_label'].append(line[:-1])


        #取完数据开始绘图
        nPicCnt = len(totalDataDic.keys())
        if nPicCnt == 0:
            return ''

        fig = plt.figure(figsize=((13+1)*nPicCnt,5))
        #plt.show()
        #plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        picIdx = 0
        for key in totalDataDic:
            dataDic = totalDataDic[key]
            picIdx += 1
            plt.subplot(1,nPicCnt,picIdx)
            DrawMultiLinePicCommon(dataDic,key+'%',plt)
                                
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        #plt.savefig(strPath)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''


def DrawDirtyDiskIdleRecover_DirtyTotalWriteAfterPerPerformance(filePath,sampleNo):
    try:
        #第一步要寻找所有要绘制的数据的关键字
        totalDataDic = {}
        with open(filePath, 'r', errors='ignore') as csvfile:
            rowLst = list(csv.reader(csvfile))
            dicKey = ''
            for line in rowLst:
                if len(line) < 2:
                    #非目标行,可能是空行或者其它非法行
                    continue
                if line[0].find('per') != -1 and int(line[1]) != 0:
                    dicKey = line[1]
                    totalDataDic[dicKey] = {}
                    continue
                if dicKey == '':
                    #说明还未开始收集数据
                    continue
                if totalDataDic[dicKey] == {}:
                    #正式开始收集数据,此行为收集标题idle 1M到idle 15M
                    totalDataDic[dicKey]['xLim'] = line[1:-1] #mars生成的结果在尾巴上加了逗号。
                    totalDataDic[dicKey]['data_begin_with_label'] = []
                else:
                    totalDataDic[dicKey]['data_begin_with_label'].append(line[:-1])


        #取完数据开始绘图
        nPicCnt = len(totalDataDic.keys())
        if nPicCnt == 0:
            return ''

        fig = plt.figure(figsize=((13+1)*nPicCnt,5),dpi= 200)
        #plt.show()
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        picIdx = 0
        for key in totalDataDic:
            dataDic = totalDataDic[key]
            picIdx += 1
            plt.subplot(1,nPicCnt,picIdx)
            DrawMultiLinePicCommon(dataDic,key+'%',plt)
                                
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        #plt.savefig(strPath)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''

#-------------------------以上完成dirty数据绘制-----------------------------


def DrawIdleRecover_totalPerWritePerformance(filePath,sampleNo):
    try:
        fullData = pd.read_csv(filePath,sep=',',header=0) 

        fig = plt.figure(figsize=(13,5))
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        plt.title('写性能')

        nMaxDataCntPerLine = 0 #最大的折线数据数量
        fMaxDataValuePerLine = 0 #最大的折线数据值

        labels = fullData.iloc[0:1,1:] #获取所有标题行
        labels = np.array(labels).tolist()[0]
        columnCnt = len(labels) - 1 #因为最后多出一列，不作为绘图数据。

        for colIdx in range(0,columnCnt):
            cur_line_label = str(labels[colIdx])   
            data = fullData.iloc[1:,colIdx+1:colIdx+2]
        
            dataCnt = len(data)
            xLst = [x for x in range(dataCnt)]
            plt.plot(xLst,data,label=cur_line_label)

            if dataCnt > nMaxDataCntPerLine:
                nMaxDataCntPerLine = dataCnt
            fMaxDataValue = float(data.max())
            if fMaxDataValue > fMaxDataValuePerLine:
                fMaxDataValuePerLine = fMaxDataValue

        xLim_Min = 0
        xLim_Max = nMaxDataCntPerLine + 10
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/100)*100

        plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 50
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''

def DrawIdleRecover_totalPerWritePerformance_TrimTime(filePath,sampleNo):
    try:
        trimTime = ''
        fullData = []
        with open(filePath, 'r', errors='ignore') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            fullData = next(csv_reader)

        fig = plt.figure(figsize=(13,5))
        #plt.show()
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        plt.title('trim times(s)')

        data = fullData[1:-1]
        
        dataCnt = len(data)
        xLst = [x for x in range(1,dataCnt+1)]
        data = [float(item) for item in data]

        fMaxDataValuePerLine = max(data)
        fMaxDataValuePerLine = float(fMaxDataValuePerLine)
        trimTime = fMaxDataValuePerLine
        xLim_Min = 0
        xLim_Max = dataCnt+1
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/1)*1

        #plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 0.5
        xLim_Gap = 1
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        plt.xticks(np.arange(0,xLim_Max+xLim_Gap,xLim_Gap))
        plt.plot(xLst,data)
        #ax = plt.gca()
        #ax.xaxis.set_major_locator(plt.MultipleLocator(1))
        #ax.yaxis.set_major_locator(plt.MultipleLocator(0.5))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        #plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        #plt.show()
        plt.close()
        return strPath,trimTime
    except:
        print(traceback.format_exc())
        return '',trimTime

def DrawIdleRecover_totalReadAfterPerPerformance(filePath,sampleNo):
    try:
        #第一步要寻找所有要绘制的数据的关键字
        totalDataDic = {}
        with open(filePath, 'r', errors='ignore') as csvfile:
            rowLst = list(csv.reader(csvfile))
            dicKey = ''
            for line in rowLst:
                if len(line) < 2:
                    #非目标行,可能是空行或者其它非法行
                    continue
                if line[0].find('per') != -1 and int(line[1]) != 0:
                    dicKey = line[1]
                    totalDataDic[dicKey] = {}
                    continue
                if dicKey == '':
                    #说明还未开始收集数据
                    continue
                if totalDataDic[dicKey] == {}:
                    #正式开始收集数据,此行为收集标题idle 1M到idle 15M
                    totalDataDic[dicKey]['xLim'] = line[1:-1] #mars生成的结果在尾巴上加了逗号。
                    totalDataDic[dicKey]['data_begin_with_label'] = []
                else:
                    totalDataDic[dicKey]['data_begin_with_label'].append(line[:-1])


        #取完数据开始绘图
        nPicCnt = len(totalDataDic.keys())
        if nPicCnt == 0:
            return ''

        fig = plt.figure(figsize=((13+1)*nPicCnt,5))
        #plt.show()
        #plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        picIdx = 0
        for key in totalDataDic:
            dataDic = totalDataDic[key]
            picIdx += 1
            plt.subplot(1,nPicCnt,picIdx)
            DrawMultiLinePicCommon(dataDic,key+'%',plt)
                                
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        #plt.savefig(strPath)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''


def DrawIdleRecover_totalWriteAfterPerPerformance(filePath,sampleNo):
    try:
        #第一步要寻找所有要绘制的数据的关键字
        totalDataDic = {}
        with open(filePath, 'r', errors='ignore') as csvfile:
            rowLst = list(csv.reader(csvfile))
            dicKey = ''
            for line in rowLst:
                if len(line) < 2:
                    #非目标行,可能是空行或者其它非法行
                    continue
                if line[0].find('per') != -1 and int(line[1]) != 0:
                    dicKey = line[1]
                    totalDataDic[dicKey] = {}
                    continue
                if dicKey == '':
                    #说明还未开始收集数据
                    continue
                if totalDataDic[dicKey] == {}:
                    #正式开始收集数据,此行为收集标题idle 1M到idle 15M
                    totalDataDic[dicKey]['xLim'] = line[1:-1] #mars生成的结果在尾巴上加了逗号。
                    totalDataDic[dicKey]['data_begin_with_label'] = []
                else:
                    totalDataDic[dicKey]['data_begin_with_label'].append(line[:-1])


        #取完数据开始绘图
        nPicCnt = len(totalDataDic.keys())
        if nPicCnt == 0:
            return ''

        fig = plt.figure(figsize=((13+1)*nPicCnt,5))
        #plt.show()
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        picIdx = 0
        for key in totalDataDic:
            dataDic = totalDataDic[key]
            picIdx += 1
            plt.subplot(1,nPicCnt,picIdx)
            DrawMultiLinePicCommon(dataDic,key+'%',plt)
                                
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        #plt.savefig(strPath)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''

#-------------------------以上完成IdleRecover数据绘制----------------------------

def DrawCoverWriteIdle_CoverWritePerformance(filePath,sampleNo):
    try:
        fullData = pd.read_csv(filePath,sep=',',header=0) 

        fig = plt.figure(figsize=(13,5))
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 显示汉字
        #plt.title('写性能')

        nMaxDataCntPerLine = 0 #最大的折线数据数量
        fMaxDataValuePerLine = 0 #最大的折线数据值

        labels = fullData.columns
        labels = list(labels)
        label = labels[1]

        cur_line_label = str(label)  
        plt.title(cur_line_label) 
        data = fullData.iloc[:,1:2]
    
        dataCnt = len(data)
        xLst = [x for x in range(dataCnt)]
        #plt.show()
        plt.plot(xLst,data,label=cur_line_label)#这里也可以直接绘制，不需要XLST，但是为了和其它地方一致性。

        
        nMaxDataCntPerLine = dataCnt
        fMaxDataValue = float(data.max())
        fMaxDataValuePerLine = fMaxDataValue

        xLim_Min = 0
        xLim_Max = nMaxDataCntPerLine + 10
        yLim_Min = 0
        yLim_Max = math.ceil(fMaxDataValuePerLine/100)*100

        plt.xlim(xLim_Min, xLim_Max)
        plt.ylim(yLim_Min,yLim_Max)
        
        yLim_Gap = 50
        plt.yticks(np.arange(0,yLim_Max+yLim_Gap,yLim_Gap))
        #ax = plt.gca() 
        #ax.xaxis.set_major_locator(plt.MultipleLocator(1000))
        plt.grid(axis='y') # 设置 y 就在轴方向显示网格线
        #ax = plt.gca() 
        #plt.legend()
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)

        str_uid = uuid.uuid4().hex
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_%s_idle_tmp.png'%(sampleNo,str_uid))
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()
        return strPath
    except:
        print(traceback.format_exc())
        return ''

#-------------------------以上完成CoverWriteIdle数据绘制----------------------------
import PublicFuc

def Run(curpath, workBook, alignment):
    ws = workBook['Reboot&Sleeper后_性能测试']
    ws.alignment = alignment
    ProCdm(curpath, ws)  
    ProAssd(curpath, ws)
    PublicFuc.WriteReportTime(ws,'H',2)
    PublicFuc.WriteReportOperator(ws,'M',2)
  
def ProCdm(curpath, worksheet):
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    cdmCol = ['C','B','E','P','G','H','I','J','K','L','M','N']
    #reboot
    cdmDic = {}
    pattern = '.+\\\\Plan32\\\\T-SS-SS-B01\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    pattern = '.+\\\\Plan4\\\\T-SS-SS-B01\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 15
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)
    #sleep
    cdmDic = {}
    pattern = '.+\\\\Plan33\\\\T-SS-SS-B03\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    pattern = '.+\\\\Plan5\\\\T-SS-SS-B03\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 65
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAssd(curpath, worksheet):   
    assdKey = ['pc_no', 'Cap', 'qa_err_msg','Seq Read','Seq Write','4K Read','4K Write','4K 64Thrd Read','4K 64Thrd Write','Read Acc Time','Write Acc Time','Read Score','Write Score','Total Score']
    assdCol = ['C','B','E','P','G','H','I','J','K','L','M','N','O','P','Q']
    assdDic = {}
    pattern = '.+\\\\Plan32\\\\T-SS-SS-B02\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    pattern = '.+\\\\Plan4\\\\T-SS-SS-B02\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 40
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)

    assdDic = {}
    pattern = '.+\\\\Plan33\\\\T-SS-SS-B04\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    pattern = '.+\\\\Plan5\\\\T-SS-SS-B04\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 90
    PublicFuc.WriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)
    